#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
1688商品采集工具
"""

import re
import sys
import os
from typing import Optional, List
from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtWidgets import QTableWidgetItem

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 统一的路径获取函数
def get_application_directory():
    """获取应用程序所在目录（exe文件所在目录）"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        return os.path.dirname(sys.executable)
    else:
        # 开发环境
        return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def get_config_file_path(relative_path):
    """获取配置文件的绝对路径"""
    app_dir = get_application_directory()
    return os.path.join(app_dir, relative_path)

# 全局缓存变量，避免重复解析类目树
_category_id_to_name_cache = None
_cache_initialized = False

# 🔧 新增：类目映射缓存，避免重复读取文件
_category_mapping_cache = None
_mapping_cache_initialized = False

def _initialize_category_cache():
    """初始化类目ID到名称的缓存映射（优先使用缓存文件）"""
    global _category_id_to_name_cache, _cache_initialized

    if _cache_initialized:
        return _category_id_to_name_cache

    try:
        import json
        import os

        # 🔧 修复：使用统一的路径获取函数
        # 🔧 优化：优先尝试读取扁平映射缓存文件
        cache_file = get_config_file_path(os.path.join("config", "类目ID映射.json"))
        tree_file = get_config_file_path(os.path.join("config", "类目树.json"))

        # 检查缓存文件是否存在且比类目树文件新
        cache_exists = os.path.exists(cache_file)
        tree_exists = os.path.exists(tree_file)

        if cache_exists and tree_exists:
            cache_mtime = os.path.getmtime(cache_file)
            tree_mtime = os.path.getmtime(tree_file)

            if cache_mtime >= tree_mtime:
                # 缓存文件存在且比类目树新，直接读取
                print(f"[类目缓存] 📁 读取扁平映射缓存文件...")
                with open(cache_file, 'r', encoding='utf-8') as f:
                    id_to_name = json.load(f)

                _category_id_to_name_cache = id_to_name
                _cache_initialized = True

                print(f"[类目缓存] ✅ 从缓存文件读取完成，共 {len(id_to_name)} 个类目映射")
                return _category_id_to_name_cache

        # 缓存文件不存在或过期，需要重新解析类目树
        print(f"[类目缓存] 🔄 缓存文件不存在或过期，重新解析类目树...")

        if not tree_exists:
            print(f"[类目缓存] ❌ 类目树文件不存在: {tree_file}")
            _cache_initialized = True
            return None

        with open(tree_file, 'r', encoding='utf-8') as f:
            tree_data = json.load(f)

        # 构建类目ID到名称的映射
        id_to_name = {}

        def extract_categories(data):
            """递归提取所有类目ID和名称"""
            if isinstance(data, dict):
                # 🔧 修复：检查是否是类目节点（使用正确的字段名）
                if 'categoryId' in data and 'categoryName' in data:
                    # 提取类目名称（直接使用，无需清理emoji）
                    category_id = data['categoryId']
                    category_name = data['categoryName']

                    # 直接使用类目名称，无需复杂的清理逻辑
                    id_to_name[category_id] = category_name.strip()

                    # 递归处理子类目
                    if 'children' in data and isinstance(data['children'], dict):
                        # 🔧 修复：children是字典，不是列表
                        for child_key, child_data in data['children'].items():
                            extract_categories(child_data)
                else:
                    # 处理字典的其他情况
                    for key, value in data.items():
                        if isinstance(value, dict):
                            # 递归处理字典值
                            extract_categories(value)
            elif isinstance(data, list):
                for item in data:
                    extract_categories(item)

        # 提取所有类目
        extract_categories(tree_data)

        # 🔧 优化：保存扁平映射到缓存文件
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(id_to_name, f, ensure_ascii=False, indent=2)
            print(f"[类目缓存] 💾 已保存扁平映射缓存文件: {cache_file}")
        except Exception as e:
            print(f"[类目缓存] ⚠️ 保存缓存文件失败: {e}")

        _category_id_to_name_cache = id_to_name
        _cache_initialized = True

        print(f"[类目缓存] ✅ 解析完成，共缓存 {len(id_to_name)} 个类目映射")
        return _category_id_to_name_cache

    except Exception as e:
        print(f"[类目缓存] ❌ 初始化缓存失败: {e}")
        _cache_initialized = True
        return None

def build_category_path_from_tree(category_ids_str):
    """
    从类目树.json文件中根据类目ID路径构建完整的类目路径（使用缓存优化）

    Args:
        category_ids_str: 类目ID路径字符串，如 "1582,9012,1591"

    Returns:
        dict: 包含完整路径的字典，如：
        {
            'path_with_arrow': '服饰内衣 > 女装 > 休闲裤',
            'path_with_comma': '服饰内衣,女装,休闲裤'
        }
        如果构建失败返回None
    """
    try:
        if not category_ids_str:
            return None

        # 解析类目ID路径
        category_ids = [int(id_str.strip()) for id_str in category_ids_str.split(',') if id_str.strip()]
        if not category_ids:
            return None

        # 🔧 优化：使用缓存的类目ID到名称映射，避免重复解析
        id_to_name = _initialize_category_cache()
        if not id_to_name:
            print(f"[类目路径构建] ❌ 类目缓存未初始化")
            return None

        # 构建路径
        path_names = []
        for cat_id in category_ids:
            if cat_id in id_to_name:
                path_names.append(id_to_name[cat_id])
            else:
                print(f"[类目路径构建] ⚠️ 未找到类目ID {cat_id} 对应的名称")
                return None

        if not path_names:
            return None

        # 返回两种格式的路径
        return {
            'path_with_arrow': ' > '.join(path_names),
            'path_with_comma': ','.join(path_names)
        }

    except Exception as e:
        print(f"[类目路径构建] 构建路径失败: {str(e)}")
        return None


def update_product_cache_subject(product_id, filtered_title):
    """
    更新商品缓存文件中的subject字段

    Args:
        product_id: 商品ID
        filtered_title: 过滤后的标题
    """
    try:
        import os
        import json

        # 获取缓存文件路径
        cache_dir = get_config_file_path(os.path.join("config", "pdcache"))
        cache_file = os.path.join(cache_dir, f"{product_id}.json")

        if not os.path.exists(cache_file):
            print(f"[缓存更新] 缓存文件不存在: {cache_file}")
            return False

        # 读取缓存文件
        with open(cache_file, 'r', encoding='utf-8') as f:
            cache_data = json.load(f)

        # 更新subject字段
        if 'product_info' in cache_data and 'subject' in cache_data['product_info']:
            old_subject = cache_data['product_info']['subject']
            cache_data['product_info']['subject'] = filtered_title

            # 同时更新raw_data中的subject（如果存在）
            if 'raw_data' in cache_data and 'productInfo' in cache_data['raw_data']:
                cache_data['raw_data']['productInfo']['subject'] = filtered_title

            # 保存更新后的缓存文件
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)

            print(f"[缓存更新] 商品 {product_id} 标题已更新")
            print(f"[缓存更新] 原标题: {old_subject}")
            print(f"[缓存更新] 新标题: {filtered_title}")
            return True
        else:
            print(f"[缓存更新] 缓存文件格式不正确: {cache_file}")
            return False

    except Exception as e:
        print(f"[缓存更新] 更新商品 {product_id} 缓存失败: {e}")
        return False


def apply_title_filter(original_title, parent_window):
    """
    应用搬家设置中的标题过滤关键词，删除指定的关键词

    Args:
        original_title: 原始标题
        parent_window: 主窗口对象，用于获取标题过滤配置

    Returns:
        过滤后的标题
    """
    try:
        # 检查标题过滤复选框是否勾选
        if not is_title_filter_enabled(parent_window):
            print(f"[标题过滤] 标题过滤复选框未勾选，返回原标题")
            return original_title

        # 获取标题过滤关键词
        filter_keywords = get_title_filter_keywords(parent_window)

        if not filter_keywords:
            print(f"[标题过滤] 没有找到过滤关键词，返回原标题")
            return original_title

        filtered_title = original_title
        removed_keywords = []

        # 应用过滤关键词
        for keyword in filter_keywords:
            if keyword in filtered_title:
                filtered_title = filtered_title.replace(keyword, "")
                removed_keywords.append(keyword)

        # 清理多余的空格
        filtered_title = re.sub(r'\s+', ' ', filtered_title).strip()

        if removed_keywords:
            print(f"[标题过滤] 原标题: {original_title}")
            print(f"[标题过滤] 删除关键词: {removed_keywords}")
            print(f"[标题过滤] 过滤后: {filtered_title}")

        return filtered_title

    except Exception as e:
        print(f"[标题过滤] 应用标题过滤时出错: {e}")
        return original_title


def is_title_filter_enabled(parent_window):
    """
    检查标题过滤复选框是否勾选

    Args:
        parent_window: 主窗口对象

    Returns:
        bool: 是否启用标题过滤
    """
    try:
        # 检查是否有标题过滤复选框
        if not hasattr(parent_window, 'title_filter_checkbox'):
            print(f"[标题过滤] 主窗口没有title_filter_checkbox属性")
            return False

        # 检查复选框是否勾选
        is_checked = parent_window.title_filter_checkbox.isChecked()
        print(f"[标题过滤] 标题过滤复选框状态: {is_checked}")

        return is_checked

    except Exception as e:
        print(f"[标题过滤] 检查复选框状态时出错: {e}")
        return False


def get_title_filter_keywords(parent_window):
    """
    从搬家设置中获取标题过滤关键词列表

    Args:
        parent_window: 主窗口对象

    Returns:
        关键词列表
    """
    try:
        # 检查是否有标题过滤文本框
        if not hasattr(parent_window, 'title_filter_text'):
            print(f"[标题过滤] 主窗口没有title_filter_text属性")
            return []

        # 获取标题过滤文本
        filter_text = parent_window.title_filter_text.toPlainText().strip()

        if not filter_text:
            print(f"[标题过滤] 标题过滤文本为空")
            return []

        print(f"[标题过滤] 获取到过滤文本: {filter_text[:100]}...")

        # 解析关键词
        keywords = []

        # 处理方括号格式的关键词 [关键词1][关键词2]
        bracket_pattern = r'\[([^\]]+)\]'
        bracket_matches = re.findall(bracket_pattern, filter_text)
        keywords.extend(bracket_matches)

        # 处理逗号分隔的关键词
        # 移除方括号部分后，按逗号分割
        text_without_brackets = re.sub(bracket_pattern, '', filter_text)
        comma_keywords = [kw.strip() for kw in text_without_brackets.split(',') if kw.strip()]
        keywords.extend(comma_keywords)

        # 去重并过滤空值
        keywords = list(set([kw.strip() for kw in keywords if kw.strip()]))

        print(f"[标题过滤] 解析到 {len(keywords)} 个关键词: {keywords[:10]}...")

        return keywords

    except Exception as e:
        print(f"[标题过滤] 获取标题过滤关键词时出错: {e}")
        return []


def apply_title_replace(filtered_title, parent_window):
    """
    应用搬家设置中的标题替换关键字，将原关键字替换为新关键字

    Args:
        filtered_title: 已过滤的标题
        parent_window: 父窗口对象，用于获取标题替换配置

    Returns:
        str: 替换后的标题
    """
    try:
        # 检查标题替换复选框是否勾选
        if not is_title_replace_enabled(parent_window):
            print(f"[标题替换] 标题替换复选框未勾选，返回原标题")
            return filtered_title

        # 获取标题替换关键字对
        replace_pairs = get_title_replace_keywords(parent_window)

        if not replace_pairs:
            print(f"[标题替换] 没有找到替换关键字对，返回原标题")
            return filtered_title

        replaced_title = filtered_title
        replaced_keywords = []

        # 应用替换关键字对
        for original_keyword, new_keyword in replace_pairs:
            if original_keyword in replaced_title:
                replaced_title = replaced_title.replace(original_keyword, new_keyword)
                replaced_keywords.append(f"'{original_keyword}' → '{new_keyword}'")

        if replaced_keywords:
            print(f"[标题替换] 原标题: {filtered_title}")
            print(f"[标题替换] 替换关键字: {replaced_keywords}")
            print(f"[标题替换] 替换后: {replaced_title}")

        return replaced_title

    except Exception as e:
        print(f"[标题替换] 应用标题替换时出错: {e}")
        return filtered_title


def is_title_replace_enabled(parent_window):
    """
    检查标题替换复选框是否勾选

    Args:
        parent_window: 主窗口对象

    Returns:
        bool: 是否启用标题替换
    """
    try:
        # 检查是否有标题替换复选框
        if not hasattr(parent_window, 'replace_char_checkbox'):
            print(f"[标题替换] 主窗口没有replace_char_checkbox属性")
            return False

        # 检查复选框是否勾选
        is_checked = parent_window.replace_char_checkbox.isChecked()
        print(f"[标题替换] 标题替换复选框状态: {is_checked}")

        return is_checked

    except Exception as e:
        print(f"[标题替换] 检查复选框状态时出错: {e}")
        return False


def get_title_replace_keywords(parent_window):
    """
    从搬家设置中获取标题替换关键字对列表

    Args:
        parent_window: 主窗口对象

    Returns:
        list: 关键字对列表 [(原关键字, 新关键字), ...]
    """
    try:
        # 检查是否有替换关键字表格
        if not hasattr(parent_window, 'replace_table'):
            print(f"[标题替换] 主窗口没有replace_table属性")
            return []

        replace_table = parent_window.replace_table
        replace_pairs = []

        # 遍历表格获取替换关键字对
        for row in range(replace_table.rowCount()):
            # 获取原关键字（第1列）
            original_item = replace_table.item(row, 1)
            # 获取新关键字（第2列）
            new_item = replace_table.item(row, 2)

            if original_item and new_item:
                original_keyword = original_item.text().strip()
                new_keyword = new_item.text().strip()

                if original_keyword and new_keyword:
                    replace_pairs.append((original_keyword, new_keyword))

        print(f"[标题替换] 获取到 {len(replace_pairs)} 对替换关键字: {replace_pairs[:5]}...")

        return replace_pairs

    except Exception as e:
        print(f"[标题替换] 获取标题替换关键字时出错: {e}")
        return []


def apply_title_prefix(filtered_title, parent_window):
    """
    应用搬家设置中的标题前缀

    Args:
        filtered_title: 已过滤的标题
        parent_window: 父窗口对象，用于获取标题前缀设置

    Returns:
        str: 添加前缀后的标题
    """
    try:
        if not parent_window:
            return filtered_title

        # 获取标题前缀输入框
        title_prefix_input = getattr(parent_window, 'title_prefix_input', None)
        if not title_prefix_input:
            return filtered_title

        title_prefix = title_prefix_input.text().strip()
        if not title_prefix:
            return filtered_title

        # 添加前缀
        final_title = title_prefix + filtered_title
        print(f"[标题前缀] 原标题: {filtered_title}")
        print(f"[标题前缀] 前缀: {title_prefix}")
        print(f"[标题前缀] 最终标题: {final_title}")

        return final_title

    except Exception as e:
        print(f"[标题前缀] 应用标题前缀时出错: {e}")
        return filtered_title


def process_sku_filter_sync(result, product_id, parent_window):
    """
    同步处理SKU过滤的函数 - 删除包含关键词的SKU文本

    Args:
        result: 商品数据
        product_id: 商品ID
        parent_window: 父窗口对象，用于获取过滤设置
    """
    try:
        # 检查是否启用SKU过滤
        if not parent_window or not hasattr(parent_window, 'sku_filter_checkbox'):
            return

        if not parent_window.sku_filter_checkbox.isChecked():
            return

        # 获取过滤关键词
        if not hasattr(parent_window, 'sku_filter_text'):
            return

        filter_text = parent_window.sku_filter_text.toPlainText().strip()
        if not filter_text:
            print(f"[SKU过滤] 商品 {product_id} 未设置过滤关键词，跳过过滤")
            return

        # 按逗号分割关键词，并去除空白
        filter_keywords = [keyword.strip() for keyword in filter_text.split(',') if keyword.strip()]
        if not filter_keywords:
            return

        print(f"[SKU过滤] 开始处理商品 {product_id} 的SKU过滤，关键词数量: {len(filter_keywords)}")

        # 处理 product_info 中的SKU数据
        if 'product_info' in result:
            product_info = result['product_info']

            # 处理 product_info.product_sku_infos
            if 'product_sku_infos' in product_info:
                for sku_index, sku in enumerate(product_info['product_sku_infos']):
                    filter_sku_text_fields_sync(sku, filter_keywords, f"product_info.product_sku_infos[{sku_index}]")

            # 处理 product_info.processed_sku_infos
            if 'processed_sku_infos' in product_info:
                for sku_index, sku in enumerate(product_info['processed_sku_infos']):
                    filter_sku_text_fields_sync(sku, filter_keywords, f"product_info.processed_sku_infos[{sku_index}]")

        # 处理根级别的SKU数据
        if 'product_sku_infos' in result:
            for sku_index, sku in enumerate(result['product_sku_infos']):
                filter_sku_text_fields_sync(sku, filter_keywords, f"product_sku_infos[{sku_index}]")

        if 'processed_sku_infos' in result:
            for sku_index, sku in enumerate(result['processed_sku_infos']):
                filter_sku_text_fields_sync(sku, filter_keywords, f"processed_sku_infos[{sku_index}]")

        # 处理 raw_data 中的SKU数据
        if 'raw_data' in result and 'productSkuInfos' in result['raw_data']:
            for sku_index, sku in enumerate(result['raw_data']['productSkuInfos']):
                filter_sku_text_fields_sync(sku, filter_keywords, f"raw_data.productSkuInfos[{sku_index}]")

        print(f"[SKU过滤] 商品 {product_id} SKU过滤处理完成")

    except Exception as e:
        print(f"[SKU过滤] 处理商品 {product_id} SKU过滤时出错: {e}")


def filter_sku_text_fields_sync(sku, filter_keywords, sku_path):
    """过滤SKU文本字段中的关键词（同步版本）"""
    try:
        filtered_count = 0

        # 过滤货号字段
        cargo_number_fields = ['cargoNumber', 'cargo_number']
        for field in cargo_number_fields:
            if field in sku and sku[field]:
                original_value = str(sku[field])
                filtered_value = remove_keywords_from_text_sync(original_value, filter_keywords)
                if filtered_value != original_value:
                    sku[field] = filtered_value
                    print(f"[SKU过滤] {sku_path}.{field}: '{original_value}' → '{filtered_value}'")
                    filtered_count += 1

        # 过滤属性字段
        if 'attributes' in sku and isinstance(sku['attributes'], list):
            for attr_index, attr in enumerate(sku['attributes']):
                # 过滤属性值
                value_fields = ['attributeValue', 'attribute_value']
                for field in value_fields:
                    if field in attr and attr[field]:
                        original_value = str(attr[field])
                        filtered_value = remove_keywords_from_text_sync(original_value, filter_keywords)
                        if filtered_value != original_value:
                            attr[field] = filtered_value
                            print(f"[SKU过滤] {sku_path}.attributes[{attr_index}].{field}: '{original_value}' → '{filtered_value}'")
                            filtered_count += 1

        if filtered_count > 0:
            print(f"[SKU过滤] {sku_path} 共过滤了 {filtered_count} 个字段")

    except Exception as e:
        print(f"[SKU过滤] 过滤SKU字段时出错: {e}")


def remove_keywords_from_text_sync(text, keywords):
    """从文本中删除关键词（同步版本）"""
    try:
        if not text or not keywords:
            return text

        filtered_text = text
        for keyword in keywords:
            if keyword in filtered_text:
                filtered_text = filtered_text.replace(keyword, '')

        # 清理多余的空格和标点符号
        filtered_text = filtered_text.strip()
        # 清理连续的空格
        import re
        filtered_text = re.sub(r'\s+', ' ', filtered_text)

        return filtered_text

    except Exception as e:
        print(f"[SKU过滤] 删除关键词时出错: {e}")
        return text


def apply_price_calculation(original_price, parent_window):
    """
    应用价格计算设置

    Args:
        original_price: 原始价格（可能是单价或价格区间）
        parent_window: 父窗口对象，用于获取价格设置

    Returns:
        str: 计算后的价格
    """
    try:
        if not parent_window or not original_price:
            return original_price

        # 获取价格设置控件
        multiply_input = getattr(parent_window, 'multiply_input', None)
        plus_input = getattr(parent_window, 'plus_input', None)
        price_value_input = getattr(parent_window, 'price_value_input', None)
        decimal_spinbox = getattr(parent_window, 'decimal_spinbox', None)
        ones_spinbox = getattr(parent_window, 'ones_spinbox', None)

        if not all([multiply_input, plus_input, price_value_input, decimal_spinbox, ones_spinbox]):
            return original_price

        # 获取价格设置参数
        multiply = float(multiply_input.text() or "1")
        plus_value = float(plus_input.text() or "0")
        price_multiplier = float(price_value_input.text() or "1")
        decimal_digit = decimal_spinbox.value()
        ones_digit = ones_spinbox.value()



        # 处理价格区间
        if '~' in original_price:
            # 价格区间处理
            prices = original_price.split('~')
            if len(prices) == 2:
                min_price = float(prices[0].strip())
                max_price = float(prices[1].strip())

                # 分别计算最低价和最高价
                new_min_price = (min_price * multiply + plus_value) * price_multiplier
                new_max_price = (max_price * multiply + plus_value) * price_multiplier

                # 格式化价格
                formatted_min = format_price(new_min_price, ones_digit, decimal_digit)
                formatted_max = format_price(new_max_price, ones_digit, decimal_digit)

                result = f"{formatted_min}~{formatted_max}"

                return result
        else:
            # 单一价格处理
            price = float(original_price)
            new_price = (price * multiply + plus_value) * price_multiplier

            # 格式化价格
            result = format_price(new_price, ones_digit, decimal_digit)

            return result

    except Exception as e:
        print(f"[价格计算] 计算价格时出错: {e}")
        return original_price


def format_price(price, ones_digit, decimal_digit):
    """格式化价格，设置个位数和小数位，最终保留两位小数"""
    try:
        # 特殊处理：当小数位和个位数都为0时，保持原来的小数位数但确保两位小数
        if ones_digit == 0 and decimal_digit == 0:
            # 保持原来的小数位数，但格式化为两位小数
            return f"{price:.2f}"

        # 先保留1位小数
        price_str = f"{price:.1f}"

        # 分离整数部分和小数部分
        parts = price_str.split('.')
        integer_part = int(parts[0])
        original_decimal = parts[1] if len(parts) > 1 else "0"

        # 修改个位数（如果个位数不为0）
        if ones_digit != 0:
            if integer_part >= 10:
                # 保留十位数以上，只修改个位数
                tens_and_above = integer_part // 10
                new_integer = tens_and_above * 10 + ones_digit
            else:
                # 如果小于10，直接设置为个位数
                new_integer = ones_digit
        else:
            # 个位数为0时，保持原来的整数部分
            new_integer = integer_part

        # 修改小数位（如果小数位不为0）
        if decimal_digit != 0:
            final_decimal = str(decimal_digit)
        else:
            # 小数位为0时，保持原来的小数部分
            final_decimal = original_decimal

        # 组合最终价格，确保两位小数
        temp_result = f"{new_integer}.{final_decimal}"

        # 转换为浮点数再格式化为两位小数，确保格式统一
        try:
            final_price = float(temp_result)
            result = f"{final_price:.2f}"
        except:
            # 如果转换失败，直接格式化原始价格
            result = f"{price:.2f}"

        #print(f"[价格格式化] {price:.2f} → {result} (个位数={ones_digit}, 小数={decimal_digit})")
        return result

    except Exception as e:
        print(f"[价格格式化] 格式化价格时出错: {e}")
        return f"{price:.2f}"


def refresh_all_titles_with_cache_update(table_widget, parent_window):
    """
    刷新所有商品标题并更新缓存

    Args:
        table_widget: 商品表格控件
        parent_window: 主窗口对象
    """
    try:
        print(f"[标题过滤] 开始实时刷新所有商品标题并更新缓存...")

        if not table_widget:
            print(f"[标题过滤] 表格控件为空，无法刷新")
            return

        row_count = table_widget.rowCount()
        if row_count == 0:
            print(f"[标题过滤] 表格为空，无需刷新")
            return

        updated_count = 0
        cache_updated_count = 0

        # 遍历所有行
        for row in range(row_count):
            try:
                # 获取商品ID（第1列）
                product_id_item = table_widget.item(row, 1)
                if not product_id_item:
                    continue

                product_id = product_id_item.text().strip()
                if not product_id:
                    continue

                # 获取原标题（第11列）
                original_title_item = table_widget.item(row, 11)
                if not original_title_item:
                    continue

                original_title = original_title_item.text().strip()
                if not original_title:
                    continue

                # 应用标题过滤
                filtered_title = apply_title_filter(original_title, parent_window)

                # 应用标题替换
                replaced_title = apply_title_replace(filtered_title, parent_window)

                # 应用标题前缀
                final_title = apply_title_prefix(replaced_title, parent_window)

                # 更新标题列（第2列）
                from PyQt5.QtWidgets import QTableWidgetItem
                table_widget.setItem(row, 2, QTableWidgetItem(final_title))
                updated_count += 1

                # 更新缓存文件
                if update_product_cache_subject(product_id, final_title):
                    cache_updated_count += 1

            except Exception as e:
                print(f"[标题过滤] 刷新第{row+1}行标题时出错: {e}")
                continue

        print(f"[标题过滤] 实时刷新完成，更新了 {updated_count} 个商品标题，更新了 {cache_updated_count} 个缓存文件")

    except Exception as e:
        print(f"[标题过滤] 刷新标题时出错: {e}")


class Product1688Collector:
    """1688商品采集器"""
    
    def __init__(self):
        """初始化采集器"""
        self.collected_links = []  # 存储采集到的链接
    
    def extract_offer_id_from_url(self, url: str) -> Optional[str]:
        """
        从1688商品链接中提取商品ID
        
        Parameters:
        -----------
        url: str
            商品链接
            
        Returns:
        --------
        Optional[str]:
            提取到的商品ID，如果提取失败返回None
        """
        if not url or not isinstance(url, str):
            return None
        
        # 常见的1688商品链接格式
        patterns = [
            r'detail\.1688\.com/offer/(\d+)\.html',  # 标准详情页
            r'detail\.1688\.com/offer/(\d+)',        # 不带.html
            r'offerId[=:](\d+)',                     # 参数形式
            r'/(\d{10,})\.html',                     # 长数字ID
            r'/(\d{10,})',                           # 长数字ID不带扩展名
            r'spm-auction[=:](\d+)',                 # spm-auction参数
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                offer_id = match.group(1)
                print(f"[1688采集] 从URL中提取到商品ID: {offer_id}")
                return offer_id
        
        print(f"[1688采集] 无法从URL中提取商品ID: {url}")
        return None
    
    def format_standard_link(self, offer_id: str) -> str:
        """
        将商品ID格式化为标准链接格式
        
        Parameters:
        -----------
        offer_id: str
            商品ID
            
        Returns:
        --------
        str:
            标准格式的商品链接
        """
        return f"https://detail.1688.com/offer/{offer_id}.html"
    
    def process_product_link(self, input_link: str) -> Optional[str]:
        """
        处理商品链接，提取ID并格式化为标准格式
        
        Parameters:
        -----------
        input_link: str
            输入的商品链接
            
        Returns:
        --------
        Optional[str]:
            处理后的标准格式链接，失败返回None
        """
        if not input_link:
            return None
        
        # 去除首尾空格
        input_link = input_link.strip()
        
        # 提取商品ID
        offer_id = self.extract_offer_id_from_url(input_link)
        if not offer_id:
            return None
        
        # 格式化为标准链接
        standard_link = self.format_standard_link(offer_id)
        print(f"[1688采集] 链接格式化完成: {input_link} -> {standard_link}")
        
        return standard_link
    
    def process_multiple_links(self, input_text: str) -> List[str]:
        """
        处理多个商品链接（支持换行分隔）
        
        Parameters:
        -----------
        input_text: str
            包含多个链接的文本，可以用换行符分隔
            
        Returns:
        --------
        List[str]:
            处理后的标准格式链接列表
        """
        if not input_text:
            return []
        
        # 按换行符分割
        lines = input_text.strip().split('\n')
        processed_links = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 处理单个链接
            processed_link = self.process_product_link(line)
            if processed_link:
                processed_links.append(processed_link)
                print(f"[1688采集] 成功处理链接: {processed_link}")
            else:
                print(f"[1688采集] 链接处理失败: {line}")
        
        return processed_links
    
    def collect_from_input(self, input_text: str) -> dict:
        """
        从输入文本采集商品链接
        
        Parameters:
        -----------
        input_text: str
            输入的文本，可能包含一个或多个商品链接
            
        Returns:
        --------
        dict:
            采集结果，包含成功和失败的统计信息
        """
        print(f"[1688采集] 开始采集商品链接...")
        print(f"[1688采集] 输入内容: {input_text[:100]}...")
        
        # 处理多个链接
        processed_links = self.process_multiple_links(input_text)
        
        # 添加到采集列表（去重）
        new_links = []
        for link in processed_links:
            if link not in self.collected_links:
                self.collected_links.append(link)
                new_links.append(link)
        
        result = {
            'success': True,
            'total_processed': len(processed_links),
            'new_links_count': len(new_links),
            'duplicate_count': len(processed_links) - len(new_links),
            'new_links': new_links,
            'all_links': self.collected_links.copy()
        }
        
        print(f"[1688采集] 采集完成: 处理{result['total_processed']}个链接，新增{result['new_links_count']}个，重复{result['duplicate_count']}个")
        
        return result
    
    def clear_collected_links(self):
        """清空已采集的链接"""
        self.collected_links.clear()
        print(f"[1688采集] 已清空采集列表")
    
    def get_collected_links(self) -> List[str]:
        """获取已采集的链接列表"""
        return self.collected_links.copy()





class ProductDetailsThread(QThread):
    """商品详情获取线程"""

    # 定义信号
    progress_updated = pyqtSignal(int, int, str)  # 当前进度, 总数, 商品ID
    product_updated = pyqtSignal(int, str, str, str, str, str, str, str, str)  # 行号, 过滤后标题, 原标题, 最终价格, 原价格, 供应商, 类目名称, 类目ID, 类目全称
    finished_signal = pyqtSignal(int, int)  # 成功数, 失败数

    def __init__(self, table_widget, links, parent_window):
        super().__init__()
        self.table_widget = table_widget
        self.links = links
        self.parent_window = parent_window
        self.should_stop = False  # 停止标志

    def run(self):
        """线程执行函数 - 使用并发处理提高效率"""
        try:
            print(f"[1688采集] 开始商品详情采集，总数: {len(self.links)}")

            # 🔧 修复：记录所有要处理的商品ID，用于验证是否有遗漏
            all_offer_ids = []
            for link in self.links:
                import re
                offer_id_pattern = re.compile(r'/offer/(\d+)\.html')
                match = offer_id_pattern.search(link)
                if match:
                    all_offer_ids.append(match.group(1))
                else:
                    all_offer_ids.append(f"invalid_link_{len(all_offer_ids)}")

            print(f"[1688采集] 要处理的商品ID列表: {all_offer_ids}")

            # 导入必要的模块
            import json
            import threading
            from queue import Queue

            # 🚀 直接使用简单的并发策略，无需复杂监控
            print(f"[1688采集] 使用统一的50线程并发策略")
            use_multiprocessing_dummy = False
            use_concurrent_futures = False
            use_sequential = False

            # 尝试导入multiprocessing.dummy
            try:
                from multiprocessing.dummy import Pool as ThreadPool
                use_multiprocessing_dummy = True
                print("[1688采集] ✅ 使用multiprocessing.dummy.Pool（PyInstaller兼容）")
            except ImportError:
                print("[1688采集] ❌ multiprocessing.dummy不可用")

            # 如果multiprocessing.dummy不可用，尝试concurrent.futures
            if not use_multiprocessing_dummy:
                try:
                    import concurrent.futures
                    use_concurrent_futures = True
                    print("[1688采集] ✅ 使用concurrent.futures.ThreadPoolExecutor（备用方案）")
                except ImportError:
                    print("[1688采集] ❌ concurrent.futures不可用")

            # 如果都不可用，使用顺序处理
            if not use_multiprocessing_dummy and not use_concurrent_futures:
                use_sequential = True
                print("[1688采集] ⚠️ 使用顺序处理（最安全但较慢）")

            # 导入API模块 - 使用动态导入
            import importlib.util
            import sys

            # 获取应用程序所在目录
            def get_application_directory():
                if getattr(sys, 'frozen', False):
                    # 如果是打包后的exe文件
                    return os.path.dirname(sys.executable)
                else:
                    # 开发环境
                    return os.path.dirname(os.path.dirname(__file__))

            app_dir = get_application_directory()
            api_module_path = os.path.join(app_dir, "tool", "阿里巴巴接口.py")
            print(f"[1688商品采集] API模块路径: {api_module_path}")
            print(f"[1688商品采集] 文件是否存在: {os.path.exists(api_module_path)}")

            spec = importlib.util.spec_from_file_location("alibaba_api", api_module_path)
            alibaba_api_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(alibaba_api_module)
            AlibabaAPI = alibaba_api_module.AlibabaAPI

            # 创建缓存目录
            cache_dir = get_config_file_path(os.path.join('config', 'pdcache'))
            os.makedirs(cache_dir, exist_ok=True)
            print(f"[1688商品采集] 缓存目录: {cache_dir}")

            # 找到表格中的列索引
            original_title_col = None
            original_price_col = None
            supplier_col = None

            for col in range(self.table_widget.columnCount()):
                header_item = self.table_widget.horizontalHeaderItem(col)
                if header_item:
                    header_text = header_item.text()
                    if header_text == "原标题":
                        original_title_col = col
                    elif header_text == "原价":
                        original_price_col = col
                    elif header_text == "上家":
                        supplier_col = col

            print(f"[1688采集] 找到列索引 - 原标题: {original_title_col}, 原价: {original_price_col}, 上家: {supplier_col}")

            # 预编译正则表达式，提高性能
            offer_id_pattern = re.compile(r'/offer/(\d+)\.html')

            # 🚀 使用保守的并发数，确保系统稳定
            # 根据系统环境动态调整并发数
            import sys
            # 🚀 智能设置并发数：少于100按实际数量，大于等于100固定为100
            if len(self.links) < 100:
                max_workers = len(self.links)  # 少于100按实际并发
                print(f"[1688采集] 商品数量 < 100，使用实际并发数: {max_workers}")
            else:
                max_workers = 100  # 大于等于100固定为100
                print(f"[1688采集] 商品数量 >= 100，使用固定并发数: {max_workers}")

            if getattr(sys, 'frozen', False):
                print(f"[1688采集] 打包环境：最终并发数 {max_workers}")
                # 优先使用multiprocessing.dummy，但不强制
                if not use_multiprocessing_dummy and not use_concurrent_futures:
                    use_sequential = True
                    print(f"[1688采集] 打包环境：无可用线程池，使用顺序处理")
            else:
                print(f"[1688采集] 开发环境：最终并发数 {max_workers}")

            print(f"[1688采集] 开始并发采集，总商品数: {len(self.links)}, 并发数: {max_workers}")

            # 用于线程安全的计数器
            success_count = 0
            error_count = 0
            processed_count = 0
            count_lock = threading.Lock()

            def process_single_product(index_and_link):
                """处理单个商品的函数 - 增强错误处理和日志记录"""
                nonlocal success_count, error_count, processed_count

                # 检查是否需要停止
                if self.should_stop:
                    print(f"[1688采集] 收到停止信号，跳过商品处理")
                    with count_lock:
                        processed_count += 1
                        self.progress_updated.emit(processed_count, len(self.links), "已停止")
                    return

                index, link = index_and_link
                print(f"[1688采集] 开始处理第 {index+1} 个商品: {link}")

                try:
                    # 创建独立的API实例（线程安全）
                    # 从配置文件获取API参数
                    import json
                    import os

                    # 获取软件运行目录
                    if getattr(sys, 'frozen', False):
                        app_dir = os.path.dirname(sys.executable)
                    else:
                        app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

                    config_path = get_config_file_path(os.path.join("config", "config.json"))
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    # 获取1688 API配置
                    api_config = config.get('alibabafenxiao', {})
                    app_key = api_config.get('app_key', '74268078')
                    app_secret = api_config.get('app_secret', 'iw9Uw4AzPQnF')
                    access_token = api_config.get('access_token', '67ef424c-3fcc-4944-bfd2-e8829d7bc74d')

                    api = AlibabaAPI(app_key, app_secret, access_token, api_type='fenxiao')

                    # 从链接中提取商品ID
                    match = offer_id_pattern.search(link)
                    if not match:
                        print(f"[1688采集] 无法从链接中提取商品ID: {link}")
                        with count_lock:
                            error_count += 1
                            processed_count += 1
                            self.progress_updated.emit(processed_count, len(self.links), f"处理失败: 无效链接")
                        return

                    offer_id = match.group(1)
                    print(f"[1688采集] 线程处理商品ID: {offer_id}")

                    # 获取商品详情 - 使用微信小店版本的调用方式，增加重试机制
                    print(f"[1688采集] 正在调用API获取商品 {offer_id} 详情...")

                    # 🔧 修复：添加重试机制，确保不会因为临时网络问题跳过商品
                    max_retries = 3
                    retry_delay = 1  # 秒
                    result = None

                    for retry_count in range(max_retries):
                        try:
                            if retry_count > 0:
                                print(f"[1688采集] 商品 {offer_id} 第 {retry_count + 1} 次重试...")
                                import time
                                time.sleep(retry_delay * retry_count)  # 递增延迟

                            result = api.get_fenxiao_product_info_wechat(offer_id=offer_id)

                            if result and result.get('success'):
                                print(f"[1688采集] 商品 {offer_id} API调用成功 (尝试 {retry_count + 1}/{max_retries})")
                                break
                            else:
                                error_msg = result.get('error_message', '未知错误') if result else 'API返回为空'
                                print(f"[1688采集] 商品 {offer_id} API调用失败 (尝试 {retry_count + 1}/{max_retries}): {error_msg}")

                                # 如果是最后一次重试，记录详细错误
                                if retry_count == max_retries - 1:
                                    print(f"[1688采集] 商品 {offer_id} 所有重试均失败，最终错误: {error_msg}")

                        except Exception as api_error:
                            print(f"[1688采集] 商品 {offer_id} API调用异常 (尝试 {retry_count + 1}/{max_retries}): {str(api_error)}")
                            if retry_count == max_retries - 1:
                                print(f"[1688采集] 商品 {offer_id} 所有重试均失败，最终异常: {str(api_error)}")

                    if result and result.get('success'):
                        # API返回的数据结构：{'product_info': {...}, 'success': True}
                        product_info = result.get('product_info', {})

                        # 提取关键信息
                        subject = product_info.get('subject', '')
                        reference_price = product_info.get('reference_price', '')
                        supplier_login_id = product_info.get('supplier_login_id', '')
                        category_name = product_info.get('category_name', '')
                        category_id = product_info.get('category_id', '')

                        # 确保类目ID是字符串类型
                        category_id_str = str(category_id) if category_id else ''

                        print(f"[1688采集] 商品 {offer_id} 详情获取成功")
                        if category_name:
                            print(f"[1688采集] 商品类目: {category_name} (ID: {category_id_str})")

                        # 应用标题过滤
                        filtered_subject = apply_title_filter(subject, self.parent_window)

                        # 应用标题替换
                        replaced_subject = apply_title_replace(filtered_subject, self.parent_window)

                        # 应用标题前缀
                        final_subject = apply_title_prefix(replaced_subject, self.parent_window)

                        # 应用价格计算
                        final_price = apply_price_calculation(reference_price, self.parent_window)

                        # 更新result中的标题为最终标题（过滤+前缀）
                        if 'product_info' in result:
                            result['product_info']['subject'] = final_subject
                            # 保存原始价格到新字段，不覆盖reference_price
                            if 'reference_price_original' not in result['product_info']:
                                result['product_info']['reference_price_original'] = reference_price
                            # 将计算后的价格保存到新字段
                            result['product_info']['reference_price_calculated'] = final_price

                        # 同时更新raw_data中的subject（如果存在）
                        if 'raw_data' in result and 'productInfo' in result['raw_data']:
                            result['raw_data']['productInfo']['subject'] = final_subject

                        # 🔧 修复：在采集线程中直接进行类目匹配，不等待异步处理
                        print(f"[1688采集] 开始类目匹配，原始1688类目: {category_name}")

                        # 🔧 优化：快速类目匹配，避免阻塞
                        try:
                            # 限制匹配时间，避免阻塞
                            match_result = _match_category_from_mapping(final_subject, category_name, str(category_id))
                            if match_result:
                                # 从匹配结果中提取类目信息
                                dongma_category_name = match_result.get('category', '')
                                dongma_category_ids = match_result.get('category_ids', '')
                                # 构建完整路径（如果有的话）
                                dongma_category_full_name = match_result.get('full_path_comma', dongma_category_name)
                                # 减少日志输出，提高性能
                            else:
                                # 如果映射匹配失败，使用原始1688类目信息
                                dongma_category_name = category_name
                                dongma_category_ids = str(category_id)
                                dongma_category_full_name = category_name
                        except Exception as e:
                            # 异常时快速回退，不打印详细错误
                            dongma_category_name = category_name
                            dongma_category_ids = str(category_id)
                            dongma_category_full_name = category_name

                        # 发送商品更新信号（包含最终标题、原标题、最终价格、原价格、懂嘛类目信息）
                        # 确保所有参数都是字符串类型
                        self.product_updated.emit(
                            index,
                            str(final_subject),
                            str(subject),
                            str(final_price),
                            str(reference_price),
                            str(supplier_login_id),
                            str(dongma_category_name),
                            str(dongma_category_ids),
                            str(dongma_category_full_name)
                        )

                        # 处理SKU价格计算（简化版本，减少日志输出）
                        try:
                            self._process_sku_prices(result, offer_id)
                        except Exception as e:
                            print(f"[SKU价格] 处理商品 {offer_id} SKU价格时出错: {e}")

                        # 处理SKU过滤（删除包含关键词的SKU文本）
                        try:
                            self._process_sku_filter(result, offer_id)
                        except Exception as e:
                            print(f"[SKU过滤] 处理商品 {offer_id} SKU过滤时出错: {e}")

                        # 保存为JSON文件（已包含过滤后的标题和计算后的SKU价格）
                        json_file = os.path.join(cache_dir, f"{offer_id}.json")
                        with open(json_file, 'w', encoding='utf-8') as f:
                            json.dump(result, f, ensure_ascii=False, indent=2)
                        print(f"[1688采集] 商品详情已保存: {json_file}")

                        with count_lock:
                            success_count += 1
                            processed_count += 1
                            # 🚀 实时显示：每个商品都发送进度信号，确保边采集边显示
                            self.progress_updated.emit(processed_count, len(self.links), f"成功: {offer_id}")
                    else:
                        # 详细记录失败原因
                        error_msg = "未知错误"
                        if result:
                            error_msg = result.get('error_message', result.get('error_code', '未知错误'))
                            print(f"[1688采集] 商品 {offer_id} 详情获取失败: {error_msg}")
                            print(f"[1688采集] 完整错误详情: {result}")
                        else:
                            error_msg = "API返回为空"
                            print(f"[1688采集] 商品 {offer_id} API返回为空")

                        with count_lock:
                            error_count += 1
                            processed_count += 1
                            # 失败时立即发送信号，包含具体错误信息
                            self.progress_updated.emit(processed_count, len(self.links), f"失败: {offer_id} ({error_msg[:20]})")

                except Exception as e:
                    print(f"[1688采集] 商品 {offer_id if 'offer_id' in locals() else 'unknown'} 处理异常: {e}")
                    import traceback
                    traceback.print_exc()
                    with count_lock:
                        error_count += 1
                        processed_count += 1
                        # 异常时立即发送信号，包含商品ID
                        offer_id_for_error = offer_id if 'offer_id' in locals() else f"index_{index}"
                        self.progress_updated.emit(processed_count, len(self.links), f"异常: {offer_id_for_error} ({str(e)[:15]})")

                finally:
                    # 🚀 每个任务完成后清理局部变量，释放内存
                    try:
                        # 清理局部变量
                        locals().clear()
                    except:
                        pass

            # 🚀 根据可用的实现选择处理策略
            all_tasks = [(i, link) for i, link in enumerate(self.links)]
            print(f"[1688采集] 准备处理 {len(all_tasks)} 个任务，最大并发: {max_workers}")

            # 🔧 修复：记录所有任务，确保没有遗漏
            task_ids = [f"{i}:{link.split('/')[-1].split('.')[0] if '/' in link else link[:10]}" for i, link in all_tasks]
            print(f"[1688采集] 任务列表: {task_ids}")

            # 🚀 移除强制顺序处理，统一使用并发处理
            print(f"[1688采集] 统一使用并发处理，商品数量: {len(self.links)}")

            if use_multiprocessing_dummy:
                # 方案1：使用multiprocessing.dummy.Pool（最推荐）
                try:
                    print(f"[1688采集] 使用multiprocessing.dummy.Pool处理")
                    with ThreadPool(max_workers) as pool:
                        # 🔧 修复：简化处理逻辑，确保所有任务都被处理
                        print(f"[1688采集] 开始处理所有 {len(all_tasks)} 个任务")

                        # 记录开始处理的任务
                        start_task_ids = [f"{i}:{link.split('/')[-1].split('.')[0] if '/' in link else link[:10]}" for i, link in all_tasks]
                        print(f"[1688采集] 开始处理任务: {start_task_ids}")

                        try:
                            # 🔧 修复：使用map_async确保所有任务都被提交
                            result_async = pool.map_async(process_single_product, all_tasks)

                            # 等待所有任务完成，设置合理的超时时间
                            timeout_seconds = max(300, len(all_tasks) * 10)  # 每个任务最多10秒，最少5分钟
                            print(f"[1688采集] 等待任务完成，超时时间: {timeout_seconds}秒")

                            results = result_async.get(timeout=timeout_seconds)
                            print(f"[1688采集] 所有任务处理完成，结果数量: {len(results) if results else 0}")

                        except Exception as process_error:
                            print(f"[1688采集] 任务处理异常: {process_error}")
                            import traceback
                            traceback.print_exc()

                            # 🔧 修复：即使出现异常也要等待已提交的任务完成
                            try:
                                print(f"[1688采集] 等待已提交的任务完成...")
                                pool.close()
                                pool.join()
                            except:
                                pass

                except Exception as pool_error:
                    print(f"[1688采集] multiprocessing.dummy.Pool异常: {pool_error}")
                    import traceback
                    traceback.print_exc()
                    # 🚀 Pool异常时降级到顺序处理
                    print(f"[1688采集] Pool异常，降级到顺序处理")
                    use_sequential = True
                    use_multiprocessing_dummy = False

            elif use_concurrent_futures:
                # 方案2：使用concurrent.futures（备用方案，打包环境下谨慎使用）
                try:
                    print(f"[1688采集] 使用concurrent.futures.ThreadPoolExecutor处理（备用方案）")

                    # 🚀 使用已计算好的max_workers（少于50按实际，大于等于50固定50）
                    print(f"[1688采集] 使用ThreadPoolExecutor，并发数: {max_workers}")

                    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                        # 🚀 智能分批策略：少于100个直接提交，大于等于100个分批提交
                        if len(all_tasks) < 100:
                            # 少于100个任务，直接提交所有任务
                            print(f"[1688采集] 任务数 < 100，直接提交所有 {len(all_tasks)} 个任务")
                            future_to_task = {
                                executor.submit(process_single_product, task): task
                                for task in all_tasks
                            }

                            # 处理完成的任务
                            completed_count = 0
                            try:
                                for future in concurrent.futures.as_completed(future_to_task, timeout=180):
                                    if self.should_stop:
                                        break
                                    completed_count += 1
                                    try:
                                        future.result(timeout=15)
                                        if completed_count % 10 == 0:
                                            print(f"[1688采集] 进度: {completed_count}/{len(all_tasks)}")
                                    except Exception as e:
                                        print(f"[1688采集] 任务执行异常: {e}")
                            except concurrent.futures.TimeoutError:
                                print(f"[1688采集] 整体处理超时")
                        else:
                            # 大于等于100个任务，分批提交
                            batch_size = 100
                            print(f"[1688采集] 任务数 >= 100，使用分批提交，每批 {batch_size} 个任务")

                            for batch_start in range(0, len(all_tasks), batch_size):
                                if self.should_stop:
                                    print(f"[1688采集] 收到停止信号，终止批量提交")
                                    break

                                batch_end = min(batch_start + batch_size, len(all_tasks))
                                batch_tasks = all_tasks[batch_start:batch_end]

                                print(f"[1688采集] 提交批次 {batch_start//batch_size + 1}，任务数: {len(batch_tasks)}")

                                # 提交当前批次的任务
                                future_to_task = {
                                    executor.submit(process_single_product, task): task
                                    for task in batch_tasks
                                }

                                # 处理当前批次的完成任务
                                completed_count = 0
                                try:
                                    for future in concurrent.futures.as_completed(future_to_task, timeout=120):  # 批次超时2分钟
                                        if self.should_stop:
                                            print(f"[1688采集] 收到停止信号，终止批次处理")
                                            break

                                        task = future_to_task[future]
                                        completed_count += 1

                                        try:
                                            future.result(timeout=15)  # 单个任务超时15秒
                                            if completed_count % 10 == 0:  # 每10个任务打印一次进度
                                                print(f"[1688采集] 批次进度: {completed_count}/{len(batch_tasks)}")
                                        except concurrent.futures.TimeoutError:
                                            print(f"[1688采集] 任务 {task} 处理超时")
                                            with count_lock:
                                                error_count += 1
                                                processed_count += 1
                                                self.progress_updated.emit(processed_count, len(self.links), "处理超时")
                                        except Exception as e:
                                            print(f"[1688采集] 任务 {task} 执行异常: {e}")
                                            with count_lock:
                                                error_count += 1
                                                processed_count += 1
                                                self.progress_updated.emit(processed_count, len(self.links), f"异常: {str(e)[:20]}")

                                except concurrent.futures.TimeoutError:
                                    print(f"[1688采集] 批次处理超时")

                                # 🚀 每批处理后强制垃圾回收
                                import gc
                                gc.collect()

                except Exception as executor_error:
                    print(f"[1688采集] 线程池执行异常: {executor_error}")
                    import traceback
                    traceback.print_exc()
                    # 🚀 ThreadPoolExecutor异常时降级到顺序处理
                    print(f"[1688采集] ThreadPoolExecutor异常，降级到顺序处理")
                    use_sequential = True
                    use_concurrent_futures = False

            # 🚀 检查是否需要降级到顺序处理
            if use_sequential or (not use_multiprocessing_dummy and not use_concurrent_futures):
                # 方案3：顺序处理（最安全的备用方案）
                print(f"[1688采集] 使用顺序处理（最安全但较慢）")

                for i, task in enumerate(all_tasks):
                    if self.should_stop:
                        print(f"[1688采集] 收到停止信号，终止顺序处理")
                        break

                    try:
                        process_single_product(task)
                        if (i + 1) % 5 == 0:  # 每5个任务打印一次进度
                            print(f"[1688采集] 顺序处理进度: {i + 1}/{len(all_tasks)}")

                        # 🚀 每处理10个商品后短暂休息，避免系统过载
                        if (i + 1) % 10 == 0:
                            import time
                            time.sleep(0.1)

                    except Exception as e:
                        print(f"[1688采集] 顺序处理任务 {task} 异常: {e}")
                        with count_lock:
                            error_count += 1
                            processed_count += 1
                            self.progress_updated.emit(processed_count, len(self.links), f"异常: {str(e)[:20]}")

                print(f"[1688采集] 顺序处理完成，处理了 {len(all_tasks)} 个任务")

            # 🔧 修复：验证所有商品是否都被处理了
            print(f"[1688采集] 采集完成统计:")
            print(f"[1688采集] - 总商品数: {len(self.links)}")
            print(f"[1688采集] - 成功处理: {success_count}")
            print(f"[1688采集] - 失败处理: {error_count}")
            print(f"[1688采集] - 已处理总数: {processed_count}")

            # 检查是否有遗漏的商品
            if processed_count < len(self.links):
                missing_count = len(self.links) - processed_count
                print(f"[1688采集] ⚠️ 警告：有 {missing_count} 个商品未被处理！")
                print(f"[1688采集] 预期处理商品ID: {all_offer_ids}")

                # 尝试找出未处理的商品
                for i in range(len(self.links)):
                    if i >= processed_count:
                        print(f"[1688采集] 未处理的商品 {i+1}: {self.links[i]}")

            # 🚀 强制垃圾回收，释放内存
            import gc
            gc.collect()
            print(f"[1688采集] 已执行垃圾回收，释放内存")

            # 🚀 打包环境下的额外内存清理
            if getattr(sys, 'frozen', False):
                try:
                    # 清理局部变量
                    locals().clear()
                    # 再次垃圾回收
                    gc.collect()
                    print(f"[1688采集] 打包环境：已执行额外内存清理")
                except:
                    pass

            # 发送完成信号
            self.finished_signal.emit(success_count, error_count)
            print(f"[1688采集] 并发采集完成！成功: {success_count} 个，失败: {error_count} 个")

        except Exception as e:
            print(f"[1688采集] 线程执行异常: {e}")

            import traceback
            traceback.print_exc()

            # 🚀 异常时也要清理资源
            try:
                import gc
                gc.collect()
                print(f"[1688采集] 异常处理：已执行垃圾回收")
            except:
                pass

            self.finished_signal.emit(0, len(self.links))

        finally:
            # 🚀 确保线程结束时清理所有资源
            try:
                import gc
                gc.collect()
                print(f"[1688采集] 线程结束：最终垃圾回收完成")
            except:
                pass

    def stop(self):
        """停止采集"""
        print(f"[1688采集] 收到停止信号")
        self.should_stop = True

    def _process_sku_prices(self, result, offer_id):
        """处理SKU价格计算的辅助方法"""
        try:
            # 检查 product_info 中的SKU数据
            if 'product_info' in result:
                product_info = result['product_info']

                # 处理SKU价格 - product_info.product_sku_infos
                if 'product_sku_infos' in product_info:
                    for sku_index, sku in enumerate(product_info['product_sku_infos']):
                        self._update_sku_price(sku, 'consignPrice', 'consignPrice_original')
                        self._update_sku_price(sku, 'multipleConsignPrice', 'multipleConsignPrice_original')

                # 处理SKU价格 - product_info.processed_sku_infos
                if 'processed_sku_infos' in product_info:
                    for sku_index, sku in enumerate(product_info['processed_sku_infos']):
                        self._update_sku_price(sku, 'consign_price', 'consign_price_original')
                        self._update_sku_price(sku, 'multiple_consign_price', 'multiple_consign_price_original')

            # 处理SKU价格 - product_sku_infos
            if 'product_sku_infos' in result:
                for sku_index, sku in enumerate(result['product_sku_infos']):
                    self._update_sku_price(sku, 'consignPrice', 'consignPrice_original')
                    self._update_sku_price(sku, 'multipleConsignPrice', 'multipleConsignPrice_original')

            # 处理SKU价格 - processed_sku_infos
            if 'processed_sku_infos' in result:
                for sku_index, sku in enumerate(result['processed_sku_infos']):
                    self._update_sku_price(sku, 'consign_price', 'consign_price_original')
                    self._update_sku_price(sku, 'multiple_consign_price', 'multiple_consign_price_original')

        except Exception as e:
            print(f"[SKU价格] 处理SKU价格时出错: {e}")

    def _update_sku_price(self, sku, price_field, original_field):
        """更新单个SKU价格的辅助方法"""
        try:
            if price_field in sku:
                original_price = sku[price_field]
                # 保存原始价格（如果还没有保存过）
                if original_field not in sku:
                    sku[original_field] = original_price

                new_price = apply_price_calculation(str(original_price), self.parent_window)
                # 转换回数字类型
                try:
                    sku[price_field] = float(new_price.split('~')[0]) if '~' in new_price else float(new_price)
                except Exception as e:
                    sku[price_field] = original_price
        except Exception as e:
            print(f"[SKU价格] 更新价格字段 {price_field} 时出错: {e}")

    def _process_sku_filter(self, result, offer_id):
        """处理SKU过滤的辅助方法 - 删除包含关键词的SKU文本"""
        try:
            # 检查是否启用SKU过滤
            if not self._is_sku_filter_enabled():
                return

            # 获取过滤关键词
            filter_keywords = self._get_sku_filter_keywords()
            if not filter_keywords:
                print(f"[SKU过滤] 商品 {offer_id} 未设置过滤关键词，跳过过滤")
                return

            print(f"[SKU过滤] 开始处理商品 {offer_id} 的SKU过滤，关键词数量: {len(filter_keywords)}")

            # 处理 product_info 中的SKU数据
            if 'product_info' in result:
                product_info = result['product_info']

                # 处理 product_info.product_sku_infos
                if 'product_sku_infos' in product_info:
                    for sku_index, sku in enumerate(product_info['product_sku_infos']):
                        self._filter_sku_text_fields(sku, filter_keywords, f"product_info.product_sku_infos[{sku_index}]")

                # 处理 product_info.processed_sku_infos
                if 'processed_sku_infos' in product_info:
                    for sku_index, sku in enumerate(product_info['processed_sku_infos']):
                        self._filter_sku_text_fields(sku, filter_keywords, f"product_info.processed_sku_infos[{sku_index}]")

            # 处理根级别的SKU数据
            if 'product_sku_infos' in result:
                for sku_index, sku in enumerate(result['product_sku_infos']):
                    self._filter_sku_text_fields(sku, filter_keywords, f"product_sku_infos[{sku_index}]")

            if 'processed_sku_infos' in result:
                for sku_index, sku in enumerate(result['processed_sku_infos']):
                    self._filter_sku_text_fields(sku, filter_keywords, f"processed_sku_infos[{sku_index}]")

            # 处理 raw_data 中的SKU数据
            if 'raw_data' in result and 'productSkuInfos' in result['raw_data']:
                for sku_index, sku in enumerate(result['raw_data']['productSkuInfos']):
                    self._filter_sku_text_fields(sku, filter_keywords, f"raw_data.productSkuInfos[{sku_index}]")

            print(f"[SKU过滤] 商品 {offer_id} SKU过滤处理完成")

        except Exception as e:
            print(f"[SKU过滤] 处理商品 {offer_id} SKU过滤时出错: {e}")

    def _is_sku_filter_enabled(self):
        """检查是否启用SKU过滤"""
        try:
            if self.parent_window and hasattr(self.parent_window, 'sku_filter_checkbox'):
                return self.parent_window.sku_filter_checkbox.isChecked()
            return False
        except Exception as e:
            print(f"[SKU过滤] 检查SKU过滤状态时出错: {e}")
            return False

    def _get_sku_filter_keywords(self):
        """获取SKU过滤关键词列表"""
        try:
            if self.parent_window and hasattr(self.parent_window, 'sku_filter_text'):
                filter_text = self.parent_window.sku_filter_text.toPlainText().strip()
                if filter_text:
                    # 按逗号分割关键词，并去除空白
                    keywords = [keyword.strip() for keyword in filter_text.split(',') if keyword.strip()]
                    return keywords
            return []
        except Exception as e:
            print(f"[SKU过滤] 获取过滤关键词时出错: {e}")
            return []

    def _filter_sku_text_fields(self, sku, filter_keywords, sku_path):
        """过滤SKU文本字段中的关键词"""
        try:
            filtered_count = 0

            # 过滤货号字段
            cargo_number_fields = ['cargoNumber', 'cargo_number']
            for field in cargo_number_fields:
                if field in sku and sku[field]:
                    original_value = str(sku[field])
                    filtered_value = self._remove_keywords_from_text(original_value, filter_keywords)
                    if filtered_value != original_value:
                        sku[field] = filtered_value
                        print(f"[SKU过滤] {sku_path}.{field}: '{original_value}' → '{filtered_value}'")
                        filtered_count += 1

            # 过滤属性字段
            if 'attributes' in sku and isinstance(sku['attributes'], list):
                for attr_index, attr in enumerate(sku['attributes']):
                    # 过滤属性值
                    value_fields = ['attributeValue', 'attribute_value']
                    for field in value_fields:
                        if field in attr and attr[field]:
                            original_value = str(attr[field])
                            filtered_value = self._remove_keywords_from_text(original_value, filter_keywords)
                            if filtered_value != original_value:
                                attr[field] = filtered_value
                                print(f"[SKU过滤] {sku_path}.attributes[{attr_index}].{field}: '{original_value}' → '{filtered_value}'")
                                filtered_count += 1

            if filtered_count > 0:
                print(f"[SKU过滤] {sku_path} 共过滤了 {filtered_count} 个字段")

        except Exception as e:
            print(f"[SKU过滤] 过滤SKU字段时出错: {e}")

    def _remove_keywords_from_text(self, text, keywords):
        """从文本中删除关键词"""
        try:
            if not text or not keywords:
                return text

            filtered_text = text
            for keyword in keywords:
                if keyword in filtered_text:
                    filtered_text = filtered_text.replace(keyword, '')

            # 清理多余的空格和标点符号
            filtered_text = filtered_text.strip()
            # 清理连续的空格
            import re
            filtered_text = re.sub(r'\s+', ' ', filtered_text)

            return filtered_text

        except Exception as e:
            print(f"[SKU过滤] 删除关键词时出错: {e}")
            return text


def _initialize_category_mapping_cache():
    """初始化类目映射缓存"""
    global _category_mapping_cache, _mapping_cache_initialized

    if _mapping_cache_initialized:
        return _category_mapping_cache

    try:
        import json
        import os

        # 读取类目映射.json文件
        mapping_file = get_config_file_path(os.path.join("config", "类目映射.json"))

        if not os.path.exists(mapping_file):
            print(f"[类目映射] 类目映射文件不存在: {mapping_file}")
            _category_mapping_cache = {}
            _mapping_cache_initialized = True
            return _category_mapping_cache

        with open(mapping_file, 'r', encoding='utf-8') as f:
            _category_mapping_cache = json.load(f)

        print(f"[类目映射] 缓存初始化完成，加载了 {len(_category_mapping_cache)} 个主类目")
        _mapping_cache_initialized = True
        return _category_mapping_cache

    except Exception as e:
        print(f"[类目映射] 缓存初始化失败: {e}")
        _category_mapping_cache = {}
        _mapping_cache_initialized = True
        return _category_mapping_cache

def _match_category_from_mapping(product_title, original_category_name, original_category_id):
    """
    从config\类目映射.json中匹配类目信息（优化版本，使用缓存）

    Parameters:
    -----------
    product_title: str
        商品标题
    original_category_name: str
        原始1688类目名称
    original_category_id: str
        原始1688类目ID

    Returns:
    --------
    dict or None:
        匹配到的类目信息，包含category, category_ids, full_path_comma等字段
        如果没有匹配到返回None
    """
    try:
        # 🔧 优化：使用缓存，避免重复读取文件
        mapping_data = _initialize_category_mapping_cache()

        if not mapping_data:
            return None

        # 🔧 优化：简化匹配逻辑，减少日志输出，提高性能
        product_title_lower = product_title.lower()

        # 直接进行全局关键词匹配，简化逻辑
        for main_category, category_configs in mapping_data.items():
            if not isinstance(category_configs, list):
                continue

            # 遍历该主类目下的所有配置
            for config in category_configs:
                if not isinstance(config, dict):
                    continue

                keyword = config.get('keyword', '')
                if not keyword:
                    continue

                # 检查商品标题中是否包含关键词
                if keyword.lower() in product_title_lower:
                    # 只在匹配成功时输出日志
                    print(f"[类目映射] ✅ 匹配成功: '{keyword}' -> {config.get('category', '')}")
                    return config

        # 匹配失败时不输出日志，减少噪音
        return None

    except Exception as e:
        print(f"[类目映射] ❌ 类目映射匹配异常: {e}")
        import traceback
        traceback.print_exc()
        return None


def start_product_details_thread(table_widget, links, parent_window):
    """启动商品详情获取线程"""

    def on_progress_updated(current, total, status):
        """进度更新回调"""
        print(f"[1688采集] 正在处理第 {current}/{total} 个商品 - {status}")
        # 更新进度显示
        if hasattr(parent_window, 'update_collection_progress'):
            parent_window.update_collection_progress(current, total, status)

    def on_product_updated(row, final_title, original_title, final_price, original_price, supplier, category_name, category_id, category_full_name):
        """商品信息更新回调"""
        # 在主线程中更新表格
        if final_title:
            # 最终标题（过滤+前缀）显示到第2列（标题列）
            table_widget.setItem(row, 2, QTableWidgetItem(final_title))  # 标题列

        if original_title:
            # 原标题存储到第11列（原标题列）
            table_widget.setItem(row, 11, QTableWidgetItem(original_title))  # 原标题列

        if final_price:
            # 计算后的价格显示到第5列（价格列）
            table_widget.setItem(row, 5, QTableWidgetItem(final_price))  # 价格列

        if original_price:
            # 原始价格显示到第10列（原价列）
            table_widget.setItem(row, 10, QTableWidgetItem(original_price))  # 原价列

        if supplier:
            table_widget.setItem(row, 12, QTableWidgetItem(supplier))  # 上家列

        # 🔧 优化：使用商品复制.py中更好更准确的三种类目匹配方法
        final_category_name = ""  # 默认为空
        final_category_id = ""    # 默认为空
        final_category_full_name = ""  # 默认为空

        # 使用商品复制.py中的三种匹配方法（更准确）
        try:
            from 商品复制 import ProductCopyWindow
            # 创建一个临时实例来调用匹配方法
            temp_instance = ProductCopyWindow()
            match_result = temp_instance._sync_match_category_by_name(category_name, final_title)

            if match_result and match_result.get('found'):
                # 使用三种匹配方法的结果
                final_category_name = match_result.get('full_path', '')
                final_category_id = match_result.get('category_ids', '')
                final_category_full_name = match_result.get('full_path_comma', '')
                print(f"[1688采集] ✅ 三种匹配方法成功: {final_category_name} (ID: {final_category_id})")
                print(f"[1688采集] ✅ 完整路径: {final_category_full_name}")
            else:
                print(f"[1688采集] ⚠️ 三种匹配方法失败，类目字段保持为空")
        except Exception as e:
            # 如果调用失败，类目字段保持为空
            print(f"[1688采集] ❌ 调用三种匹配方法异常: {e}，类目字段保持为空")

        print(f"[1688采集] 填充类目信息: category_name='{final_category_name}', category_id='{final_category_id}', category_full_name='{final_category_full_name}'")

        # 类目列（第3列）：显示类目名称
        table_widget.setItem(row, 3, QTableWidgetItem(final_category_name))
        # 类目id列（第4列）：显示类目ID
        table_widget.setItem(row, 4, QTableWidgetItem(final_category_id))
        # 类目全称列（第9列）：显示完整类目路径
        table_widget.setItem(row, 9, QTableWidgetItem(final_category_full_name))

        print(f"[1688采集] 类目信息填充完成: {final_category_name} -> {final_category_full_name}")

        # 🚀 优化：移除第一次填充数据时的表格模式切换，避免性能阻塞
        # 注释掉原来的表格模式切换代码，因为已在初始化时设置了固定列宽
        # if row == 0 and hasattr(table_widget, 'switch_to_content_resize'):
        #     print("[1688采集] 第一次填充数据，切换到内容自适应模式")
        #     table_widget.switch_to_content_resize()

        # ✅ 类目匹配已在此处理完成，优先使用config\类目映射.json

    def on_finished(success_count, error_count):
        """完成回调"""
        print(f"[1688采集] 商品详情获取完成！成功: {success_count} 个，失败: {error_count} 个")
        # 更新进度显示为完成状态
        total_count = success_count + error_count
        if hasattr(parent_window, 'update_collection_progress'):
            parent_window.update_collection_progress(total_count, total_count, f"完成 (成功:{success_count}, 失败:{error_count})")

    # 创建并启动线程
    thread = ProductDetailsThread(table_widget, links, parent_window)
    thread.progress_updated.connect(on_progress_updated)
    thread.product_updated.connect(on_product_updated)
    thread.finished_signal.connect(on_finished)

    # 保存线程引用，防止被垃圾回收
    if not hasattr(parent_window, '_product_threads'):
        parent_window._product_threads = []
    parent_window._product_threads.append(thread)

    thread.start()
    print(f"[1688采集] 商品详情获取线程已启动")


def refresh_all_titles(table_widget, parent_window):
    """
    实时刷新表格中所有商品的标题列
    当标题过滤关键词发生变化时调用

    Args:
        table_widget: 商品表格控件
        parent_window: 主窗口对象
    """
    try:
        print(f"[标题过滤] 开始实时刷新所有商品标题...")

        if not table_widget:
            print(f"[标题过滤] 表格控件为空，无法刷新")
            return

        row_count = table_widget.rowCount()
        if row_count == 0:
            print(f"[标题过滤] 表格为空，无需刷新")
            return

        updated_count = 0

        # 遍历所有行
        for row in range(row_count):
            try:
                # 获取原标题（第11列）
                original_title_item = table_widget.item(row, 11)
                if not original_title_item:
                    continue

                original_title = original_title_item.text().strip()
                if not original_title:
                    continue

                # 应用标题过滤
                filtered_title = apply_title_filter(original_title, parent_window)

                # 应用标题替换
                replaced_title = apply_title_replace(filtered_title, parent_window)

                # 应用标题前缀
                final_title = apply_title_prefix(replaced_title, parent_window)

                # 更新标题列（第2列）
                table_widget.setItem(row, 2, QTableWidgetItem(final_title))
                updated_count += 1

            except Exception as e:
                print(f"[标题过滤] 刷新第{row+1}行标题时出错: {e}")
                continue

        print(f"[标题过滤] 实时刷新完成，更新了 {updated_count} 个商品标题")

    except Exception as e:
        print(f"[标题过滤] 实时刷新标题时出错: {e}")


def setup_title_filter_realtime_update(parent_window):
    """
    设置标题过滤的实时更新功能
    监听标题过滤文本框和复选框的变化

    Args:
        parent_window: 主窗口对象
    """
    try:
        # 检查必要的控件是否存在
        if not hasattr(parent_window, 'title_filter_text'):
            print(f"[标题过滤] 主窗口没有title_filter_text属性，无法设置实时更新")
            return

        if not hasattr(parent_window, 'title_filter_checkbox'):
            print(f"[标题过滤] 主窗口没有title_filter_checkbox属性，无法设置实时更新")
            return

        # 获取商品表格
        table_widget = None
        if hasattr(parent_window, 'table_widget'):
            table_widget = parent_window.table_widget
        else:
            print(f"[标题过滤] 主窗口没有table_widget属性，无法设置实时更新")
            return

        def on_title_filter_changed():
            """标题过滤内容变化时的回调"""
            print(f"[标题过滤] 检测到标题过滤内容变化，开始实时更新...")
            refresh_all_titles_with_cache_update(table_widget, parent_window)

        def on_checkbox_changed():
            """复选框状态变化时的回调"""
            print(f"[标题过滤] 检测到复选框状态变化，开始实时更新...")
            refresh_all_titles_with_cache_update(table_widget, parent_window)

        # 连接信号
        parent_window.title_filter_text.textChanged.connect(on_title_filter_changed)
        parent_window.title_filter_checkbox.stateChanged.connect(on_checkbox_changed)

        print(f"[标题过滤] 实时更新功能设置完成")

    except Exception as e:
        print(f"[标题过滤] 设置实时更新功能时出错: {e}")









