#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import json
import threading
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView, QLabel, QComboBox,
                            QPushButton, QFrame, QCheckBox, QLineEdit, QSpinBox,
                            QGroupBox, QFormLayout, QGridLayout, QSpacerItem, QSizePolicy,
                            QMessageBox, QScrollArea, QProgressDialog, QButtonGroup, QRadioButton,
                            QMenu, QInputDialog)
from PyQt5.QtGui import QIcon, QColor, QFont, QPalette, QPainter, QPainterPath, QPen
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QThread, pyqtSignal as Signal

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入快手API
from tool.快手cokie_api import KuaishouCookieAPI

class UnsetProductsLoadThread(QThread):
    """未设置商品加载线程"""

    # 定义信号
    progress_updated = Signal(str)  # 进度更新信号
    page_loaded = Signal(list, str, int, int)  # 页面数据加载信号 (商品列表, 店铺名称, 当前页, 总页数)
    total_info = Signal(int, str)  # 总数信息信号 (总商品数, 店铺名称)
    data_loaded = Signal(list, str)  # 数据加载完成信号 (商品列表, 店铺名称)
    error_occurred = Signal(str)  # 错误信号

    def __init__(self, shop_name, shop_id):
        super().__init__()
        self.shop_name = shop_name
        self.shop_id = shop_id

    def run(self):
        """运行线程"""
        try:
            self.progress_updated.emit(f"正在连接店铺 {self.shop_name}...")

            # 初始化API
            api = KuaishouCookieAPI(shop_id=self.shop_id)

            # 验证cookies
            if not api.validate_cookies():
                self.error_occurred.emit(f"店铺 {self.shop_name} 的Cookie已过期，请重新登录")
                return

            self.progress_updated.emit(f"正在获取店铺 {self.shop_name} 的未设置商品...")

            # 先获取第一页来获取总数
            first_result = api.get_plan_unset_products(page=1, limit=50)

            if first_result.get('result') != 1:
                error_msg = first_result.get('error_msg', '未知错误')
                self.error_occurred.emit(f"获取店铺 {self.shop_name} 未设置商品失败: {error_msg}")
                return

            total_count = first_result.get('total', 0)
            first_page_products = first_result.get('data', [])

            # 发送总数信息
            self.total_info.emit(total_count, self.shop_name)

            if total_count == 0:
                self.data_loaded.emit([], self.shop_name)
                return

            # 计算总页数
            total_pages = (total_count + 49) // 50  # 向上取整

            # 发送第一页数据
            self.page_loaded.emit(first_page_products, self.shop_name, 1, total_pages)

            # 如果有多页，继续获取其他页
            all_products = first_page_products.copy()

            for page in range(2, total_pages + 1):
                self.progress_updated.emit(f"正在加载第 {page}/{total_pages} 页商品...")

                result = api.get_plan_unset_products(page=page, limit=50)

                if result.get('result') == 1:
                    page_products = result.get('data', [])
                    all_products.extend(page_products)

                    # 发送页面数据
                    self.page_loaded.emit(page_products, self.shop_name, page, total_pages)
                else:
                    # 如果某页失败，记录错误但继续
                    error_msg = result.get('error_msg', '未知错误')
                    self.progress_updated.emit(f"第 {page} 页加载失败: {error_msg}")

            # 发送完成信号
            self.progress_updated.emit(f"店铺 {self.shop_name} 加载完成，共 {len(all_products)} 个未设置商品")
            self.data_loaded.emit(all_products, self.shop_name)

        except Exception as e:
            self.error_occurred.emit(f"加载店铺 {self.shop_name} 未设置商品时发生异常: {str(e)}")

class SingleShopLoadThread(QThread):
    """单个店铺加载线程"""

    # 定义信号
    shop_completed = Signal(list, str)  # 店铺完成信号 (商品列表, 店铺名称)
    shop_failed = Signal(str, str)  # 店铺失败信号 (店铺名称, 错误信息)

    def __init__(self, shop_info):
        super().__init__()
        self.shop_info = shop_info

    def run(self):
        """运行线程"""
        shop_name = self.shop_info['shop_name']
        shop_id = self.shop_info['shop_id']

        try:
            # 初始化API
            api = KuaishouCookieAPI(shop_id=shop_id)

            # 验证cookies
            if not api.validate_cookies():
                self.shop_failed.emit(shop_name, "Cookie已过期")
                return

            # 获取未设置商品
            result = api.get_all_plan_unset_products(limit=50)

            if result.get('result') == 1:
                products = result.get('data', [])
                self.shop_completed.emit(products, shop_name)
            else:
                error_msg = result.get('error_msg', '未知错误')
                self.shop_failed.emit(shop_name, error_msg)

        except Exception as e:
            self.shop_failed.emit(shop_name, str(e))

class AllShopsUnsetProductsLoadThread(QThread):
    """全部店铺未设置商品并发加载线程"""

    # 定义信号
    progress_updated = Signal(str)  # 进度更新信号
    shop_completed = Signal(list, str)  # 单个店铺完成信号 (商品列表, 店铺名称)
    all_completed = Signal(int, list, list)  # 全部完成信号 (总商品数, Cookie过期店铺列表, 其他错误店铺列表)
    error_occurred = Signal(str)  # 错误信号

    def __init__(self, valid_shops, max_concurrent=10):
        super().__init__()
        self.valid_shops = valid_shops
        self.max_concurrent = min(max_concurrent, 20)  # 最大并发数，限制最高20个
        self.completed_count = 0
        self.total_products = 0
        self.shop_threads = []
        self.cookie_expired_shops = []  # Cookie过期的店铺
        self.other_error_shops = []  # 其他错误的店铺

    def run(self):
        """运行线程"""
        try:
            total_shops = len(self.valid_shops)
            self.progress_updated.emit(f"开始并发处理 {total_shops} 个店铺（最大并发数: {self.max_concurrent}）...")

            # 分批处理店铺
            for i in range(0, total_shops, self.max_concurrent):
                batch = self.valid_shops[i:i + self.max_concurrent]
                batch_threads = []

                self.progress_updated.emit(f"启动第 {i//self.max_concurrent + 1} 批次，处理 {len(batch)} 个店铺...")

                # 创建并启动当前批次的线程
                for shop_info in batch:
                    thread = SingleShopLoadThread(shop_info)
                    thread.shop_completed.connect(self.on_single_shop_completed)
                    thread.shop_failed.connect(self.on_single_shop_failed)
                    batch_threads.append(thread)
                    thread.start()

                # 等待当前批次完成
                for thread in batch_threads:
                    thread.wait()  # 等待线程完成

                self.progress_updated.emit(f"第 {i//self.max_concurrent + 1} 批次完成")

            self.progress_updated.emit(f"全部店铺处理完成，共获取 {self.total_products} 个未设置商品")
            self.all_completed.emit(self.total_products, self.cookie_expired_shops, self.other_error_shops)

        except Exception as e:
            self.error_occurred.emit(f"并发处理店铺时发生异常: {str(e)}")

    def on_single_shop_completed(self, products, shop_name):
        """单个店铺完成回调"""
        self.completed_count += 1
        if products:
            self.total_products += len(products)
            self.progress_updated.emit(f"店铺 {shop_name} 完成，获取 {len(products)} 个商品 ({self.completed_count}/{len(self.valid_shops)})")
            self.shop_completed.emit(products, shop_name)
        else:
            self.progress_updated.emit(f"店铺 {shop_name} 完成，无未设置商品 ({self.completed_count}/{len(self.valid_shops)})")

    def on_single_shop_failed(self, shop_name, error_msg):
        """单个店铺失败回调"""
        self.completed_count += 1

        # 分类收集错误
        if "Cookie已过期" in error_msg or "登录失效" in error_msg or "重新登录" in error_msg:
            self.cookie_expired_shops.append(shop_name)
            self.progress_updated.emit(f"店铺 {shop_name} Cookie过期 ({self.completed_count}/{len(self.valid_shops)})")
        else:
            self.other_error_shops.append(f"{shop_name}: {error_msg}")
            self.progress_updated.emit(f"店铺 {shop_name} 失败: {error_msg} ({self.completed_count}/{len(self.valid_shops)})")

class PromotingProductsLoadThread(QThread):
    """推广中商品加载线程"""

    # 定义信号
    progress_updated = Signal(str)  # 进度更新信号
    page_loaded = Signal(list, str, int, int)  # 页面数据加载信号 (商品列表, 店铺名称, 当前已加载数, 总数)
    data_loaded = Signal(list, str)  # 数据加载完成信号 (商品列表, 店铺名称)
    error_occurred = Signal(str)  # 错误信号

    def __init__(self, shop_name, shop_id):
        super().__init__()
        self.shop_name = shop_name
        self.shop_id = shop_id

    def run(self):
        """运行线程"""
        try:
            # 创建API实例
            api = KuaishouCookieAPI(shop_id=self.shop_id)

            # 验证cookies
            if not api.validate_cookies():
                self.error_occurred.emit(f"店铺 {self.shop_name} 的Cookie已过期，请重新登录")
                return

            self.progress_updated.emit(f"正在获取店铺 {self.shop_name} 的推广中商品...")

            # 先获取第一页来获取总数
            first_result = api.get_plan_promoting_products(page=1, limit=50)

            if first_result.get('result') != 1:
                error_msg = first_result.get('error_msg', '未知错误')
                self.error_occurred.emit(f"获取店铺 {self.shop_name} 推广中商品失败: {error_msg}")
                return

            total_count = first_result.get('total', 0)
            first_page_products = first_result.get('data', [])

            if total_count == 0:
                self.progress_updated.emit(f"店铺 {self.shop_name} 没有推广中商品")
                self.data_loaded.emit([], self.shop_name)
                return

            # 发送第一页数据
            self.page_loaded.emit(first_page_products, self.shop_name, len(first_page_products), total_count)

            all_products = first_page_products.copy()

            # 如果还有更多页，继续获取
            if total_count > len(first_page_products):
                page = 2
                while len(all_products) < total_count:
                    self.progress_updated.emit(f"正在获取第 {page} 页数据... ({len(all_products)}/{total_count})")

                    result = api.get_plan_promoting_products(page=page, limit=50)

                    if result.get('result') == 1:
                        page_products = result.get('data', [])
                        if not page_products:
                            break

                        all_products.extend(page_products)
                        # 发送页面数据，实时显示
                        self.page_loaded.emit(page_products, self.shop_name, len(all_products), total_count)
                        page += 1
                    else:
                        # 如果某页失败，记录错误但继续
                        error_msg = result.get('error_msg', '未知错误')
                        self.progress_updated.emit(f"第 {page} 页加载失败: {error_msg}")
                        break

            # 发送完成信号
            self.progress_updated.emit(f"店铺 {self.shop_name} 加载完成，共 {len(all_products)} 个推广中商品")
            self.data_loaded.emit(all_products, self.shop_name)

        except Exception as e:
            self.error_occurred.emit(f"加载店铺 {self.shop_name} 推广中商品时发生异常: {str(e)}")

class OfflineProductsLoadThread(QThread):
    """已下架推广商品加载线程"""

    # 定义信号
    progress_updated = Signal(str)  # 进度更新信号
    page_loaded = Signal(list, str, int, int)  # 页面数据加载信号 (商品列表, 店铺名称, 当前已加载数, 总数)
    data_loaded = Signal(list, str)  # 数据加载完成信号 (商品列表, 店铺名称)
    error_occurred = Signal(str)  # 错误信号

    def __init__(self, shop_name, shop_id):
        super().__init__()
        self.shop_name = shop_name
        self.shop_id = shop_id

    def run(self):
        """运行线程"""
        try:
            # 创建API实例
            api = KuaishouCookieAPI(shop_id=self.shop_id)

            # 验证cookies
            if not api.validate_cookies():
                self.error_occurred.emit(f"店铺 {self.shop_name} 的Cookie已过期，请重新登录")
                return

            self.progress_updated.emit(f"正在获取店铺 {self.shop_name} 的已下架推广商品...")

            # 先获取第一页来获取总数
            first_result = api.get_plan_offline_products(page=1, limit=50)

            if first_result.get('result') != 1:
                error_msg = first_result.get('error_msg', '未知错误')
                self.error_occurred.emit(f"获取店铺 {self.shop_name} 已下架推广商品失败: {error_msg}")
                return

            total_count = first_result.get('total', 0)
            first_page_products = first_result.get('data', [])

            if total_count == 0:
                self.progress_updated.emit(f"店铺 {self.shop_name} 没有已下架推广商品")
                self.data_loaded.emit([], self.shop_name)
                return

            # 发送第一页数据
            self.page_loaded.emit(first_page_products, self.shop_name, len(first_page_products), total_count)

            all_products = first_page_products.copy()

            # 如果还有更多页，继续获取
            if total_count > len(first_page_products):
                page = 2
                while len(all_products) < total_count:
                    self.progress_updated.emit(f"正在获取第 {page} 页数据... ({len(all_products)}/{total_count})")

                    result = api.get_plan_offline_products(page=page, limit=50)

                    if result.get('result') == 1:
                        page_products = result.get('data', [])
                        if not page_products:
                            break

                        all_products.extend(page_products)
                        # 发送页面数据，实时显示
                        self.page_loaded.emit(page_products, self.shop_name, len(all_products), total_count)
                        page += 1
                    else:
                        # 如果某页失败，记录错误但继续
                        error_msg = result.get('error_msg', '未知错误')
                        self.progress_updated.emit(f"第 {page} 页加载失败: {error_msg}")
                        break

            # 发送完成信号
            self.progress_updated.emit(f"店铺 {self.shop_name} 加载完成，共 {len(all_products)} 个已下架推广商品")
            self.data_loaded.emit(all_products, self.shop_name)

        except Exception as e:
            self.error_occurred.emit(f"加载店铺 {self.shop_name} 已下架推广商品时发生异常: {str(e)}")

class CommissionModificationThread(QThread):
    """佣金修改线程"""

    # 定义信号
    progress_updated = Signal(str)  # 进度更新信号
    product_updated = Signal(int, int, object)  # 单个商品更新信号 (行号, 新佣金, 状态: None=处理中, True=成功, False=失败)
    all_completed = Signal(int, int)  # 全部完成信号 (成功数, 失败数)
    error_occurred = Signal(str)  # 错误信号

    def __init__(self, products, commission_rate, parent_window):
        super().__init__()
        self.products = products
        self.commission_rate = commission_rate
        self.parent_window = parent_window

    def run(self):
        """运行线程"""
        try:
            success_count = 0
            failed_count = 0

            # 按店铺分组商品
            shop_products = {}
            for product in self.products:
                shop_name = product['shop']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(product)

            total_products = len(self.products)
            processed_count = 0

            for shop_name, products in shop_products.items():
                self.progress_updated.emit(f"正在处理店铺 {shop_name} 的 {len(products)} 个商品...")

                try:
                    # 获取店铺的access_token
                    access_token = self.parent_window.get_shop_access_token(shop_name)
                    if not access_token:
                        # 店铺没有access_token，标记所有商品为失败
                        for product in products:
                            failed_count += 1
                            processed_count += 1
                            self.product_updated.emit(product['row'], self.commission_rate, False)
                            self.progress_updated.emit(f"处理进度: {processed_count}/{total_products}")
                        continue

                    # 创建API实例
                    from tool.快手api import KuaishouAPI
                    api = KuaishouAPI(access_token=access_token)

                    # 处理每个商品
                    for product in products:
                        try:
                            print(f"[佣金调试] 开始处理商品: 行={product['row']}, 商品ID={product.get('product_id')}")
                            # 发送开始处理信号，显示"设置中..."状态
                            print(f"[佣金调试] 发送开始处理信号: 行={product['row']}, 佣金={self.commission_rate}")
                            self.product_updated.emit(product['row'], self.commission_rate, None)  # None表示正在处理

                            # 获取商品的分销计划ID
                            product_id = product.get('product_id')
                            if not product_id:
                                failed_count += 1
                                processed_count += 1
                                self.product_updated.emit(product['row'], self.commission_rate, False)
                                self.progress_updated.emit(f"处理进度: {processed_count}/{total_products}")
                                continue

                            # 调用真实的佣金修改API
                            try:
                                # 将佣金比例转换为API需要的格式（乘以10）
                                commission_rate_api = int(self.commission_rate * 10)

                                # 调用修改佣金API
                                result = api.update_distribution_plan_commission(
                                    product_id=product_id,
                                    commission_rate=commission_rate_api
                                )

                                if result and result.get('result') == 1:
                                    success_count += 1
                                    print(f"[佣金调试] 发送成功信号: 行={product['row']}, 佣金={self.commission_rate}")
                                    self.product_updated.emit(product['row'], self.commission_rate, True)
                                else:
                                    failed_count += 1
                                    error_msg = result.get('error_msg', '未知错误') if result else '网络错误'
                                    print(f"[佣金调试] 发送失败信号: 行={product['row']}, 佣金={self.commission_rate}")
                                    self.product_updated.emit(product['row'], self.commission_rate, False)
                                    print(f"商品 {product_id} 佣金修改失败: {error_msg}")

                            except Exception as api_error:
                                failed_count += 1
                                self.product_updated.emit(product['row'], self.commission_rate, False)
                                print(f"商品 {product_id} 佣金修改API调用异常: {str(api_error)}")

                            processed_count += 1
                            self.progress_updated.emit(f"处理进度: {processed_count}/{total_products}")

                        except Exception as e:
                            failed_count += 1
                            processed_count += 1
                            self.product_updated.emit(product['row'], self.commission_rate, False)
                            self.progress_updated.emit(f"处理进度: {processed_count}/{total_products}")
                            print(f"处理商品 {product['item_id']} 失败: {str(e)}")

                except Exception as e:
                    # 店铺处理失败，标记所有商品为失败
                    for product in products:
                        failed_count += 1
                        processed_count += 1
                        self.product_updated.emit(product['row'], self.commission_rate, False)
                        self.progress_updated.emit(f"处理进度: {processed_count}/{total_products}")
                    print(f"处理店铺 {shop_name} 失败: {str(e)}")

            # 发送完成信号
            self.all_completed.emit(success_count, failed_count)

        except Exception as e:
            self.error_occurred.emit(f"佣金修改异常: {str(e)}")

class ProductSearchThread(QThread):
    """商品搜索线程"""

    # 定义信号
    progress_updated = Signal(str)  # 进度更新信号
    data_loaded = Signal(list, str, str)  # 数据加载完成信号 (商品列表, 店铺名称, 搜索关键词)
    error_occurred = Signal(str)  # 错误信号

    def __init__(self, shop_name, shop_id, search_text):
        super().__init__()
        self.shop_name = shop_name
        self.shop_id = shop_id
        self.search_text = search_text

    def run(self):
        """运行线程"""
        try:
            # 创建API实例
            from tool.快手cokie_api import KuaishouCookieAPI
            api = KuaishouCookieAPI(shop_id=self.shop_id)

            # 验证cookies
            if not api.validate_cookies():
                self.error_occurred.emit(f"店铺 {self.shop_name} 的Cookie已过期，请重新登录")
                return

            self.progress_updated.emit(f"正在搜索商品...")

            # 判断搜索类型：如果是纯数字，按商品ID搜索；否则按商品标题搜索
            if self.search_text.isdigit():
                # 按商品ID搜索
                self.progress_updated.emit(f"按商品ID搜索: {self.search_text}")
                result = api.search_plan_by_item_id(self.search_text, status=1, limit=50)
            else:
                # 按商品标题搜索
                self.progress_updated.emit(f"按商品标题搜索: {self.search_text}")
                result = api.search_plan_by_item_name(self.search_text, status=1, limit=50)

            if result.get('result') != 1:
                error_msg = result.get('error_msg', '未知错误')
                self.error_occurred.emit(f"搜索失败: {error_msg}")
                return

            products = result.get('data', [])
            total_count = result.get('total', 0)

            if total_count == 0:
                self.progress_updated.emit(f"未找到匹配的商品")
                self.data_loaded.emit([], self.shop_name, self.search_text)
                return

            # 发送搜索结果
            self.progress_updated.emit(f"搜索完成，找到 {len(products)} 个商品")
            self.data_loaded.emit(products, self.shop_name, self.search_text)

        except Exception as e:
            self.error_occurred.emit(f"搜索商品时发生异常: {str(e)}")

class AllShopsSearchThread(QThread):
    """全店铺搜索线程"""

    # 定义信号
    progress_updated = Signal(str)  # 进度更新信号
    data_loaded = Signal(list, str, str)  # 数据加载完成信号 (商品列表, 店铺名称, 搜索关键词)
    error_occurred = Signal(str)  # 错误信号

    def __init__(self, search_text, parent_window):
        super().__init__()
        self.search_text = search_text
        self.parent_window = parent_window

    def run(self):
        """运行线程"""
        try:
            # 从配置文件读取所有店铺信息
            import json
            import os
            from concurrent.futures import ThreadPoolExecutor, as_completed
            import threading

            config_path = get_config_path('账号管理.json')
            if not os.path.exists(config_path):
                self.error_occurred.emit(f"配置文件 {config_path} 不存在")
                return

            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 获取店铺数据，兼容不同的配置文件格式
            if isinstance(data, dict):
                config_data = data.get('data', [])
            elif isinstance(data, list):
                config_data = data
            else:
                self.error_occurred.emit("配置文件格式错误：不是有效的JSON格式")
                return

            if not isinstance(config_data, list):
                self.error_occurred.emit("配置文件格式错误：店铺数据不是列表格式")
                return

            # 过滤有效的店铺
            valid_shops = []
            for shop_config in config_data:
                shop_name = shop_config.get('店铺名称', '')
                shop_id = shop_config.get('店铺ID', '')
                if shop_name and shop_id:
                    valid_shops.append({
                        'name': shop_name,
                        'id': shop_id
                    })

            total_shops = len(valid_shops)
            if total_shops == 0:
                self.progress_updated.emit("没有找到有效的店铺")
                self.data_loaded.emit([], "所有店铺", self.search_text)
                return

            self.progress_updated.emit(f"开始并发搜索，共 {total_shops} 个店铺，最多20个并发")

            all_results = []
            processed_shops = 0
            results_lock = threading.Lock()

            def search_single_shop(shop_info):
                """搜索单个店铺"""
                shop_name = shop_info['name']
                shop_id = shop_info['id']

                try:
                    # 创建API实例
                    from tool.快手cokie_api import KuaishouCookieAPI
                    api = KuaishouCookieAPI(shop_id=shop_id)

                    # 验证cookies
                    if not api.validate_cookies():
                        return {
                            'shop_name': shop_name,
                            'success': False,
                            'message': 'Cookie已过期',
                            'products': []
                        }

                    # 判断搜索类型：如果是纯数字，按商品ID搜索；否则按商品标题搜索
                    if self.search_text.isdigit():
                        # 按商品ID搜索
                        result = api.search_plan_by_item_id(self.search_text, status=1, limit=50)
                    else:
                        # 按商品标题搜索
                        result = api.search_plan_by_item_name(self.search_text, status=1, limit=50)

                    if result.get('result') == 1:
                        products = result.get('data', [])
                        # 为每个商品添加店铺信息
                        for product in products:
                            product['shop_name'] = shop_name

                        return {
                            'shop_name': shop_name,
                            'success': True,
                            'message': f'找到 {len(products)} 个商品',
                            'products': products
                        }
                    else:
                        error_msg = result.get('error_msg', '未知错误')
                        return {
                            'shop_name': shop_name,
                            'success': False,
                            'message': f'搜索失败: {error_msg}',
                            'products': []
                        }

                except Exception as e:
                    return {
                        'shop_name': shop_name,
                        'success': False,
                        'message': f'搜索异常: {str(e)}',
                        'products': []
                    }

            # 使用线程池进行并发搜索，最多20个并发
            with ThreadPoolExecutor(max_workers=20) as executor:
                # 提交所有搜索任务
                future_to_shop = {executor.submit(search_single_shop, shop): shop for shop in valid_shops}

                # 处理完成的任务
                for future in as_completed(future_to_shop):
                    try:
                        result = future.result()

                        with results_lock:
                            processed_shops += 1

                            # 添加搜索结果
                            if result['success'] and result['products']:
                                all_results.extend(result['products'])

                            # 更新进度
                            progress_percent = (processed_shops / total_shops) * 100
                            self.progress_updated.emit(
                                f"店铺 {result['shop_name']}: {result['message']} "
                                f"({processed_shops}/{total_shops}, {progress_percent:.1f}%)"
                            )

                    except Exception as e:
                        with results_lock:
                            processed_shops += 1
                            shop_info = future_to_shop[future]
                            self.progress_updated.emit(f"店铺 {shop_info['name']} 处理异常: {str(e)}")

            # 发送搜索结果
            if all_results:
                self.progress_updated.emit(f"并发搜索完成，共找到 {len(all_results)} 个商品")
                self.data_loaded.emit(all_results, "所有店铺", self.search_text)
            else:
                self.progress_updated.emit(f"并发搜索完成，未找到匹配的商品")
                self.data_loaded.emit([], "所有店铺", self.search_text)

        except Exception as e:
            self.error_occurred.emit(f"并发搜索时发生异常: {str(e)}")

class SampleAndPromotionThread(QThread):
    """申样设置和分销计划创建线程"""

    # 定义信号
    progress_updated = Signal(str)  # 进度更新信号
    sample_completed = Signal(int, int)  # 申样完成信号 (成功数, 失败数)
    promotion_completed = Signal(int, int)  # 推广完成信号 (成功数, 失败数)
    all_completed = Signal(int, int, int, int)  # 全部完成信号 (申样成功, 申样失败, 推广成功, 推广失败)
    error_occurred = Signal(str)  # 错误信号

    def __init__(self, all_products, price_ranges, parent_window):
        super().__init__()
        self.all_products = all_products
        self.price_ranges = price_ranges
        self.parent_window = parent_window

    def run(self):
        """运行线程"""
        try:
            # 第一步：设置申样规则
            self.progress_updated.emit("第1步：正在设置申样规则...")
            sample_success, sample_failed = self.process_sample_setting()

            self.sample_completed.emit(sample_success, sample_failed)

            if sample_success == 0:
                self.error_occurred.emit("申样设置全部失败，无法创建分销计划")
                return

            # 第二步：创建分销计划
            self.progress_updated.emit("第2步：正在创建分销计划...")
            promotion_success, promotion_failed = self.process_promotion_creation()

            self.promotion_completed.emit(promotion_success, promotion_failed)
            self.all_completed.emit(sample_success, sample_failed, promotion_success, promotion_failed)

        except Exception as e:
            self.error_occurred.emit(f"处理异常: {str(e)}")

    def process_sample_setting(self):
        """处理申样设置"""
        try:
            # 按店铺分组商品
            shop_products = {}
            for product in self.all_products:
                shop_name = product['shop']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(product)

            total_success = 0
            total_failed = 0
            sample_count = 10  # 默认10个申样

            # 为每个店铺设置申样规则
            for i, (shop_name, products) in enumerate(shop_products.items()):
                self.progress_updated.emit(f"第1步：正在处理店铺申样 {shop_name} ({i+1}/{len(shop_products)})")

                try:
                    # 获取店铺的access_token
                    access_token = self.parent_window.get_shop_access_token(shop_name)
                    if not access_token:
                        total_failed += len(products)
                        continue

                    # 构建申样规则数据
                    item_rules = []
                    for product in products:
                        item_rules.append({
                            'item_id': product['item_id'],
                            'sample_count': sample_count
                        })

                    # 分批处理（每批最多20个商品）
                    batch_size = 20
                    from tool.快手api import KuaishouAPI
                    api = KuaishouAPI(access_token=access_token)

                    for batch_start in range(0, len(item_rules), batch_size):
                        batch_end = min(batch_start + batch_size, len(item_rules))
                        batch_rules = item_rules[batch_start:batch_end]

                        result = api.save_sample_rule(batch_rules)

                        if result.get('success'):
                            batch_success = result.get('data', {}).get('success_count', 0)
                            total_success += batch_success
                            total_failed += len(batch_rules) - batch_success
                        else:
                            total_failed += len(batch_rules)

                except Exception as e:
                    total_failed += len(products)
                    print(f"店铺 {shop_name} 申样设置异常: {str(e)}")

            return total_success, total_failed

        except Exception as e:
            print(f"申样设置处理异常: {str(e)}")
            return 0, len(self.all_products)

    def process_promotion_creation(self):
        """处理分销计划创建"""
        try:
            # 按店铺分组商品
            shop_products = {}
            for product in self.all_products:
                shop_name = product['shop']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(product)

            total_success = 0
            total_failed = 0

            # 为每个店铺创建分销计划
            for i, (shop_name, products) in enumerate(shop_products.items()):
                self.progress_updated.emit(f"第2步：正在处理店铺推广 {shop_name} ({i+1}/{len(shop_products)})")

                try:
                    # 获取店铺的access_token
                    access_token = self.parent_window.get_shop_access_token(shop_name)
                    if not access_token:
                        total_failed += len(products)
                        continue

                    # 构建分销计划数据
                    item_plans = []
                    for product in products:
                        # 获取商品价格（使用表头名索引）
                        price_text = self.parent_window.get_table_cell_by_header(product['row'], "价格")
                        price = price_text.replace('¥', '').replace(',', '').strip() if price_text else '0'

                        # 根据价格获取佣金比例
                        commission_rate = self.parent_window.get_commission_rate_by_price(price, self.price_ranges)

                        item_plans.append({
                            'item_id': product['item_id'],
                            'commission_rate': commission_rate
                        })

                    # 调用快手API创建分销计划
                    from tool.快手api import KuaishouAPI
                    api = KuaishouAPI(access_token=access_token)
                    result = api.create_distribution_plan(item_plans, "ITEM_NORMAL")

                    if result.get('success'):
                        success_count = result.get('data', {}).get('total_success', 0)
                        failed_count = result.get('data', {}).get('total_failed', 0)
                        total_success += success_count
                        total_failed += failed_count

                        # 更新商品的推广状态
                        self.update_promotion_status(products, success_count, failed_count)
                    else:
                        total_failed += len(products)
                        # 更新失败商品的状态
                        for product in products:
                            self.update_promotion_status_failed(product['row'])

                except Exception as e:
                    total_failed += len(products)
                    print(f"店铺 {shop_name} 推广创建异常: {str(e)}")

            return total_success, total_failed

        except Exception as e:
            print(f"推广创建处理异常: {str(e)}")
            return 0, len(self.all_products)

    def update_promotion_status(self, products, success_count, failed_count):
        """更新推广状态"""
        try:
            for i, product in enumerate(products):
                if i < success_count:
                    # 推广创建成功，在推广状态列显示"推广中"
                    status_text = "推广中"
                    self.parent_window.update_table_cell_by_header(product['row'], "推广状态", status_text, QColor(0, 128, 0))
                else:
                    status_text = "失败"
                    self.parent_window.update_table_cell_by_header(product['row'], "推广状态", status_text, QColor(255, 0, 0))
        except Exception as e:
            print(f"更新推广状态失败: {str(e)}")

    def update_promotion_status_failed(self, row):
        """更新推广失败状态"""
        try:
            self.parent_window.update_table_cell_by_header(row, "推广状态", "失败", QColor(255, 0, 0))
        except Exception as e:
            print(f"更新推广失败状态失败: {str(e)}")

def get_config_path(relative_path):
    """
    获取配置文件的绝对路径，确保使用exe同目录的config

    参数:
        relative_path (str): 相对于config目录的文件路径

    返回:
        str: 配置文件的绝对路径
    """
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        app_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境
        app_dir = os.path.dirname(os.path.abspath(__file__))

    config_path = os.path.join(app_dir, 'config', relative_path)
    return config_path

class PlanManager(QWidget):
    """计划管理界面"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setFixedSize(1498, 708)  # 设置窗口大小
        self.initUI()
        
    def initUI(self):
        """初始化界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 顶部搜索区域
        self.create_top_search_area(main_layout)
        
        # 中间内容区域（表格 + 右侧功能区）
        content_layout = QHBoxLayout()
        
        # 左侧表格区域
        self.create_table_area(content_layout)
        
        # 右侧功能区域
        self.create_right_function_area(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # 底部按钮区域
        self.create_bottom_buttons(main_layout)
        
    def create_top_search_area(self, main_layout):
        """创建顶部搜索区域"""
        top_frame = QFrame()
        top_frame.setFixedHeight(50)
        top_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
            }
        """)
        
        top_layout = QHBoxLayout(top_frame)
        top_layout.setContentsMargins(15, 10, 15, 10)
        top_layout.setSpacing(15)
        
        # 商品ID搜索
        product_label = QLabel("商品ID")
        product_label.setStyleSheet("font-weight: bold; color: #333;")
        self.product_input = QLineEdit()
        self.product_input.setPlaceholderText("输入商品ID进行搜索")
        self.product_input.setFixedWidth(200)
        self.product_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 5px;
                font-size: 12px;
            }
        """)
        
        product_search_btn = QPushButton("商品搜索")
        product_search_btn.setFixedSize(80, 30)
        product_search_btn.setStyleSheet("""
            QPushButton {
                background-color: #4e7ae7;
                color: white;
                border: none;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a6be0;
            }
        """)
        
        # 店铺选择
        shop_label = QLabel("店铺")
        shop_label.setStyleSheet("font-weight: bold; color: #333;")
        self.shop_combo = QComboBox()
        self.shop_combo.setFixedWidth(200)
        self.shop_combo.setEditable(True)  # 设置为可编辑，支持搜索
        self.shop_combo.setInsertPolicy(QComboBox.NoInsert)  # 不允许插入新项
        self.shop_combo.completer().setCompletionMode(self.shop_combo.completer().PopupCompletion)  # 设置自动完成模式
        self.shop_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 5px;
                font-size: 12px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
                background-color: transparent;
            }
            QComboBox::down-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 8px solid #666;
                margin-right: 8px;
            }
            QComboBox::down-arrow:hover {
                border-top-color: #4e7ae7;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #ddd;
                background-color: white;
                selection-background-color: #4e7ae7;
                selection-color: white;
                outline: none;
                font-size: 12px;
            }
            QComboBox QAbstractItemView::item {
                height: 35px;
                min-height: 35px;
                padding: 8px 12px;
                border-bottom: 1px solid #f0f0f0;
                background-color: white;
                color: #333;
                font-size: 12px;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #f0f8ff;
                color: #333;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #4e7ae7;
                color: white;
                font-weight: bold;
            }
        """)

        # 加载店铺选项
        self.load_shop_options()

        # 设置自定义视图（必须在设置样式之前）
        from PyQt5.QtWidgets import QListView
        self.shop_combo.setView(QListView())

        # 设置搜索筛选功能
        self.setup_shop_search()
        
        # 绑定商品搜索按钮点击事件
        product_search_btn.clicked.connect(self.search_products)

        # 添加到布局
        top_layout.addWidget(product_label)
        top_layout.addWidget(self.product_input)
        top_layout.addWidget(product_search_btn)
        top_layout.addSpacing(30)
        top_layout.addWidget(shop_label)
        top_layout.addWidget(self.shop_combo)
        top_layout.addStretch()
        
        main_layout.addWidget(top_frame)
        
    def create_table_area(self, content_layout):
        """创建表格区域"""
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
            }
        """)
        
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(10, 10, 10, 10)
        
        # 表格
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(8)
        self.table_widget.setHorizontalHeaderLabels([
            "选择", "商品ID", "商品标题", "价格", "佣金", "推广状态", "操作状态", "店铺"
        ])
        
        # 设置表格样式
        self.table_widget.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f9f9f9;
                selection-background-color: #3E5FAC;
                border: none;
            }
            QHeaderView::section {
                background-color: white;
                padding: 10px 8px;
                border: none;
                border-bottom: 2px solid #e0e0e0;
                border-right: 1px solid #e0e0e0;
                font-weight: bold;
                color: #333;
                font-size: 12px;
            }
            QHeaderView::section:last {
                border-right: none;
            }
            QTableWidget::item {
                border-bottom: 1px solid #f0f0f0;
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #E3F2FD;
                color: #1565C0;
                font-weight: bold;
            }
            QTableWidget::item:hover {
                background-color: #f0f4ff;
                border: 1px solid #3E5FAC;
            }
            QTableWidget QWidget {
                background: transparent;
                border: none;
                margin: 0px;
                padding: 0px;
            }
        """)
        
        # 设置表格属性
        self.table_widget.setAlternatingRowColors(True)
        self.table_widget.setSelectionBehavior(QTableWidget.SelectRows)
        self.table_widget.horizontalHeader().setStretchLastSection(True)

        # 隐藏垂直表头（序号列）
        self.table_widget.verticalHeader().setVisible(False)

        # 设置默认行高，确保复选框有足够空间
        self.table_widget.verticalHeader().setDefaultSectionSize(40)

        # 启用右键菜单
        self.table_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.table_widget.customContextMenuRequested.connect(self.show_context_menu)

        # 添加状态栏（在表格顶部）
        self.create_status_bar(table_layout)

        table_layout.addWidget(self.table_widget)

        content_layout.addWidget(table_frame, 3)  # 占3/4宽度

    def create_status_bar(self, table_layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setFixedHeight(30)
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-top: 1px solid #e0e0e0;
                border-radius: 0px;
            }
        """)

        status_layout = QHBoxLayout(status_frame)
        status_layout.setContentsMargins(10, 5, 10, 5)
        status_layout.setSpacing(10)

        # 左侧：商品加载进度（默认隐藏）
        self.progress_label = QLabel("商品加载进度：准备中...")
        self.progress_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
            }
        """)
        self.progress_label.hide()  # 默认隐藏

        # 右侧：商品统计（显示表格实际数据）
        self.stats_label = QLabel("总商品：0 | 已选择商品：0")
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #333;
                font-size: 12px;
                font-weight: bold;
            }
        """)

        # 添加到布局
        status_layout.addWidget(self.progress_label)
        status_layout.addStretch()  # 弹性空间，将右侧内容推到右边
        status_layout.addWidget(self.stats_label)

        table_layout.addWidget(status_frame)

    def update_status_bar(self, message):
        """更新状态栏消息"""
        try:
            # 显示进度标签并更新消息
            self.progress_label.setText(message)
            self.progress_label.show()

            # 可选：设置定时器自动隐藏消息
            from PyQt5.QtCore import QTimer
            if not hasattr(self, 'status_timer'):
                self.status_timer = QTimer()
                self.status_timer.timeout.connect(self.hide_status_message)

            # 10秒后自动隐藏状态消息
            self.status_timer.start(10000)

        except Exception as e:
            print(f"更新状态栏失败: {str(e)}")

    def hide_status_message(self):
        """隐藏状态消息"""
        try:
            self.progress_label.hide()
            if hasattr(self, 'status_timer'):
                self.status_timer.stop()
        except Exception as e:
            print(f"隐藏状态消息失败: {str(e)}")

    def create_right_function_area(self, content_layout):
        """创建右侧功能区域"""
        right_frame = QFrame()
        right_frame.setFixedWidth(300)
        right_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
            }
        """)
        
        right_layout = QVBoxLayout(right_frame)
        right_layout.setContentsMargins(15, 15, 15, 15)
        right_layout.setSpacing(15)
        
        # 佣金筛选
        commission_group = QGroupBox()
        commission_layout = QVBoxLayout(commission_group)

        # 标题区域
        commission_title_layout = QHBoxLayout()
        commission_title = QLabel("佣金筛选")
        commission_title.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #333;
                font-weight: bold;
                border: none;
                background: transparent;
            }
        """)
        commission_desc = QLabel("(勾选佣金等于输入值的商品)")
        commission_desc.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        commission_title_layout.addWidget(commission_title)
        commission_title_layout.addWidget(commission_desc)
        commission_title_layout.addStretch()
        commission_layout.addLayout(commission_title_layout)

        commission_input_layout = QHBoxLayout()
        self.commission_input = QLineEdit()
        self.commission_input.setPlaceholderText("输入佣金值...")
        self.commission_input.setFixedWidth(120)
        self.commission_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)
        commission_filter_btn = QPushButton("佣金筛选")
        commission_filter_btn.setFixedWidth(80)  # 设置固定宽度
        commission_filter_btn.setStyleSheet("""
            QPushButton {
                background-color: #4e7ae7;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }
        """)

        commission_input_layout.addWidget(self.commission_input)
        commission_input_layout.addStretch()  # 添加弹性空间，将按钮推到右侧
        commission_input_layout.addWidget(commission_filter_btn)

        # 绑定佣金筛选按钮点击事件
        commission_filter_btn.clicked.connect(self.filter_by_commission)

        # 设置布局间距
        commission_input_layout.setSpacing(5)  # 设置统一间距

        commission_layout.addLayout(commission_input_layout)

        right_layout.addWidget(commission_group)

        # 价格筛选
        price_filter_group = QGroupBox()
        price_filter_layout = QVBoxLayout(price_filter_group)

        # 标题区域
        price_filter_title_layout = QHBoxLayout()
        price_filter_title = QLabel("价格筛选")
        price_filter_title.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #333;
                font-weight: bold;
                border: none;
                background: transparent;
            }
        """)
        price_filter_desc = QLabel("(勾选价格在区间内的商品)")
        price_filter_desc.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        price_filter_title_layout.addWidget(price_filter_title)
        price_filter_title_layout.addWidget(price_filter_desc)
        price_filter_title_layout.addStretch()
        price_filter_layout.addLayout(price_filter_title_layout)

        price_filter_input_layout = QHBoxLayout()
        self.price_min_input = QLineEdit()
        self.price_min_input.setPlaceholderText("最小")
        self.price_min_input.setFixedWidth(50)
        self.price_min_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)

        price_to_label = QLabel("至")
        price_to_label.setFixedWidth(20)  # 设置固定宽度
        price_to_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        price_to_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)

        self.price_max_input = QLineEdit()
        self.price_max_input.setPlaceholderText("最大")
        self.price_max_input.setFixedWidth(50)
        self.price_max_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)

        price_filter_btn = QPushButton("价格筛选")
        price_filter_btn.setFixedWidth(80)  # 设置固定宽度
        price_filter_btn.setStyleSheet("""
            QPushButton {
                background-color: #4e7ae7;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }
        """)

        price_filter_input_layout.addWidget(self.price_min_input)
        price_filter_input_layout.addWidget(price_to_label)
        price_filter_input_layout.addWidget(self.price_max_input)
        price_filter_input_layout.addStretch()  # 添加弹性空间，将按钮推到右侧
        price_filter_input_layout.addWidget(price_filter_btn)

        # 绑定价格筛选按钮点击事件
        price_filter_btn.clicked.connect(self.filter_by_price)

        # 设置布局间距
        price_filter_input_layout.setSpacing(5)  # 设置统一间距

        price_filter_layout.addLayout(price_filter_input_layout)

        right_layout.addWidget(price_filter_group)

        # 批量改佣金
        batch_commission_group = QGroupBox()
        batch_commission_layout = QVBoxLayout(batch_commission_group)

        # 标题区域
        batch_commission_title_layout = QHBoxLayout()
        batch_commission_title = QLabel("批量改佣金")
        batch_commission_title.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #333;
                font-weight: bold;
                border: none;
                background: transparent;
            }
        """)
        batch_commission_desc = QLabel("(为选中商品设置统一佣金)")
        batch_commission_desc.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        batch_commission_title_layout.addWidget(batch_commission_title)
        batch_commission_title_layout.addWidget(batch_commission_desc)
        batch_commission_title_layout.addStretch()
        batch_commission_layout.addLayout(batch_commission_title_layout)

        batch_commission_input_layout = QHBoxLayout()
        self.batch_commission_input = QLineEdit()
        self.batch_commission_input.setPlaceholderText("输入佣金值...")
        self.batch_commission_input.setFixedWidth(120)
        self.batch_commission_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)
        batch_commission_btn = QPushButton("批量改佣金")
        batch_commission_btn.setFixedWidth(80)  # 设置固定宽度
        batch_commission_btn.setStyleSheet("""
            QPushButton {
                background-color: #4e7ae7;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 5px 10px;
            }
        """)

        batch_commission_input_layout.addWidget(self.batch_commission_input)
        batch_commission_input_layout.addStretch()  # 添加弹性空间，将按钮推到右侧
        batch_commission_input_layout.addWidget(batch_commission_btn)

        # 绑定批量改佣金按钮点击事件
        batch_commission_btn.clicked.connect(self.batch_modify_commission)

        # 设置布局间距
        batch_commission_input_layout.setSpacing(5)  # 设置统一间距

        batch_commission_layout.addLayout(batch_commission_input_layout)

        right_layout.addWidget(batch_commission_group)

        # 价格范围设置
        price_range_group = QGroupBox()
        price_range_layout = QVBoxLayout(price_range_group)

        # 标题区域（带复选框）
        title_layout = QHBoxLayout()
        self.price_range_checkbox = QCheckBox("价格范围")
        self.price_range_checkbox.setStyleSheet("font-size: 12px; color: #333; font-weight: bold;")
        self.price_range_checkbox.setChecked(True)  # 默认勾选
        title_layout.addWidget(self.price_range_checkbox)
        title_layout.addStretch()
        price_range_layout.addLayout(title_layout)

        # 输入区域
        price_range_input_layout = QHBoxLayout()

        min_label = QLabel("最小")
        min_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        self.price_range_min_input = QLineEdit()
        self.price_range_min_input.setPlaceholderText("最小")
        self.price_range_min_input.setFixedWidth(50)
        self.price_range_min_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)

        max_label = QLabel("最大")
        max_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        self.price_range_max_input = QLineEdit()
        self.price_range_max_input.setPlaceholderText("最大")
        self.price_range_max_input.setFixedWidth(50)
        self.price_range_max_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)

        commission_label = QLabel("佣金")
        commission_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                border: none;
                background: transparent;
            }
        """)
        self.price_range_commission_input = QLineEdit()
        self.price_range_commission_input.setPlaceholderText("佣金")
        self.price_range_commission_input.setFixedWidth(50)
        self.price_range_commission_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4e7ae7;
            }
        """)

        price_range_input_layout.addWidget(min_label)
        price_range_input_layout.addWidget(self.price_range_min_input)
        price_range_input_layout.addWidget(max_label)
        price_range_input_layout.addWidget(self.price_range_max_input)
        price_range_input_layout.addWidget(commission_label)
        price_range_input_layout.addWidget(self.price_range_commission_input)

        # 设置布局间距
        price_range_input_layout.setSpacing(5)  # 设置统一间距

        price_range_layout.addLayout(price_range_input_layout)

        # 价格范围列表显示区域
        self.price_range_list_layout = QVBoxLayout()
        price_range_layout.addLayout(self.price_range_list_layout)

        # 加载已保存的价格范围
        self.load_price_ranges()

        # 底部添加按钮区域
        bottom_layout = QHBoxLayout()
        bottom_layout.addStretch()  # 左侧弹性空间，将按钮推到右下角

        add_range_btn = QPushButton("添加")
        add_range_btn.setFixedSize(50, 25)
        add_range_btn.setStyleSheet("""
            QPushButton {
                background-color: #4e7ae7;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 13px;
            }
        """)
        add_range_btn.clicked.connect(self.add_price_range)
        bottom_layout.addWidget(add_range_btn)

        price_range_layout.addLayout(bottom_layout)

        right_layout.addWidget(price_range_group)

        # 申样设置 - 保留区域边框
        sample_setting_group = QGroupBox()
        sample_setting_layout = QVBoxLayout(sample_setting_group)

        # 标题 - 无边框
        sample_title = QLabel("申样设置")
        sample_title.setStyleSheet("font-size: 12px; color: #333; font-weight: bold; margin-bottom: 5px; border: none;")
        sample_setting_layout.addWidget(sample_title)

        # 单选框和确认按钮在一行
        main_layout = QHBoxLayout()

        # 创建单选框组
        from PyQt5.QtWidgets import QButtonGroup, QRadioButton
        self.sample_button_group = QButtonGroup()

        # 5个申样
        self.sample_5_radio = QRadioButton("5")
        self.sample_5_radio.setStyleSheet("font-size: 12px; color: #333;")
        self.sample_button_group.addButton(self.sample_5_radio, 1)  # 使用简单的ID
        main_layout.addWidget(self.sample_5_radio)

        # 10个申样
        self.sample_10_radio = QRadioButton("10")
        self.sample_10_radio.setStyleSheet("font-size: 12px; color: #333;")
        self.sample_10_radio.setChecked(True)  # 默认选中10
        self.sample_button_group.addButton(self.sample_10_radio, 2)  # 使用简单的ID
        main_layout.addWidget(self.sample_10_radio)

        # 20个申样
        self.sample_20_radio = QRadioButton("20")
        self.sample_20_radio.setStyleSheet("font-size: 12px; color: #333;")
        self.sample_button_group.addButton(self.sample_20_radio, 3)  # 使用简单的ID
        main_layout.addWidget(self.sample_20_radio)

        # 自定义申样（无文字）
        self.sample_custom_radio = QRadioButton("")
        self.sample_custom_radio.setStyleSheet("font-size: 12px; color: #333;")
        self.sample_button_group.addButton(self.sample_custom_radio, 4)  # 使用简单的ID
        main_layout.addWidget(self.sample_custom_radio)

        # 自定义输入框（显示自定义提示）
        self.sample_custom_input = QLineEdit()
        self.sample_custom_input.setPlaceholderText("自定义")
        self.sample_custom_input.setFixedSize(60, 30)
        # 当输入框获得焦点或有内容时，自动选中自定义单选框
        self.sample_custom_input.textChanged.connect(self.on_custom_input_changed)
        self.sample_custom_input.focusInEvent = self.on_custom_input_focus
        self.sample_custom_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3E5FAC;
            }
        """)
        main_layout.addWidget(self.sample_custom_input)

        main_layout.addStretch()  # 弹性空间

        # 确认按钮
        self.sample_confirm_btn = QPushButton("确认")
        self.sample_confirm_btn.setFixedHeight(30)
        self.sample_confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #3E5FAC;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 3px 8px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4E7AE7;
            }
            QPushButton:pressed {
                background-color: #1E3F8C;
            }
        """)
        self.sample_confirm_btn.clicked.connect(self.apply_sample_setting)
        main_layout.addWidget(self.sample_confirm_btn)

        sample_setting_layout.addLayout(main_layout)

        right_layout.addWidget(sample_setting_group)

        right_layout.addStretch()
        content_layout.addWidget(right_frame, 1)  # 占1/4宽度
        
    def create_bottom_buttons(self, main_layout):
        """创建底部按钮区域"""
        bottom_frame = QFrame()
        bottom_frame.setFixedHeight(50)
        bottom_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 5px;
            }
        """)
        
        bottom_layout = QHBoxLayout(bottom_frame)
        bottom_layout.setContentsMargins(15, 10, 15, 10)
        bottom_layout.setSpacing(10)
        
        # 按钮列表
        buttons = [
            "全部商品", "未设置商品", "全部未设置商品",
            "已下架推广", "添加推广", "重新上架"
        ]

        # 存储按钮引用
        self.bottom_buttons = {}

        for btn_text in buttons:
            btn = QPushButton(btn_text)
            btn.setFixedHeight(30)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #f5f5f5;
                    color: #333;
                    border: 1px solid #ddd;
                    border-radius: 3px;
                    padding: 5px 15px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)

            # 为特定按钮添加点击事件
            if btn_text == "全部商品":
                btn.clicked.connect(self.load_promoting_products)
            elif btn_text == "已下架推广":
                btn.clicked.connect(self.load_offline_products)
            elif btn_text == "未设置商品":
                btn.clicked.connect(self.load_unset_products)
            elif btn_text == "全部未设置商品":
                btn.clicked.connect(self.load_all_shops_unset_products)
            elif btn_text == "添加推广":
                btn.clicked.connect(self.add_promotion)
            elif btn_text == "佣金筛选":
                btn.clicked.connect(self.filter_by_commission)
            elif btn_text == "价格筛选":
                btn.clicked.connect(self.filter_by_price)

            # 存储按钮引用
            self.bottom_buttons[btn_text] = btn
            bottom_layout.addWidget(btn)
        
        bottom_layout.addStretch()
        main_layout.addWidget(bottom_frame)

    def load_shop_options(self):
        """从配置文件加载店铺选项"""
        try:
            config_path = get_config_path('账号管理.json')

            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}")
                self.shop_combo.addItem("无可用店铺")
                return

            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 获取店铺数据
            shops_data = data.get('data', []) if isinstance(data, dict) else data

            # 清空现有选项
            self.shop_combo.clear()

            # 添加默认选项
            self.shop_combo.addItem("请选择店铺")
            self.shop_combo.addItem("全部店铺")

            # 添加店铺选项
            for shop in shops_data:
                if isinstance(shop, dict):
                    shop_name = shop.get('店铺名称', '').strip()
                    if shop_name:
                        self.shop_combo.addItem(shop_name)

            print(f"加载了 {self.shop_combo.count() - 1} 个店铺选项")

        except Exception as e:
            print(f"加载店铺选项失败: {str(e)}")
            self.shop_combo.clear()
            self.shop_combo.addItem("加载失败")

    def show_loading_progress(self, message="正在加载商品数据..."):
        """显示加载进度"""
        self.progress_label.setText(message)
        self.progress_label.show()

    def hide_loading_progress(self):
        """隐藏加载进度"""
        self.progress_label.hide()

    def update_stats(self, total_count=0, selected_count=0):
        """更新商品统计信息"""
        self.stats_label.setText(f"总商品：{total_count} | 已选择商品：{selected_count}")

    def get_table_stats(self):
        """获取表格统计信息"""
        total_count = self.table_widget.rowCount()
        selected_count = 0

        # 统计选中的复选框数量
        for row in range(total_count):
            select_widget = self.table_widget.cellWidget(row, 0)
            if select_widget:
                # 查找widget中的复选框
                checkbox = select_widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    selected_count += 1

        return total_count, selected_count

    def refresh_stats(self):
        """刷新统计信息"""
        total_count, selected_count = self.get_table_stats()
        self.update_stats(total_count, selected_count)

    def setup_shop_search(self):
        """设置店铺搜索功能"""
        try:
            # 使用Qt内置的自动完成功能，更稳定
            from PyQt5.QtWidgets import QCompleter
            from PyQt5.QtCore import Qt

            # 获取所有店铺名称
            shop_names = []
            for i in range(self.shop_combo.count()):
                shop_names.append(self.shop_combo.itemText(i))

            # 创建自动完成器
            completer = QCompleter(shop_names)
            completer.setFilterMode(Qt.MatchContains)  # 包含匹配
            completer.setCaseSensitivity(Qt.CaseInsensitive)  # 大小写不敏感

            # 设置自动完成器
            self.shop_combo.setCompleter(completer)

            # 连接选择变化信号
            self.shop_combo.currentTextChanged.connect(self.on_shop_selected)

        except Exception as e:
            print(f"设置店铺搜索功能时出错: {str(e)}")

    def on_shop_selected(self, text):
        """店铺选择事件处理"""
        try:
            if text and text != "请选择店铺":
                print(f"选择了店铺: {text}")
                # 这里可以添加店铺选择后的逻辑
        except Exception as e:
            print(f"处理店铺选择时出错: {str(e)}")

    def get_config_path(self):
        """获取配置文件路径"""
        # 使用统一的配置路径函数
        return get_config_path("config.json")

    def load_config(self):
        """加载配置文件"""
        try:
            import json
            config_path = self.get_config_path()
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            return {}

    def save_config(self, config):
        """保存配置文件（保留其他配置，只更新指定部分）"""
        try:
            import json
            config_path = self.get_config_path()

            # 先读取现有配置
            existing_config = {}
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    existing_config = json.load(f)

            # 合并配置（保留现有配置，只更新传入的部分）
            existing_config.update(config)

            # 保存合并后的配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(existing_config, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")
            return False

    def load_price_ranges(self):
        """加载价格范围列表"""
        try:
            config = self.load_config()
            price_ranges = config.get('price_ranges', [])

            # 清空现有列表
            self.clear_price_range_list()

            # 添加保存的价格范围
            for range_data in price_ranges:
                self.add_price_range_to_list(range_data)

        except Exception as e:
            print(f"加载价格范围失败: {str(e)}")

    def clear_price_range_list(self):
        """清空价格范围列表"""
        try:
            while self.price_range_list_layout.count():
                child = self.price_range_list_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
        except Exception as e:
            print(f"清空价格范围列表失败: {str(e)}")

    def add_price_range_to_list(self, range_data):
        """添加价格范围到列表显示"""
        try:
            min_price = range_data.get('min_price', '')
            max_price = range_data.get('max_price', '')
            commission = range_data.get('commission', '')

            range_text = f"{min_price}-{max_price}元: {commission}%"
            range_label = QLabel(range_text)
            range_label.setStyleSheet("font-size: 13px; color: #666; padding: 2px;")
            self.price_range_list_layout.addWidget(range_label)

        except Exception as e:
            print(f"添加价格范围到列表失败: {str(e)}")

    def add_price_range(self):
        """添加价格范围"""
        try:
            # 获取输入值
            min_price = self.price_range_min_input.text().strip()
            max_price = self.price_range_max_input.text().strip()
            commission = self.price_range_commission_input.text().strip()

            # 验证输入
            if not min_price or not max_price or not commission:
                QMessageBox.warning(self, "提示", "请填写完整的价格范围信息")
                return

            try:
                float(min_price)
                float(max_price)
                float(commission)
            except ValueError:
                QMessageBox.warning(self, "提示", "请输入有效的数字")
                return

            if float(min_price) >= float(max_price):
                QMessageBox.warning(self, "提示", "最小价格必须小于最大价格")
                return

            # 创建价格范围数据
            range_data = {
                'min_price': min_price,
                'max_price': max_price,
                'commission': commission
            }

            # 加载现有配置
            config = self.load_config()
            if 'price_ranges' not in config:
                config['price_ranges'] = []

            # 添加新的价格范围
            config['price_ranges'].append(range_data)

            # 只保存price_ranges部分，不影响其他配置
            price_ranges_config = {'price_ranges': config['price_ranges']}

            # 保存配置
            if self.save_config(price_ranges_config):
                # 添加到列表显示
                self.add_price_range_to_list(range_data)

                # 清空输入框
                self.price_range_min_input.clear()
                self.price_range_max_input.clear()
                self.price_range_commission_input.clear()

                QMessageBox.information(self, "成功", "价格范围添加成功")
            else:
                QMessageBox.warning(self, "错误", "保存配置失败")

        except Exception as e:
            print(f"添加价格范围失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"添加价格范围失败: {str(e)}")

    def apply_sample_setting(self):
        """应用申样设置"""
        try:
            # 获取选中的申样数量
            selected_button = self.sample_button_group.checkedButton()
            if not selected_button:
                QMessageBox.warning(self, "提示", "请选择申样数量")
                return

            button_id = self.sample_button_group.id(selected_button)

            print(f"调试：选中的按钮ID: {button_id}")  # 调试信息

            # 根据按钮ID确定申样数量
            if button_id == 1:  # 5个申样
                sample_count = 5
                print(f"调试：选择5个申样")
            elif button_id == 2:  # 10个申样
                sample_count = 10
                print(f"调试：选择10个申样")
            elif button_id == 3:  # 20个申样
                sample_count = 20
                print(f"调试：选择20个申样")
            elif button_id == 4:  # 自定义
                custom_value = self.sample_custom_input.text().strip()
                print(f"调试：自定义输入框的值: '{custom_value}'")  # 调试信息

                if not custom_value:
                    QMessageBox.warning(self, "提示", "请输入自定义申样数量")
                    return

                try:
                    sample_count = int(custom_value)
                    if sample_count <= 0:
                        QMessageBox.warning(self, "提示", "申样数量必须大于0")
                        return
                    print(f"调试：自定义申样数量: {sample_count}")  # 调试信息
                except ValueError:
                    QMessageBox.warning(self, "提示", "请输入有效的数字")
                    return
            else:
                QMessageBox.warning(self, "错误", f"未知的按钮ID: {button_id}")
                return

            # 获取表格中的所有商品（不用判断勾选）
            all_products = self.get_all_table_products()
            if not all_products:
                QMessageBox.warning(self, "提示", "表格中没有商品数据")
                return

            # 确认对话框
            reply = QMessageBox.question(
                self,
                "确认申样",
                f"将对表格中所有 {len(all_products)} 个商品进行申样设置\n申样数量：{sample_count}\n\n是否继续？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.process_sample_setting(all_products, sample_count)

        except Exception as e:
            print(f"应用申样设置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"应用申样设置失败: {str(e)}")

    def on_custom_input_changed(self, text):
        """当自定义输入框内容改变时，自动选中自定义单选框"""
        if text.strip():  # 如果输入框有内容
            self.sample_custom_radio.setChecked(True)

    def on_custom_input_focus(self, event):
        """当自定义输入框获得焦点时，自动选中自定义单选框"""
        self.sample_custom_radio.setChecked(True)
        # 调用原始的focusInEvent
        QLineEdit.focusInEvent(self.sample_custom_input, event)

    def get_selected_products(self):
        """获取选中的商品"""
        selected_products = []
        try:
            for row in range(self.table_widget.rowCount()):
                select_widget = self.table_widget.cellWidget(row, 0)
                if select_widget:
                    checkbox = select_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        # 获取商品信息（使用表头名索引）
                        product_info = {
                            'row': row,
                            'item_id': self.get_table_cell_by_header(row, "商品ID"),
                            'name': self.get_table_cell_by_header(row, "商品标题"),
                            'shop': self.get_table_cell_by_header(row, "店铺")
                        }
                        selected_products.append(product_info)
        except Exception as e:
            print(f"获取选中商品失败: {str(e)}")

        return selected_products

    def get_all_table_products(self):
        """获取表格中的所有商品"""
        all_products = []
        try:
            for row in range(self.table_widget.rowCount()):
                # 获取商品信息（使用表头名索引）
                product_info = {
                    'row': row,
                    'item_id': self.get_table_cell_by_header(row, "商品ID"),
                    'name': self.get_table_cell_by_header(row, "商品标题"),
                    'shop': self.get_table_cell_by_header(row, "店铺")
                }
                all_products.append(product_info)
        except Exception as e:
            print(f"获取表格商品失败: {str(e)}")

        return all_products

    def process_sample_setting(self, selected_products, sample_count):
        """处理申样设置"""
        try:
            # 按店铺分组商品
            shop_products = {}
            for product in selected_products:
                shop_name = product['shop']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(product)

            # 显示进度对话框
            progress_dialog = QProgressDialog("正在设置申样规则...", "取消", 0, len(shop_products), self)
            progress_dialog.setWindowTitle("申样设置进度")
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.show()

            total_success = 0
            total_failed = 0
            failed_shops = []

            # 为每个店铺设置申样规则
            for i, (shop_name, products) in enumerate(shop_products.items()):
                if progress_dialog.wasCanceled():
                    break

                progress_dialog.setLabelText(f"正在处理店铺: {shop_name}")
                progress_dialog.setValue(i)
                QApplication.processEvents()

                try:
                    # 获取店铺的access_token
                    access_token = self.get_shop_access_token(shop_name)
                    if not access_token:
                        failed_shops.append(f"{shop_name}: 缺少access_token")
                        total_failed += len(products)
                        continue

                    # 构建申样规则数据
                    item_rules = []
                    for product in products:
                        item_rules.append({
                            'item_id': product['item_id'],
                            'sample_count': sample_count
                        })

                    # 分批处理（每批最多20个商品）
                    batch_size = 20
                    shop_success = 0
                    shop_failed = 0

                    from tool.快手api import KuaishouAPI
                    api = KuaishouAPI(access_token=access_token)

                    for batch_start in range(0, len(item_rules), batch_size):
                        batch_end = min(batch_start + batch_size, len(item_rules))
                        batch_rules = item_rules[batch_start:batch_end]

                        progress_dialog.setLabelText(f"正在处理店铺: {shop_name} (第{batch_start//batch_size + 1}批)")
                        QApplication.processEvents()

                        result = api.save_sample_rule(batch_rules)

                        if result.get('success'):
                            batch_success = result.get('data', {}).get('success_count', 0)
                            success_items = result.get('data', {}).get('success_items', [])
                            shop_success += batch_success
                            shop_failed += len(batch_rules) - batch_success

                            # 更新成功商品的设置状态
                            self.update_product_status(batch_rules, success_items, sample_count, "申样设置成功")

                            # 更新失败商品的设置状态
                            failed_items = [rule['item_id'] for rule in batch_rules if rule['item_id'] not in [str(item) for item in success_items]]
                            if failed_items:
                                self.update_product_status_by_ids(failed_items, "申样设置失败")
                        else:
                            error_msg = result.get('message', '未知错误')
                            if batch_start == 0:  # 只在第一批失败时记录错误
                                failed_shops.append(f"{shop_name}: {error_msg}")
                            shop_failed += len(batch_rules)

                            # 更新失败商品的设置状态
                            failed_item_ids = [rule['item_id'] for rule in batch_rules]
                            self.update_product_status_by_ids(failed_item_ids, f"申样设置失败: {error_msg}")

                    total_success += shop_success
                    total_failed += shop_failed

                except Exception as e:
                    failed_shops.append(f"{shop_name}: {str(e)}")
                    total_failed += len(products)

            progress_dialog.close()

            # 显示结果
            result_msg = f"申样设置完成！\n\n"
            result_msg += f"处理商品：{len(selected_products)} 个\n"
            result_msg += f"成功设置：{total_success} 个\n"
            result_msg += f"失败：{total_failed} 个\n"
            result_msg += f"申样数量：{sample_count}"

            if failed_shops:
                result_msg += f"\n\n失败的店铺：\n"
                for shop_error in failed_shops:
                    result_msg += f"• {shop_error}\n"

            if total_failed > 0:
                QMessageBox.warning(self, "申样设置完成（有错误）", result_msg)
            else:
                QMessageBox.information(self, "申样设置完成", result_msg)

        except Exception as e:
            print(f"处理申样设置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"处理申样设置失败: {str(e)}")

    def get_shop_access_token(self, shop_name):
        """根据店铺名称获取access_token"""
        try:
            config_path = get_config_path('账号管理.json')

            if not os.path.exists(config_path):
                return None

            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 获取店铺数据
            shops_data = data.get('data', []) if isinstance(data, dict) else data

            # 查找匹配的店铺
            for shop in shops_data:
                if isinstance(shop, dict):
                    if shop.get('店铺名称', '').strip() == shop_name:
                        return shop.get('accesstoken', '')

            return None

        except Exception as e:
            print(f"获取店铺access_token失败: {str(e)}")
            return None

    def update_product_status(self, item_rules, success_items, sample_count, status_text):
        """更新成功商品的设置状态"""
        try:
            success_item_ids = [str(item) for item in success_items]
            # item_rules参数用于扩展功能，当前版本暂未使用

            for row in range(self.table_widget.rowCount()):
                item_id = self.get_table_cell_by_header(row, "商品ID")

                if item_id in success_item_ids:
                    self.update_table_cell_by_header(row, "操作状态", f"{status_text}({sample_count}个)", QColor(0, 128, 0))

        except Exception as e:
            print(f"更新商品状态失败: {str(e)}")

    def update_product_status_by_ids(self, item_ids, status_text):
        """根据商品ID更新设置状态"""
        try:
            for row in range(self.table_widget.rowCount()):
                item_id = self.get_table_cell_by_header(row, "商品ID")

                if item_id in item_ids:
                    self.update_table_cell_by_header(row, "操作状态", status_text, QColor(255, 0, 0))

        except Exception as e:
            print(f"更新商品状态失败: {str(e)}")

    def load_unset_products(self):
        """加载未设置商品"""
        try:
            # 检查是否选择了店铺
            selected_shop = self.shop_combo.currentText()
            if not selected_shop or selected_shop == "请选择店铺":
                QMessageBox.warning(self, "提示", "请先选择一个店铺")
                return

            # 获取店铺ID
            shop_id = self.get_shop_id_by_name(selected_shop)
            if not shop_id:
                QMessageBox.warning(self, "错误", f"未找到店铺 {selected_shop} 的ID")
                return

            # 检查是否正在加载中
            if hasattr(self, 'loading_thread') and self.loading_thread and self.loading_thread.isRunning():
                QMessageBox.information(self, "提示", "正在加载中，请稍候...")
                return

            # 显示加载进度
            self.show_loading_progress(f"正在加载店铺 {selected_shop} 的未设置商品...")

            # 清空表格
            self.clear_table()

            # 创建并启动加载线程
            self.loading_thread = UnsetProductsLoadThread(selected_shop, shop_id)
            self.loading_thread.progress_updated.connect(self.update_loading_progress)
            self.loading_thread.total_info.connect(self.on_total_info_received)
            self.loading_thread.page_loaded.connect(self.on_page_loaded)
            self.loading_thread.data_loaded.connect(self.on_unset_products_loaded)
            self.loading_thread.error_occurred.connect(self.on_loading_error)
            self.loading_thread.start()

        except Exception as e:
            print(f"加载未设置商品失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载未设置商品失败: {str(e)}")
            self.hide_loading_progress()

    def load_promoting_products(self):
        """加载推广中商品"""
        try:
            # 检查是否选择了店铺
            selected_shop = self.shop_combo.currentText()
            if not selected_shop or selected_shop == "请选择店铺":
                QMessageBox.warning(self, "提示", "请先选择一个店铺")
                return

            # 获取店铺ID
            shop_id = self.get_shop_id_by_name(selected_shop)
            if not shop_id:
                QMessageBox.warning(self, "错误", f"未找到店铺 {selected_shop} 的ID")
                return

            # 检查是否正在加载中
            if hasattr(self, 'loading_thread') and self.loading_thread and self.loading_thread.isRunning():
                QMessageBox.information(self, "提示", "正在加载中，请稍候...")
                return

            # 显示加载进度
            self.show_loading_progress(f"正在加载店铺 {selected_shop} 的推广中商品...")

            # 清空表格
            self.clear_table()

            # 创建并启动加载线程
            self.loading_thread = PromotingProductsLoadThread(selected_shop, shop_id)
            self.loading_thread.progress_updated.connect(self.update_loading_progress)
            self.loading_thread.page_loaded.connect(self.on_promoting_page_loaded)
            self.loading_thread.data_loaded.connect(self.on_promoting_products_loaded)
            self.loading_thread.error_occurred.connect(self.on_loading_error)
            self.loading_thread.start()

        except Exception as e:
            print(f"加载推广中商品失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载推广中商品失败: {str(e)}")
            self.hide_loading_progress()

    def load_offline_products(self):
        """加载已下架推广商品"""
        try:
            # 检查是否选择了店铺
            selected_shop = self.shop_combo.currentText()
            if not selected_shop or selected_shop == "请选择店铺":
                QMessageBox.warning(self, "提示", "请先选择一个店铺")
                return

            # 获取店铺ID
            shop_id = self.get_shop_id_by_name(selected_shop)
            if not shop_id:
                QMessageBox.warning(self, "错误", f"未找到店铺 {selected_shop} 的ID")
                return

            # 检查是否正在加载中
            if hasattr(self, 'loading_thread') and self.loading_thread and self.loading_thread.isRunning():
                QMessageBox.information(self, "提示", "正在加载中，请稍候...")
                return

            # 显示加载进度
            self.show_loading_progress(f"正在加载店铺 {selected_shop} 的已下架推广商品...")

            # 清空表格
            self.clear_table()

            # 创建并启动加载线程
            self.loading_thread = OfflineProductsLoadThread(selected_shop, shop_id)
            self.loading_thread.progress_updated.connect(self.update_loading_progress)
            self.loading_thread.page_loaded.connect(self.on_offline_page_loaded)
            self.loading_thread.data_loaded.connect(self.on_offline_products_loaded)
            self.loading_thread.error_occurred.connect(self.on_loading_error)
            self.loading_thread.start()

        except Exception as e:
            print(f"加载已下架推广商品失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载已下架推广商品失败: {str(e)}")
            self.hide_loading_progress()

    def load_all_shops_unset_products(self):
        """加载所有店铺的未设置商品"""
        try:
            # 检查是否正在加载中
            if hasattr(self, 'loading_thread') and self.loading_thread and self.loading_thread.isRunning():
                QMessageBox.information(self, "提示", "正在加载中，请稍候...")
                return

            # 获取所有有权限的店铺
            valid_shops = self.get_valid_shops()
            if not valid_shops:
                QMessageBox.warning(self, "提示", "没有找到有权限的店铺")
                return

            # 显示确认对话框
            reply = QMessageBox.question(
                self,
                "确认",
                f"将遍历 {len(valid_shops)} 个店铺获取未设置商品，这可能需要较长时间，是否继续？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 显示加载进度
            self.show_loading_progress(f"正在遍历 {len(valid_shops)} 个店铺...")

            # 清空表格
            self.clear_table()

            # 创建并启动全部店铺加载线程
            self.loading_thread = AllShopsUnsetProductsLoadThread(valid_shops)
            self.loading_thread.progress_updated.connect(self.update_loading_progress)
            self.loading_thread.shop_completed.connect(self.on_shop_unset_products_loaded)
            self.loading_thread.all_completed.connect(self.on_all_shops_completed)
            self.loading_thread.error_occurred.connect(self.on_loading_error)
            self.loading_thread.start()

        except Exception as e:
            print(f"加载全部店铺未设置商品失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载全部店铺未设置商品失败: {str(e)}")
            self.hide_loading_progress()

    def get_valid_shops(self):
        """获取有权限的店铺列表（跳过邀约信息为'暂无权限'的店铺）"""
        try:
            config_path = get_config_path('账号管理.json')

            if not os.path.exists(config_path):
                return []

            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 获取店铺数据
            shops_data = data.get('data', []) if isinstance(data, dict) else data

            valid_shops = []
            for shop in shops_data:
                if isinstance(shop, dict):
                    invite_info = shop.get('邀约信息', '')
                    shop_name = shop.get('店铺名称', '')
                    shop_id = shop.get('店铺ID', '')

                    # 跳过邀约信息为'暂无权限'的店铺
                    if invite_info != '暂无权限' and shop_name and shop_id:
                        valid_shops.append({
                            'shop_name': shop_name,
                            'shop_id': shop_id,
                            'invite_info': invite_info
                        })

            print(f"找到 {len(valid_shops)} 个有权限的店铺")
            return valid_shops

        except Exception as e:
            print(f"获取有权限店铺列表失败: {str(e)}")
            return []

    def on_shop_unset_products_loaded(self, products, shop_name):
        """单个店铺未设置商品加载完成"""
        try:
            # 将店铺商品追加到表格
            self.append_products_to_table(products, shop_name)

            # 更新统计信息
            self.refresh_stats()

        except Exception as e:
            print(f"处理店铺 {shop_name} 未设置商品数据失败: {str(e)}")

    def on_all_shops_completed(self, total_products, cookie_expired_shops, other_error_shops):
        """全部店铺处理完成"""
        try:
            self.hide_loading_progress()

            # 更新统计信息
            self.refresh_stats()

            # 构建完成消息
            message = f"全部店铺处理完成！\n共获取 {total_products} 个未设置商品"

            # 添加错误信息
            if cookie_expired_shops or other_error_shops:
                message += "\n\n处理过程中遇到以下问题："

                if cookie_expired_shops:
                    message += f"\n\n🔑 Cookie已过期的店铺 ({len(cookie_expired_shops)}个)："
                    for shop in cookie_expired_shops:
                        message += f"\n• {shop}"
                    message += "\n\n建议：请重新登录这些店铺的账号"

                if other_error_shops:
                    message += f"\n\n❌ 其他错误的店铺 ({len(other_error_shops)}个)："
                    for error_info in other_error_shops:
                        message += f"\n• {error_info}"

            # 根据是否有错误选择消息框类型
            if cookie_expired_shops or other_error_shops:
                QMessageBox.warning(self, "处理完成（有错误）", message)
            else:
                QMessageBox.information(self, "处理完成", message)

        except Exception as e:
            print(f"处理全部店铺完成事件失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"处理完成事件失败: {str(e)}")

    def get_shop_id_by_name(self, shop_name):
        """根据店铺名称获取店铺ID"""
        try:
            config_path = get_config_path('账号管理.json')

            if not os.path.exists(config_path):
                return None

            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 获取店铺数据
            shops_data = data.get('data', []) if isinstance(data, dict) else data

            # 查找匹配的店铺
            for shop in shops_data:
                if isinstance(shop, dict):
                    if shop.get('店铺名称', '').strip() == shop_name:
                        return shop.get('店铺ID', '')

            return None

        except Exception as e:
            print(f"获取店铺ID失败: {str(e)}")
            return None

    def clear_table(self):
        """清空表格"""
        try:
            self.table_widget.setRowCount(0)
            self.refresh_stats()
        except Exception as e:
            print(f"清空表格失败: {str(e)}")

    def update_loading_progress(self, message):
        """更新加载进度"""
        try:
            self.show_loading_progress(message)
        except Exception as e:
            print(f"更新加载进度失败: {str(e)}")

    def on_total_info_received(self, total_count, shop_name):
        """接收到总数信息"""
        try:
            if total_count == 0:
                self.hide_loading_progress()
                QMessageBox.information(self, "提示", f"店铺 {shop_name} 没有未设置商品")
            else:
                self.update_loading_progress(f"店铺 {shop_name} 共有 {total_count} 个未设置商品，开始加载...")
        except Exception as e:
            print(f"处理总数信息失败: {str(e)}")

    def on_page_loaded(self, page_products, shop_name, current_page, total_pages):
        """页面数据加载完成"""
        try:
            # 更新进度
            loaded_count = (current_page - 1) * 20 + len(page_products)
            self.update_loading_progress(f"正在加载第 {current_page}/{total_pages} 页，已加载 {loaded_count} 个商品...")

            # 将页面数据添加到表格
            self.append_products_to_table(page_products, shop_name)

            # 更新统计信息
            self.refresh_stats()

        except Exception as e:
            print(f"处理页面数据失败: {str(e)}")

    def on_unset_products_loaded(self, products, shop_name):
        """未设置商品加载完成"""
        try:
            self.hide_loading_progress()

            # 更新统计信息
            self.refresh_stats()

            QMessageBox.information(self, "成功", f"成功加载店铺 {shop_name} 的 {len(products)} 个未设置商品")

        except Exception as e:
            print(f"处理未设置商品数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"处理商品数据失败: {str(e)}")

    def on_promoting_products_loaded(self, products, shop_name):
        """推广中商品加载完成"""
        try:
            self.hide_loading_progress()

            # 更新统计信息
            self.refresh_stats()

            QMessageBox.information(self, "成功", f"成功加载店铺 {shop_name} 的 {len(products)} 个推广中商品")

        except Exception as e:
            print(f"处理推广中商品数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"处理推广中商品数据失败: {str(e)}")

    def on_promoting_page_loaded(self, page_products, shop_name, current_loaded, total_count):
        """推广中商品页面数据加载完成"""
        try:
            # 将页面商品追加到表格
            self.append_promoting_products_to_table(page_products, shop_name)

            # 更新统计信息
            self.refresh_stats()

            # 更新进度显示
            progress_percent = (current_loaded / total_count) * 100 if total_count > 0 else 100
            self.update_loading_progress(f"正在加载推广中商品: {current_loaded}/{total_count} ({progress_percent:.1f}%)")

        except Exception as e:
            print(f"处理推广中商品页面数据失败: {str(e)}")

    def on_offline_page_loaded(self, page_products, shop_name, current_loaded, total_count):
        """已下架推广商品页面数据加载完成"""
        try:
            # 将页面商品追加到表格
            self.append_offline_products_to_table(page_products, shop_name)

            # 更新统计信息
            self.refresh_stats()

            # 更新进度显示
            progress_percent = (current_loaded / total_count) * 100 if total_count > 0 else 100
            self.update_loading_progress(f"正在加载已下架推广商品: {current_loaded}/{total_count} ({progress_percent:.1f}%)")

        except Exception as e:
            print(f"处理已下架推广商品页面数据失败: {str(e)}")

    def on_offline_products_loaded(self, products, shop_name):
        """已下架推广商品加载完成"""
        try:
            self.hide_loading_progress()

            # 更新统计信息
            self.refresh_stats()

            QMessageBox.information(self, "成功", f"成功加载店铺 {shop_name} 的 {len(products)} 个已下架推广商品")

        except Exception as e:
            print(f"处理已下架推广商品数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"处理已下架推广商品数据失败: {str(e)}")

    def on_loading_error(self, error_message):
        """加载错误处理"""
        try:
            self.hide_loading_progress()
            QMessageBox.critical(self, "加载错误", error_message)
        except Exception as e:
            print(f"处理加载错误失败: {str(e)}")

    def append_products_to_table(self, products, shop_name):
        """将商品数据追加到表格"""
        try:
            # 暂时禁用表格更新，减少闪烁
            self.table_widget.setUpdatesEnabled(False)

            # 获取当前行数
            current_row_count = self.table_widget.rowCount()

            # 增加表格行数
            new_row_count = current_row_count + len(products)
            self.table_widget.setRowCount(new_row_count)

            for i, product in enumerate(products):
                row = current_row_count + i
                self._add_product_row(row, product, shop_name)

            # 调整列宽
            self.adjust_column_widths()

            # 重新启用更新
            self.table_widget.setUpdatesEnabled(True)

        except Exception as e:
            # 确保在异常情况下也重新启用更新
            self.table_widget.setUpdatesEnabled(True)
            print(f"追加表格数据失败: {str(e)}")
            raise

    def populate_table_with_products(self, products, shop_name):
        """用商品数据填充表格（已废弃，现在使用边加载边显示）"""
        try:
            # 清空表格并重新填充（这个方法现在主要用于兼容性）
            self.clear_table()
            self.append_products_to_table(products, shop_name)

        except Exception as e:
            print(f"填充表格数据失败: {str(e)}")
            raise

    def append_promoting_products_to_table(self, products, shop_name):
        """将推广中商品追加到表格"""
        try:
            if not products:
                return

            # 禁用更新以提高性能
            self.table_widget.setUpdatesEnabled(False)

            # 获取当前行数
            current_row_count = self.table_widget.rowCount()

            # 设置新的行数
            new_row_count = current_row_count + len(products)
            self.table_widget.setRowCount(new_row_count)

            for i, product in enumerate(products):
                row = current_row_count + i
                self._add_promoting_product_row(row, product, shop_name)

            # 调整列宽
            self.adjust_column_widths()

            # 重新启用更新
            self.table_widget.setUpdatesEnabled(True)

        except Exception as e:
            print(f"填充推广中商品表格数据失败: {str(e)}")
            raise

    def append_offline_products_to_table(self, products, shop_name):
        """将已下架推广商品追加到表格"""
        try:
            if not products:
                return

            # 禁用更新以提高性能
            self.table_widget.setUpdatesEnabled(False)

            # 获取当前行数
            current_row_count = self.table_widget.rowCount()

            # 设置新的行数
            new_row_count = current_row_count + len(products)
            self.table_widget.setRowCount(new_row_count)

            for i, product in enumerate(products):
                row = current_row_count + i
                self._add_offline_product_row(row, product, shop_name)

            # 调整列宽
            self.adjust_column_widths()

            # 重新启用更新
            self.table_widget.setUpdatesEnabled(True)

        except Exception as e:
            print(f"填充已下架推广商品表格数据失败: {str(e)}")
            raise

    def _add_offline_product_row(self, row, product, shop_name):
        """添加单行已下架推广商品数据"""
        try:
            # 选择列 - 垂直水平都居中
            widget = QWidget()
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setAlignment(Qt.AlignVCenter)  # 垂直居中

            # 左边弹性空间
            layout.addStretch()

            # 序号
            label = QLabel(str(row + 1))
            label.setStyleSheet("font-size: 12px; font-weight: bold; color: #666;")
            layout.addWidget(label, 0, Qt.AlignVCenter)  # 垂直居中

            # 复选框
            checkbox = QCheckBox()
            checkbox.stateChanged.connect(self.refresh_stats)
            layout.addWidget(checkbox, 0, Qt.AlignVCenter)  # 垂直居中

            # 右边弹性空间
            layout.addStretch()

            self.table_widget.setCellWidget(row, 0, widget)

            # 商品ID
            item_id = product.get('itemId', '')
            self.update_table_cell_by_header(row, "商品ID", str(item_id))

            # 商品标题
            name = product.get('name', '')  # 已下架推广商品使用'name'字段
            name_item = QTableWidgetItem(name)
            name_item.setToolTip(name)  # 设置工具提示显示完整标题
            col = self.get_column_index_by_header("商品标题")
            if col >= 0:
                self.table_widget.setItem(row, col, name_item)

            # 价格 (已下架推广商品的价格字段)
            price = product.get('price', 0)
            if isinstance(price, (int, float)):
                price_yuan = price / 100 if price > 0 else 0
                self.update_table_cell_by_header(row, "价格", f"{price_yuan:.2f}")
            else:
                self.update_table_cell_by_header(row, "价格", str(price))

            # 佣金 (需要除以10)
            commission_rate = product.get('commissionRate', 0)
            commission_percent = commission_rate / 10 if commission_rate > 0 else 0
            self.update_table_cell_by_header(row, "佣金", f"{commission_percent}%")

            # 推广状态 - 已下架推广商品默认为"已下架"
            self.update_table_cell_by_header(row, "推广状态", "已下架")

            # 操作状态 - 默认为空
            self.update_table_cell_by_header(row, "操作状态", "")

            # 店铺
            self.update_table_cell_by_header(row, "店铺", shop_name)

        except Exception as e:
            print(f"添加第{row+1}行已下架推广商品数据失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

    def _add_promoting_product_row(self, row, product, shop_name):
        """添加单行推广中商品数据"""
        try:
            # 选择列 - 垂直水平都居中
            widget = QWidget()
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setAlignment(Qt.AlignVCenter)  # 垂直居中

            # 左边弹性空间
            layout.addStretch()

            # 序号
            label = QLabel(str(row + 1))
            label.setStyleSheet("font-size: 12px; font-weight: bold; color: #666;")
            layout.addWidget(label, 0, Qt.AlignVCenter)  # 垂直居中

            # 复选框
            checkbox = QCheckBox()
            checkbox.stateChanged.connect(self.refresh_stats)
            layout.addWidget(checkbox, 0, Qt.AlignVCenter)  # 垂直居中

            # 右边弹性空间
            layout.addStretch()

            self.table_widget.setCellWidget(row, 0, widget)

            # 商品ID
            item_id = product.get('itemId', '')
            self.update_table_cell_by_header(row, "商品ID", str(item_id))

            # 商品标题
            name = product.get('name', '')  # 推广中商品使用'name'字段
            name_item = QTableWidgetItem(name)
            name_item.setToolTip(name)  # 设置工具提示显示完整标题
            col = self.get_column_index_by_header("商品标题")
            if col >= 0:
                self.table_widget.setItem(row, col, name_item)

            # 价格 (推广中商品的价格字段)
            price = product.get('price', 0)
            if isinstance(price, (int, float)):
                price_yuan = price / 100 if price > 0 else 0
                self.update_table_cell_by_header(row, "价格", f"{price_yuan:.2f}")
            else:
                self.update_table_cell_by_header(row, "价格", str(price))

            # 佣金 (需要除以10)
            commission_rate = product.get('commissionRate', 0)
            commission_percent = commission_rate / 10 if commission_rate > 0 else 0
            self.update_table_cell_by_header(row, "佣金", f"{commission_percent}%")

            # 推广状态 - 推广中商品默认为"推广中"
            self.update_table_cell_by_header(row, "推广状态", "推广中")

            # 操作状态 - 默认为空
            self.update_table_cell_by_header(row, "操作状态", "")

            # 店铺
            self.update_table_cell_by_header(row, "店铺", shop_name)

        except Exception as e:
            print(f"添加第{row+1}行推广中商品数据失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

    def _add_product_row(self, row, product, shop_name):
        """添加单行商品数据"""
        try:
            # 选择列 - 垂直水平都居中
            widget = QWidget()
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setAlignment(Qt.AlignVCenter)  # 垂直居中

            # 左边弹性空间
            layout.addStretch()

            # 序号
            label = QLabel(str(row + 1))
            label.setStyleSheet("font-size: 12px; font-weight: bold; color: #666;")
            layout.addWidget(label, 0, Qt.AlignVCenter)  # 垂直居中

            # 复选框
            checkbox = QCheckBox()
            checkbox.stateChanged.connect(self.refresh_stats)
            layout.addWidget(checkbox, 0, Qt.AlignVCenter)  # 垂直居中

            # 右边弹性空间
            layout.addStretch()

            self.table_widget.setCellWidget(row, 0, widget)

            # 商品ID
            item_id = product.get('itemId', '')
            self.update_table_cell_by_header(row, "商品ID", str(item_id))

            # 商品标题
            name = product.get('name', '')
            name_item = QTableWidgetItem(name)
            name_item.setToolTip(name)  # 设置工具提示显示完整标题
            col = self.get_column_index_by_header("商品标题")
            if col >= 0:
                self.table_widget.setItem(row, col, name_item)

            # 价格 (转换为元)
            price = product.get('price', 0)
            price_yuan = price / 100 if price > 0 else 0
            self.update_table_cell_by_header(row, "价格", f"{price_yuan:.2f}")

            # 佣金 (需要除以10)
            commission_rate = product.get('commissionRate', 0)
            commission_percent = commission_rate / 10 if commission_rate > 0 else 0
            self.update_table_cell_by_header(row, "佣金", f"{commission_percent}%")

            # 推广状态
            plan_status = "未设置" if product.get('planId', 0) == 0 else "已设置"
            self.update_table_cell_by_header(row, "推广状态", plan_status)

            # 操作状态 - 默认为空
            self.update_table_cell_by_header(row, "操作状态", "")

            # 店铺
            self.update_table_cell_by_header(row, "店铺", shop_name)

        except Exception as e:
            print(f"添加第{row+1}行商品数据失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

    def adjust_column_widths(self):
        """调整列宽"""
        try:
            # 设置各列的宽度 - 选择列包含序号和复选框，增加宽度
            column_widths = [100, 120, 300, 80, 80, 100, 100, 120]

            for i, width in enumerate(column_widths):
                if i < self.table_widget.columnCount():
                    self.table_widget.setColumnWidth(i, width)

            # 最后一列自动拉伸
            self.table_widget.horizontalHeader().setStretchLastSection(True)

        except Exception as e:
            print(f"调整列宽失败: {str(e)}")

    def add_promotion(self):
        """添加推广 - 创建分销计划"""
        try:
            # 获取表格中的所有商品
            all_products = self.get_all_table_products()
            if not all_products:
                QMessageBox.warning(self, "提示", "表格中没有商品数据")
                return

            # 获取价格范围设置
            price_ranges = self.get_price_ranges()
            if not price_ranges:
                QMessageBox.warning(self, "提示", "请先设置价格范围和佣金比例")
                return

            # 确认对话框
            reply = QMessageBox.question(
                self,
                "确认添加推广",
                f"将为表格中所有 {len(all_products)} 个商品创建分销计划\n根据价格范围自动设置佣金比例\n\n是否继续？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 先设置申样规则，再创建分销计划
                self.process_sample_and_promotion(all_products, price_ranges)

        except Exception as e:
            print(f"添加推广失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"添加推广失败: {str(e)}")

    def get_price_ranges(self):
        """获取价格范围设置"""
        try:
            price_ranges = []

            # 从配置文件中读取价格范围
            config = self.load_config()
            saved_ranges = config.get('price_ranges', [])

            for range_data in saved_ranges:
                try:
                    min_price = float(range_data.get('min_price', 0))
                    max_price = float(range_data.get('max_price', 0))
                    commission_rate = int(range_data.get('commission', 20))

                    price_ranges.append({
                        'min_price': min_price,
                        'max_price': max_price,
                        'commission_rate': commission_rate,
                        'text': f"{min_price}-{max_price}元: {commission_rate}%"
                    })
                except (ValueError, TypeError):
                    continue

            print(f"获取到 {len(price_ranges)} 个价格范围设置")
            return price_ranges

        except Exception as e:
            print(f"获取价格范围失败: {str(e)}")
            return []

    def get_commission_rate_by_price(self, price, price_ranges):
        """根据价格获取对应的佣金比例"""
        try:
            price_value = float(price)

            for range_info in price_ranges:
                if range_info['min_price'] <= price_value <= range_info['max_price']:
                    return range_info['commission_rate']

            # 如果没有匹配的价格范围，返回默认佣金比例
            return 20  # 默认20%

        except (ValueError, TypeError):
            return 20  # 默认20%

    def process_sample_and_promotion(self, all_products, price_ranges):
        """先设置申样规则，再创建分销计划（异步处理）"""
        try:
            # 显示进度信息
            self.progress_label.setText("正在设置申样规则和创建分销计划...")
            self.progress_label.show()

            # 创建并启动异步处理线程
            self.promotion_thread = SampleAndPromotionThread(all_products, price_ranges, self)
            self.promotion_thread.progress_updated.connect(self.update_progress_display)
            self.promotion_thread.sample_completed.connect(self.on_sample_step_completed)
            self.promotion_thread.promotion_completed.connect(self.on_promotion_step_completed)
            self.promotion_thread.all_completed.connect(self.on_sample_promotion_completed)
            self.promotion_thread.error_occurred.connect(self.on_sample_promotion_error)
            self.promotion_thread.start()

        except Exception as e:
            print(f"启动申样和推广处理失败: {str(e)}")
            self.update_status_bar(f"启动申样和推广处理失败: {str(e)}")

    def update_progress_display(self, message):
        """更新进度显示"""
        self.progress_label.setText(message)
        self.progress_label.show()

    def on_sample_step_completed(self, success_count, failed_count):
        """申样步骤完成"""
        if success_count > 0:
            self.update_progress_display(f"申样设置完成：成功 {success_count} 个，失败 {failed_count} 个。开始创建分销计划...")
        else:
            self.update_progress_display(f"申样设置失败：失败 {failed_count} 个，无法创建分销计划")

    def on_promotion_step_completed(self, success_count, failed_count):
        """推广步骤完成"""
        self.update_progress_display(f"分销计划创建完成：成功 {success_count} 个，失败 {failed_count} 个")

    def on_sample_promotion_completed(self, sample_success, sample_failed, promotion_success, promotion_failed):
        """申样和推广全部完成"""
        total_success = promotion_success
        total_failed = promotion_failed

        if total_failed > 0:
            status_msg = f"添加推广完成：成功 {total_success} 个，失败 {total_failed} 个"
        else:
            status_msg = f"添加推广成功：共 {total_success} 个商品"

        self.update_status_bar(status_msg)
        self.progress_label.hide()

    def on_sample_promotion_error(self, error_msg):
        """申样和推广处理错误"""
        self.update_status_bar(f"添加推广失败: {error_msg}")
        self.progress_label.hide()

    def process_sample_setting_internal(self, all_products, sample_count, progress_dialog=None):
        """内部申样设置方法（不显示单独的进度对话框）"""
        try:
            # 按店铺分组商品
            shop_products = {}
            for product in all_products:
                shop_name = product['shop']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(product)

            total_success = 0
            total_failed = 0
            failed_shops = []

            # 为每个店铺设置申样规则
            for i, (shop_name, products) in enumerate(shop_products.items()):
                if progress_dialog and progress_dialog.wasCanceled():
                    break

                if progress_dialog:
                    progress_dialog.setLabelText(f"第1步：正在处理店铺申样: {shop_name}")
                    progress_dialog.setValue(10 + int(30 * i / len(shop_products)))
                    QApplication.processEvents()

                try:
                    # 获取店铺的access_token
                    access_token = self.get_shop_access_token(shop_name)
                    if not access_token:
                        failed_shops.append(f"{shop_name}: 缺少access_token")
                        total_failed += len(products)
                        continue

                    # 构建申样规则数据
                    item_rules = []
                    for product in products:
                        item_rules.append({
                            'item_id': product['item_id'],
                            'sample_count': sample_count
                        })

                    # 分批处理（每批最多20个商品）
                    batch_size = 20
                    shop_success = 0
                    shop_failed = 0

                    from tool.快手api import KuaishouAPI
                    api = KuaishouAPI(access_token=access_token)

                    for batch_start in range(0, len(item_rules), batch_size):
                        batch_end = min(batch_start + batch_size, len(item_rules))
                        batch_rules = item_rules[batch_start:batch_end]

                        result = api.save_sample_rule(batch_rules)

                        if result.get('success'):
                            batch_success = result.get('data', {}).get('success_count', 0)
                            shop_success += batch_success
                            shop_failed += len(batch_rules) - batch_success
                        else:
                            error_msg = result.get('message', '未知错误')
                            if batch_start == 0:  # 只在第一批失败时记录错误
                                failed_shops.append(f"{shop_name}: {error_msg}")
                            shop_failed += len(batch_rules)

                    total_success += shop_success
                    total_failed += shop_failed

                except Exception as e:
                    failed_shops.append(f"{shop_name}: {str(e)}")
                    total_failed += len(products)

            # 更新状态
            if total_failed == 0:
                status_msg = f"申样设置成功：共 {total_success} 个商品"
                self.update_status_bar(status_msg)
                return True
            else:
                status_msg = f"申样设置完成：成功 {total_success} 个，失败 {total_failed} 个"
                self.update_status_bar(status_msg)
                return total_success > 0  # 只要有成功的就继续

        except Exception as e:
            print(f"内部申样设置失败: {str(e)}")
            return False

    def process_promotion_creation_internal(self, all_products, price_ranges, progress_dialog=None):
        """内部分销计划创建方法"""
        try:
            # 按店铺分组商品
            shop_products = {}
            for product in all_products:
                shop_name = product['shop']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(product)

            # 如果没有传入进度对话框，则不显示进度
            if not progress_dialog:
                progress_dialog = QProgressDialog("正在创建分销计划...", "取消", 0, len(shop_products), self)
                progress_dialog.setWindowTitle("创建分销计划进度")
                progress_dialog.setWindowModality(Qt.WindowModal)
                progress_dialog.show()
                should_close_dialog = True
            else:
                should_close_dialog = False

            total_success = 0
            total_failed = 0
            failed_shops = []

            # 为每个店铺创建分销计划
            for i, (shop_name, products) in enumerate(shop_products.items()):
                if progress_dialog.wasCanceled():
                    break

                progress_dialog.setLabelText(f"第2步：正在处理店铺推广: {shop_name}")
                progress_dialog.setValue(50 + int(40 * i / len(shop_products)))
                QApplication.processEvents()

                try:
                    # 获取店铺的access_token
                    access_token = self.get_shop_access_token(shop_name)
                    if not access_token:
                        failed_shops.append(f"{shop_name}: 缺少access_token")
                        total_failed += len(products)
                        continue

                    # 构建分销计划数据
                    item_plans = []
                    for product in products:
                        # 获取商品价格（使用表头名索引）
                        price_text = self.get_table_cell_by_header(product['row'], "价格")
                        price = price_text.replace('¥', '').replace(',', '').strip() if price_text else '0'

                        # 根据价格获取佣金比例
                        commission_rate = self.get_commission_rate_by_price(price, price_ranges)

                        item_plans.append({
                            'item_id': product['item_id'],
                            'commission_rate': commission_rate
                        })

                    # 调用快手API创建分销计划
                    from tool.快手api import KuaishouAPI
                    api = KuaishouAPI(access_token=access_token)
                    result = api.create_distribution_plan(item_plans, "ITEM_NORMAL")

                    if result.get('success'):
                        success_count = result.get('data', {}).get('total_success', 0)
                        failed_count = result.get('data', {}).get('total_failed', 0)
                        total_success += success_count
                        total_failed += failed_count

                        # 更新商品的操作状态
                        self.update_promotion_status(products, result, price_ranges)

                    else:
                        error_msg = result.get('message', '未知错误')
                        failed_shops.append(f"{shop_name}: {error_msg}")
                        total_failed += len(products)

                        # 更新失败商品的状态
                        for product in products:
                            self.update_single_product_status(product['row'], "失败")

                except Exception as e:
                    failed_shops.append(f"{shop_name}: {str(e)}")
                    total_failed += len(products)

                    # 更新失败商品的状态
                    for product in products:
                        self.update_single_product_status(product['row'], "异常")

            # 只有在创建了新对话框时才关闭
            if should_close_dialog:
                progress_dialog.close()

            # 在状态栏显示结果，不弹窗
            if total_failed > 0:
                status_msg = f"分销计划创建完成：成功 {total_success} 个，失败 {total_failed} 个"
                if failed_shops:
                    status_msg += f"，失败店铺：{', '.join([shop.split(':')[0] for shop in failed_shops[:3]])}"
                    if len(failed_shops) > 3:
                        status_msg += f" 等{len(failed_shops)}个"
            else:
                status_msg = f"分销计划创建成功：共 {total_success} 个商品"

            # 更新状态栏
            self.update_status_bar(status_msg)

        except Exception as e:
            print(f"处理分销计划创建失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"处理分销计划创建失败: {str(e)}")

    def update_promotion_status(self, products, result, price_ranges):
        """更新推广创建状态"""
        try:
            success_count = result.get('data', {}).get('total_success', 0)
            # failed_count变量暂未使用，但保留以备将来扩展

            # 简单处理：假设前面的商品成功，后面的失败
            for i, product in enumerate(products):
                if i < success_count:
                    # 推广创建成功，在推广状态列显示"推广中"
                    self.update_table_cell_by_header(product['row'], "推广状态", "推广中", QColor(0, 128, 0))
                else:
                    # 推广创建失败
                    self.update_table_cell_by_header(product['row'], "推广状态", "失败", QColor(255, 0, 0))

        except Exception as e:
            print(f"更新推广状态失败: {str(e)}")

    def update_single_product_status(self, row, status_text, color=None):
        """更新单个商品的操作状态"""
        try:
            if color is None:
                color = QColor(255, 0, 0)  # 默认红色
            self.update_table_cell_by_header(row, "操作状态", status_text, color)

        except Exception as e:
            print(f"更新单个商品状态失败: {str(e)}")

    def get_column_index_by_header(self, header_name):
        """根据表头名称获取列索引"""
        try:
            for col in range(self.table_widget.columnCount()):
                header_text = self.table_widget.horizontalHeaderItem(col).text()
                if header_text == header_name:
                    print(f"[佣金调试] 找到表头 '{header_name}' 在列 {col}")
                    return col
            print(f"[佣金调试] 未找到表头 '{header_name}'，可用表头:")
            for col in range(self.table_widget.columnCount()):
                header_text = self.table_widget.horizontalHeaderItem(col).text()
                print(f"[佣金调试]   列{col}: '{header_text}'")
            return -1  # 未找到
        except Exception as e:
            print(f"获取列索引失败: {str(e)}")
            return -1

    def update_table_cell_by_header(self, row, header_name, text, color=None):
        """根据表头名称更新表格单元格"""
        try:
            print(f"[佣金调试] 更新单元格: 行={row}, 表头='{header_name}', 文本='{text}'")
            col = self.get_column_index_by_header(header_name)
            if col >= 0:
                print(f"[佣金调试] 找到列索引: {col}")
                item = QTableWidgetItem(text)
                if color:
                    item.setForeground(color)
                    print(f"[佣金调试] 设置颜色: {color}")
                self.table_widget.setItem(row, col, item)
                print(f"[佣金调试] 单元格更新完成")

                # 验证更新是否成功
                verify_item = self.table_widget.item(row, col)
                if verify_item:
                    print(f"[佣金调试] 验证成功，单元格内容: '{verify_item.text()}'")
                else:
                    print(f"[佣金调试] 验证失败，单元格为空")
            else:
                print(f"[佣金调试] 未找到表头: {header_name}")
        except Exception as e:
            print(f"[佣金调试] 更新表格单元格失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def get_table_cell_by_header(self, row, header_name):
        """根据表头名称获取表格单元格内容"""
        try:
            col = self.get_column_index_by_header(header_name)
            if col >= 0:
                item = self.table_widget.item(row, col)
                return item.text() if item else ''
            return ''
        except Exception as e:
            print(f"获取表格单元格失败: {str(e)}")
            return ''

    def filter_by_commission(self):
        """佣金筛选功能"""
        try:
            # 从输入框获取佣金筛选条件
            commission_text = self.commission_input.text().strip()

            if not commission_text:
                QMessageBox.warning(self, "提示", "请先在佣金输入框中输入筛选条件")
                return

            matched_count = self.apply_commission_filter(commission_text)

            if matched_count > 0:
                self.update_status_bar(f"佣金筛选完成：已勾选 {matched_count} 个符合条件的商品")
            else:
                self.update_status_bar("佣金筛选完成：没有找到符合条件的商品")

        except Exception as e:
            print(f"佣金筛选失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"佣金筛选失败: {str(e)}")

    def apply_commission_filter(self, condition):
        """应用佣金筛选条件"""
        try:
            matched_count = 0

            # 先取消所有勾选
            self.deselect_all_items()

            for row in range(self.table_widget.rowCount()):
                commission_text = self.get_table_cell_by_header(row, "佣金")
                if not commission_text:
                    continue

                # 提取佣金数值（去掉%符号）
                try:
                    commission_value = float(commission_text.replace('%', '').strip())
                except ValueError:
                    continue

                # 检查是否符合筛选条件
                if self.check_commission_condition(commission_value, condition):
                    # 勾选符合条件的商品
                    select_widget = self.table_widget.cellWidget(row, 0)
                    if select_widget:
                        checkbox = select_widget.findChild(QCheckBox)
                        if checkbox:
                            checkbox.setChecked(True)
                            matched_count += 1

            # 更新统计
            self.refresh_stats()
            return matched_count

        except Exception as e:
            print(f"应用佣金筛选失败: {str(e)}")
            return 0

    def check_commission_condition(self, value, condition):
        """检查佣金值是否符合筛选条件"""
        try:
            condition = condition.strip()

            # 范围条件：5-10
            if '-' in condition and not condition.startswith('-'):
                parts = condition.split('-')
                if len(parts) == 2:
                    try:
                        min_val = float(parts[0].strip())
                        max_val = float(parts[1].strip())
                        return min_val <= value <= max_val
                    except ValueError:
                        return False

            # 大于等于：>=5
            elif condition.startswith('>='):
                try:
                    threshold = float(condition[2:].strip())
                    return value >= threshold
                except ValueError:
                    return False

            # 小于等于：<=5
            elif condition.startswith('<='):
                try:
                    threshold = float(condition[2:].strip())
                    return value <= threshold
                except ValueError:
                    return False

            # 大于：>5
            elif condition.startswith('>'):
                try:
                    threshold = float(condition[1:].strip())
                    return value > threshold
                except ValueError:
                    return False

            # 小于：<5
            elif condition.startswith('<'):
                try:
                    threshold = float(condition[1:].strip())
                    return value < threshold
                except ValueError:
                    return False

            # 等于：=5 或 5
            elif condition.startswith('='):
                try:
                    threshold = float(condition[1:].strip())
                    return abs(value - threshold) < 0.01  # 浮点数比较
                except ValueError:
                    return False

            # 默认等于
            else:
                try:
                    threshold = float(condition)
                    return abs(value - threshold) < 0.01  # 浮点数比较
                except ValueError:
                    return False

        except Exception as e:
            print(f"检查佣金条件失败: {str(e)}")
            return False

    def filter_by_price(self):
        """价格筛选功能"""
        try:
            # 从输入框获取价格筛选条件
            price_min_text = self.price_min_input.text().strip()
            price_max_text = self.price_max_input.text().strip()

            if not price_min_text and not price_max_text:
                QMessageBox.warning(self, "提示", "请先在价格输入框中输入筛选条件")
                return

            # 构建价格筛选条件
            condition = ""
            if price_min_text and price_max_text:
                # 范围筛选
                condition = f"{price_min_text}-{price_max_text}"
            elif price_min_text:
                # 只有最小值，表示大于等于
                condition = f">={price_min_text}"
            elif price_max_text:
                # 只有最大值，表示小于等于
                condition = f"<={price_max_text}"

            matched_count = self.apply_price_filter(condition)

            if matched_count > 0:
                self.update_status_bar(f"价格筛选完成：已勾选 {matched_count} 个符合条件的商品")
            else:
                self.update_status_bar("价格筛选完成：没有找到符合条件的商品")

        except Exception as e:
            print(f"价格筛选失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"价格筛选失败: {str(e)}")

    def apply_price_filter(self, condition):
        """应用价格筛选条件"""
        try:
            matched_count = 0

            # 先取消所有勾选
            self.deselect_all_items()

            for row in range(self.table_widget.rowCount()):
                price_text = self.get_table_cell_by_header(row, "价格")
                if not price_text:
                    continue

                # 提取价格数值（去掉¥符号和逗号）
                try:
                    price_value = float(price_text.replace('¥', '').replace(',', '').strip())
                except ValueError:
                    continue

                # 检查是否符合筛选条件
                if self.check_price_condition(price_value, condition):
                    # 勾选符合条件的商品
                    select_widget = self.table_widget.cellWidget(row, 0)
                    if select_widget:
                        checkbox = select_widget.findChild(QCheckBox)
                        if checkbox:
                            checkbox.setChecked(True)
                            matched_count += 1

            # 更新统计
            self.refresh_stats()
            return matched_count

        except Exception as e:
            print(f"应用价格筛选失败: {str(e)}")
            return 0

    def check_price_condition(self, value, condition):
        """检查价格值是否符合筛选条件"""
        try:
            condition = condition.strip()

            # 范围条件：50-100
            if '-' in condition and not condition.startswith('-'):
                parts = condition.split('-')
                if len(parts) == 2:
                    try:
                        min_val = float(parts[0].strip())
                        max_val = float(parts[1].strip())
                        return min_val <= value <= max_val
                    except ValueError:
                        return False

            # 大于等于：>=50
            elif condition.startswith('>='):
                try:
                    threshold = float(condition[2:].strip())
                    return value >= threshold
                except ValueError:
                    return False

            # 小于等于：<=50
            elif condition.startswith('<='):
                try:
                    threshold = float(condition[2:].strip())
                    return value <= threshold
                except ValueError:
                    return False

            # 大于：>50
            elif condition.startswith('>'):
                try:
                    threshold = float(condition[1:].strip())
                    return value > threshold
                except ValueError:
                    return False

            # 小于：<50
            elif condition.startswith('<'):
                try:
                    threshold = float(condition[1:].strip())
                    return value < threshold
                except ValueError:
                    return False

            # 等于：=50 或 50
            elif condition.startswith('='):
                try:
                    threshold = float(condition[1:].strip())
                    return abs(value - threshold) < 0.01  # 浮点数比较
                except ValueError:
                    return False

            # 默认等于
            else:
                try:
                    threshold = float(condition)
                    return abs(value - threshold) < 0.01  # 浮点数比较
                except ValueError:
                    return False

        except Exception as e:
            print(f"检查价格条件失败: {str(e)}")
            return False

    def show_context_menu(self, position):
        """显示右键菜单"""
        try:
            # 检查是否点击在有效行上
            item = self.table_widget.itemAt(position)
            if item is None:
                return

            # 创建右键菜单
            context_menu = QMenu(self)

            # 设置菜单样式
            context_menu.setStyleSheet("""
                QMenu {
                    background-color: white;
                    border: 1px solid #d0d0d0;
                    border-radius: 8px;
                    padding: 5px;
                    font-size: 13px;
                }
                QMenu::item {
                    background-color: transparent;
                    padding: 8px 20px 8px 35px;
                    border-radius: 4px;
                    margin: 1px;
                }
                QMenu::item:selected {
                    background-color: #3E5FAC;
                    color: white;
                }
                QMenu::item:disabled {
                    color: #999;
                }
                QMenu::separator {
                    height: 1px;
                    background-color: #e0e0e0;
                    margin: 5px 10px;
                }
            """)

            # 移除系统级阴影效果，修复右下角圆角背景显示异常问题
            context_menu.setWindowFlags(context_menu.windowFlags() | Qt.NoDropShadowWindowHint)

            # 添加菜单项
            # 全选
            select_all_action = context_menu.addAction("🔲 全选")
            select_all_action.triggered.connect(self.select_all_items)

            # 取消全选
            deselect_all_action = context_menu.addAction("⬜ 取消全选")
            deselect_all_action.triggered.connect(self.deselect_all_items)

            context_menu.addSeparator()

            # 复制商品ID
            copy_id_action = context_menu.addAction("📋 复制商品ID")
            copy_id_action.triggered.connect(self.copy_product_ids)

            # 复制标题
            copy_title_action = context_menu.addAction("📝 复制标题")
            copy_title_action.triggered.connect(self.copy_product_titles)

            context_menu.addSeparator()

            # 修改佣金
            modify_commission_action = context_menu.addAction("💰 修改佣金")
            modify_commission_action.triggered.connect(self.modify_commission)

            # 显示菜单
            context_menu.exec_(self.table_widget.mapToGlobal(position))

        except Exception as e:
            print(f"显示右键菜单失败: {str(e)}")

    def select_all_items(self):
        """全选所有商品"""
        try:
            for row in range(self.table_widget.rowCount()):
                select_widget = self.table_widget.cellWidget(row, 0)
                if select_widget:
                    checkbox = select_widget.findChild(QCheckBox)
                    if checkbox:
                        checkbox.setChecked(True)

            # 更新统计
            self.refresh_stats()

        except Exception as e:
            print(f"全选失败: {str(e)}")

    def deselect_all_items(self):
        """取消全选所有商品"""
        try:
            for row in range(self.table_widget.rowCount()):
                select_widget = self.table_widget.cellWidget(row, 0)
                if select_widget:
                    checkbox = select_widget.findChild(QCheckBox)
                    if checkbox:
                        checkbox.setChecked(False)

            # 更新统计
            self.refresh_stats()

        except Exception as e:
            print(f"取消全选失败: {str(e)}")

    def copy_product_ids(self):
        """复制商品ID（优先复制勾选的，没有勾选则复制选中行的）"""
        try:
            selected_ids = []

            # 首先检查是否有勾选的商品
            for row in range(self.table_widget.rowCount()):
                select_widget = self.table_widget.cellWidget(row, 0)
                if select_widget:
                    checkbox = select_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        product_id = self.get_table_cell_by_header(row, "商品ID")
                        if product_id:
                            selected_ids.append(product_id)

            # 如果没有勾选的商品，则获取选中行的商品ID
            if not selected_ids:
                selected_rows = set()
                for item in self.table_widget.selectedItems():
                    selected_rows.add(item.row())

                for row in selected_rows:
                    product_id = self.get_table_cell_by_header(row, "商品ID")
                    if product_id:
                        selected_ids.append(product_id)

            if selected_ids:
                # 复制到剪贴板
                clipboard = QApplication.clipboard()
                clipboard.setText('\n'.join(selected_ids))

                # 显示提示
                self.update_status_bar(f"已复制 {len(selected_ids)} 个商品ID到剪贴板")
            else:
                QMessageBox.information(self, "提示", "请先勾选商品或选中表格行")

        except Exception as e:
            print(f"复制商品ID失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"复制商品ID失败: {str(e)}")

    def copy_product_titles(self):
        """复制商品标题（优先复制勾选的，没有勾选则复制选中行的）"""
        try:
            selected_titles = []

            # 首先检查是否有勾选的商品
            for row in range(self.table_widget.rowCount()):
                select_widget = self.table_widget.cellWidget(row, 0)
                if select_widget:
                    checkbox = select_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        title = self.get_table_cell_by_header(row, "商品标题")
                        if title:
                            selected_titles.append(title)

            # 如果没有勾选的商品，则获取选中行的商品标题
            if not selected_titles:
                selected_rows = set()
                for item in self.table_widget.selectedItems():
                    selected_rows.add(item.row())

                for row in selected_rows:
                    title = self.get_table_cell_by_header(row, "商品标题")
                    if title:
                        selected_titles.append(title)

            if selected_titles:
                # 复制到剪贴板
                clipboard = QApplication.clipboard()
                clipboard.setText('\n'.join(selected_titles))

                # 显示提示
                self.update_status_bar(f"已复制 {len(selected_titles)} 个商品标题到剪贴板")
            else:
                QMessageBox.information(self, "提示", "请先勾选商品或选中表格行")

        except Exception as e:
            print(f"复制商品标题失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"复制商品标题失败: {str(e)}")

    def modify_commission(self):
        """修改选中商品的佣金"""
        try:
            # 从批量改佣金输入框获取佣金值
            commission_text = self.batch_commission_input.text().strip()

            if not commission_text:
                QMessageBox.warning(self, "提示", "请先在批量改佣金输入框中输入佣金值")
                return

            # 验证佣金值
            try:
                commission_rate = int(float(commission_text))
                if commission_rate < 0 or commission_rate > 90:
                    QMessageBox.warning(self, "错误", "佣金比例必须在0-90之间")
                    return
            except ValueError:
                QMessageBox.warning(self, "错误", "请输入有效的数字")
                return

            # 获取选中行的商品（不使用勾选，只使用选中行）
            selected_products = []
            selected_rows = set()
            for item in self.table_widget.selectedItems():
                selected_rows.add(item.row())

            for row in selected_rows:
                product_info = {
                    'row': row,
                    'item_id': self.get_table_cell_by_header(row, "商品ID"),
                    'name': self.get_table_cell_by_header(row, "商品标题"),
                    'shop': self.get_table_cell_by_header(row, "店铺"),
                    'promotion_status': self.get_table_cell_by_header(row, "推广状态")
                }
                selected_products.append(product_info)

            if not selected_products:
                QMessageBox.information(self, "提示", "请先选中表格行")
                return

            # 检查是否有推广中的商品
            promoting_products = [p for p in selected_products if p['promotion_status'] == '推广中']
            if not promoting_products:
                QMessageBox.warning(self, "提示", "选中的商品中没有推广中的商品，无法修改佣金")
                return

            # 显示确认对话框
            reply = QMessageBox.question(
                self,
                "确认修改佣金",
                f"确定要将选中的 {len(promoting_products)} 个推广中商品的佣金修改为 {commission_rate}%？\n"
                f"此操作将调用快手API实际更新分销计划。\n\n"
                f"注意：\n"
                f"• 只会修改推广中的商品\n"
                f"• 佣金修改次日0点生效\n"
                f"• 23:50-24:00期间不允许下调佣金率",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 异步执行佣金修改
                self.execute_commission_modification(promoting_products, commission_rate)

        except Exception as e:
            print(f"修改佣金失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"修改佣金失败: {str(e)}")

    def execute_commission_modification(self, products, commission_rate):
        """执行佣金修改"""
        try:
            # 显示进度
            self.show_loading_progress(f"正在修改 {len(products)} 个商品的佣金...")

            # 创建并启动佣金修改线程
            self.commission_thread = CommissionModificationThread(products, commission_rate, self)
            self.commission_thread.progress_updated.connect(self.update_progress_display)  # 使用进度显示方法
            self.commission_thread.product_updated.connect(self.on_commission_product_updated)
            self.commission_thread.all_completed.connect(self.on_commission_modification_completed)
            self.commission_thread.error_occurred.connect(self.on_loading_error)
            self.commission_thread.start()

        except Exception as e:
            print(f"执行佣金修改失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"执行佣金修改失败: {str(e)}")
            self.hide_loading_progress()

    def on_commission_product_updated(self, row, new_commission, status):
        """单个商品佣金更新状态变化"""
        try:
            print(f"[佣金调试] 开始更新商品状态: 行={row}, 佣金={new_commission}, 状态={status}")
            print(f"[佣金调试] 表格总行数: {self.table_widget.rowCount()}")

            # 检查行号是否有效
            if row < 0 or row >= self.table_widget.rowCount():
                print(f"[佣金调试] 错误：行号 {row} 超出范围 (0-{self.table_widget.rowCount()-1})")
                return

            if status is None:
                # 正在处理中 - 更新三个列
                print(f"[佣金调试] 处理中状态 - 更新三列")
                try:
                    print(f"[佣金调试] 尝试更新佣金列...")
                    self.update_table_cell_by_header(row, "佣金", f"设置中...", QColor(255, 165, 0))  # 佣金列显示设置中
                    print(f"[佣金调试] 佣金列更新成功")
                except Exception as e:
                    print(f"[佣金调试] 更新佣金列失败: {e}")
                try:
                    print(f"[佣金调试] 尝试更新推广状态列...")
                    self.update_table_cell_by_header(row, "推广状态", f"佣金设置中", QColor(255, 165, 0))  # 推广状态列
                    print(f"[佣金调试] 推广状态列更新成功")
                except Exception as e:
                    print(f"[佣金调试] 更新推广状态列失败: {e}")
                try:
                    print(f"[佣金调试] 尝试更新操作状态列...")
                    self.update_table_cell_by_header(row, "操作状态", f"设置佣金中({new_commission}%)...", QColor(255, 165, 0))  # 操作状态列
                    print(f"[佣金调试] 操作状态列更新成功")
                except Exception as e:
                    print(f"[佣金调试] 更新操作状态列失败: {e}")
                # 强制刷新表格显示
                self.table_widget.viewport().update()
                QApplication.processEvents()  # 立即处理UI事件
            elif status is True:
                # 设置成功 - 更新三个列
                print(f"[佣金调试] 成功状态 - 更新三列")
                try:
                    print(f"[佣金调试] 尝试更新佣金列...")
                    self.update_table_cell_by_header(row, "佣金", f"{new_commission}%", QColor(0, 128, 0))  # 佣金列显示新佣金值
                    print(f"[佣金调试] 佣金列更新成功")
                except Exception as e:
                    print(f"[佣金调试] 更新佣金列失败: {e}")
                try:
                    print(f"[佣金调试] 尝试更新推广状态列...")
                    self.update_table_cell_by_header(row, "推广状态", f"佣金已更新", QColor(0, 128, 0))  # 推广状态列
                    print(f"[佣金调试] 推广状态列更新成功")
                except Exception as e:
                    print(f"[佣金调试] 更新推广状态列失败: {e}")
                try:
                    print(f"[佣金调试] 尝试更新操作状态列...")
                    self.update_table_cell_by_header(row, "操作状态", f"设置成功({new_commission}%)", QColor(0, 128, 0))  # 操作状态列
                    print(f"[佣金调试] 操作状态列更新成功")
                except Exception as e:
                    print(f"[佣金调试] 更新操作状态列失败: {e}")
                # 强制刷新表格显示
                self.table_widget.viewport().update()
                QApplication.processEvents()  # 立即处理UI事件
            elif status is False:
                # 设置失败 - 更新三个列
                print(f"[佣金调试] 失败状态 - 更新三列")
                try:
                    print(f"[佣金调试] 尝试更新佣金列...")
                    self.update_table_cell_by_header(row, "佣金", f"设置失败", QColor(255, 0, 0))  # 佣金列显示失败
                    print(f"[佣金调试] 佣金列更新成功")
                except Exception as e:
                    print(f"[佣金调试] 更新佣金列失败: {e}")
                try:
                    print(f"[佣金调试] 尝试更新推广状态列...")
                    self.update_table_cell_by_header(row, "推广状态", f"佣金设置失败", QColor(255, 0, 0))  # 推广状态列
                    print(f"[佣金调试] 推广状态列更新成功")
                except Exception as e:
                    print(f"[佣金调试] 更新推广状态列失败: {e}")
                try:
                    print(f"[佣金调试] 尝试更新操作状态列...")
                    self.update_table_cell_by_header(row, "操作状态", f"佣金设置失败", QColor(255, 0, 0))  # 操作状态列
                    print(f"[佣金调试] 操作状态列更新成功")
                except Exception as e:
                    print(f"[佣金调试] 更新操作状态列失败: {e}")
                # 强制刷新表格显示
                self.table_widget.viewport().update()
                QApplication.processEvents()  # 立即处理UI事件

        except Exception as e:
            print(f"更新商品佣金显示失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_commission_modification_completed(self, success_count, failed_count):
        """佣金修改全部完成"""
        try:
            # 隐藏进度显示
            self.progress_label.hide()

            if failed_count == 0:
                self.update_status_bar(f"佣金修改成功：共 {success_count} 个商品")
                QMessageBox.information(self, "成功", f"佣金修改成功：共 {success_count} 个商品")
            else:
                self.update_status_bar(f"佣金修改完成：成功 {success_count} 个，失败 {failed_count} 个")
                QMessageBox.warning(self, "部分成功", f"佣金修改完成：成功 {success_count} 个，失败 {failed_count} 个")

        except Exception as e:
            print(f"处理佣金修改完成失败: {str(e)}")

    def batch_modify_commission(self):
        """批量改佣金功能"""
        try:
            # 从输入框获取佣金值
            commission_text = self.batch_commission_input.text().strip()

            if not commission_text:
                QMessageBox.warning(self, "提示", "请先在佣金输入框中输入佣金值")
                return

            # 验证佣金值
            try:
                commission_rate = int(float(commission_text))
                if commission_rate < 0 or commission_rate > 90:
                    QMessageBox.warning(self, "错误", "佣金比例必须在0-90之间")
                    return
            except ValueError:
                QMessageBox.warning(self, "错误", "请输入有效的数字")
                return

            # 获取勾选的商品
            selected_products = []
            for row in range(self.table_widget.rowCount()):
                select_widget = self.table_widget.cellWidget(row, 0)
                if select_widget:
                    checkbox = select_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        product_info = {
                            'row': row,
                            'item_id': self.get_table_cell_by_header(row, "商品ID"),
                            'product_id': self.get_table_cell_by_header(row, "商品ID"),  # 添加product_id字段
                            'name': self.get_table_cell_by_header(row, "商品标题"),
                            'shop': self.get_table_cell_by_header(row, "店铺"),
                            'promotion_status': self.get_table_cell_by_header(row, "推广状态")
                        }
                        selected_products.append(product_info)

            if not selected_products:
                QMessageBox.information(self, "提示", "请先勾选要修改佣金的商品")
                return

            # 检查是否有推广中的商品
            promoting_products = [p for p in selected_products if p['promotion_status'] == '推广中']
            if not promoting_products:
                QMessageBox.warning(self, "提示", "勾选的商品中没有推广中的商品，无法修改佣金")
                return

            # 显示确认对话框
            reply = QMessageBox.question(
                self,
                "确认批量改佣金",
                f"确定要将 {len(promoting_products)} 个推广中商品的佣金修改为 {commission_rate}%？\n"
                f"此操作将调用快手API实际更新分销计划。\n\n"
                f"注意：\n"
                f"• 只会修改推广中的商品\n"
                f"• 佣金修改次日0点生效\n"
                f"• 23:50-24:00期间不允许下调佣金率",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 异步执行佣金修改
                self.execute_commission_modification(promoting_products, commission_rate)

                # 清空输入框
                self.batch_commission_input.clear()

        except Exception as e:
            print(f"批量改佣金失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"批量改佣金失败: {str(e)}")

    def search_products(self):
        """搜索商品功能"""
        try:
            # 获取搜索关键词
            search_text = self.product_input.text().strip()

            if not search_text:
                QMessageBox.warning(self, "提示", "请输入商品ID或商品标题进行搜索")
                return

            # 检查是否选择了店铺
            selected_shop = self.shop_combo.currentText()

            # 检查是否正在加载中
            if hasattr(self, 'loading_thread') and self.loading_thread and self.loading_thread.isRunning():
                QMessageBox.information(self, "提示", "正在加载中，请稍候...")
                return

            # 显示加载进度
            if selected_shop and selected_shop not in ["请选择店铺", "全部店铺"]:
                # 选中了具体店铺
                shop_id = self.get_shop_id_by_name(selected_shop)
                if not shop_id:
                    QMessageBox.warning(self, "错误", f"未找到店铺 {selected_shop} 的ID")
                    return

                self.show_loading_progress(f"正在在店铺 {selected_shop} 中搜索商品...")

                # 创建并启动单店铺搜索线程
                self.loading_thread = ProductSearchThread(selected_shop, shop_id, search_text)
            else:
                # 选择了"全部店铺"或没有选中店铺，遍历所有店铺搜索
                self.show_loading_progress(f"正在所有店铺中搜索商品...")

                # 创建并启动全店铺搜索线程
                self.loading_thread = AllShopsSearchThread(search_text, self)

            # 清空表格
            self.clear_table()

            # 绑定信号
            self.loading_thread.progress_updated.connect(self.update_loading_progress)
            self.loading_thread.data_loaded.connect(self.on_search_products_loaded)
            self.loading_thread.error_occurred.connect(self.on_loading_error)
            self.loading_thread.start()

        except Exception as e:
            print(f"搜索商品失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"搜索商品失败: {str(e)}")
            self.hide_loading_progress()

    def on_search_products_loaded(self, products, shop_name, search_text):
        """搜索商品加载完成"""
        try:
            self.hide_loading_progress()

            if not products:
                self.update_status_bar(f"搜索完成：未找到匹配的商品")
                QMessageBox.information(self, "搜索结果", f"未找到包含 '{search_text}' 的商品")
                return

            # 将搜索结果添加到表格
            self.append_search_products_to_table(products, shop_name)

            # 更新统计信息
            self.refresh_stats()

            self.update_status_bar(f"搜索完成：找到 {len(products)} 个匹配的商品")
            QMessageBox.information(self, "搜索结果", f"找到 {len(products)} 个包含 '{search_text}' 的商品")

        except Exception as e:
            print(f"处理搜索结果失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"处理搜索结果失败: {str(e)}")

    def append_search_products_to_table(self, products, shop_name):
        """将搜索结果添加到表格"""
        try:
            if not products:
                return

            # 禁用更新以提高性能
            self.table_widget.setUpdatesEnabled(False)

            # 获取当前行数
            current_row_count = self.table_widget.rowCount()

            # 设置新的行数
            new_row_count = current_row_count + len(products)
            self.table_widget.setRowCount(new_row_count)

            for i, product in enumerate(products):
                row = current_row_count + i
                self._add_search_product_row(row, product, shop_name)

            # 调整列宽
            self.adjust_column_widths()

            # 重新启用更新
            self.table_widget.setUpdatesEnabled(True)

        except Exception as e:
            print(f"填充搜索结果表格数据失败: {str(e)}")
            raise

    def _add_search_product_row(self, row, product, shop_name):
        """添加单行搜索结果数据"""
        try:
            # 选择列 - 垂直水平都居中
            widget = QWidget()
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setAlignment(Qt.AlignVCenter)  # 垂直居中

            # 左边弹性空间
            layout.addStretch()

            # 序号
            label = QLabel(str(row + 1))
            label.setStyleSheet("font-size: 12px; font-weight: bold; color: #666;")
            layout.addWidget(label, 0, Qt.AlignVCenter)  # 垂直居中

            # 复选框
            checkbox = QCheckBox()
            checkbox.stateChanged.connect(self.refresh_stats)
            layout.addWidget(checkbox, 0, Qt.AlignVCenter)  # 垂直居中

            # 右边弹性空间
            layout.addStretch()

            self.table_widget.setCellWidget(row, 0, widget)

            # 商品ID
            item_id = product.get('itemId', '')
            self.update_table_cell_by_header(row, "商品ID", str(item_id))

            # 商品标题
            name = product.get('name', '')
            name_item = QTableWidgetItem(name)
            name_item.setToolTip(name)  # 设置工具提示显示完整标题
            col = self.get_column_index_by_header("商品标题")
            if col >= 0:
                self.table_widget.setItem(row, col, name_item)

            # 价格 (需要除以100)
            price = product.get('price', 0)
            if isinstance(price, (int, float)):
                price_yuan = price / 100 if price > 0 else 0
                self.update_table_cell_by_header(row, "价格", f"{price_yuan:.2f}")
            else:
                self.update_table_cell_by_header(row, "价格", str(price))

            # 佣金 (需要除以10)
            commission_rate = product.get('commissionRate', 0)
            commission_percent = commission_rate / 10 if commission_rate > 0 else 0
            self.update_table_cell_by_header(row, "佣金", f"{commission_percent}%")

            # 推广状态 - 根据status判断
            status = product.get('status', 0)
            if status == 1:
                promotion_status = "推广中"
            elif status == 3:
                promotion_status = "已关闭"
            else:
                promotion_status = "未知状态"
            self.update_table_cell_by_header(row, "推广状态", promotion_status)

            # 操作状态 - 默认为空
            self.update_table_cell_by_header(row, "操作状态", "")

            # 店铺 - 优先使用商品中的店铺信息，如果没有则使用传入的店铺名称
            product_shop_name = product.get('shop_name', shop_name)
            self.update_table_cell_by_header(row, "店铺", product_shop_name)

        except Exception as e:
            print(f"添加第{row+1}行搜索结果数据失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = PlanManager()
    window.show()
    sys.exit(app.exec_())
