"""
内嵌浏览器组件模块

功能：
- 使用PyQt5的QWebEngineView提供内嵌浏览器功能
- 替代Selenium外部浏览器，提供更好的用户体验
- 支持cookie设置和快手订单详情查看
- 现代化UI设计，与主程序风格一致

作者：快手小店管理系统
创建时间：2025年
"""

import sys
import warnings
import json
import time
from urllib.parse import urlencode

# 忽略PyQt5的弃用警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="sip")

# 全局窗口管理器 - 使用进程级全局变量确保跨导入共享
import sys
import os

# 使用进程级全局变量存储窗口管理器
_GLOBAL_BROWSER_WINDOWS = None

def get_browser_windows():
    """获取全局窗口管理器，确保跨模块导入共享"""
    global _GLOBAL_BROWSER_WINDOWS

    # 如果全局管理器不存在，创建新的
    if _GLOBAL_BROWSER_WINDOWS is None:
        print(f"🆕 创建进程级全局窗口管理器 (PID: {os.getpid()})")
        _GLOBAL_BROWSER_WINDOWS = {}
        return _GLOBAL_BROWSER_WINDOWS

    print(f"🔗 复用进程级全局窗口管理器 (PID: {os.getpid()}, ID: {id(_GLOBAL_BROWSER_WINDOWS)})")
    return _GLOBAL_BROWSER_WINDOWS

# 注意：不再使用全局变量，改为通过 get_browser_windows() 函数获取

try:
    from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout,
                                QPushButton, QMessageBox, QMainWindow,
                                QApplication, QFrame, QSizePolicy)
    from PyQt5.QtCore import Qt, QUrl, pyqtSignal, QCoreApplication
    from PyQt5.QtGui import QIcon

    # 设置Qt属性以支持WebEngine
    try:
        QCoreApplication.setAttribute(Qt.AA_ShareOpenGLContexts, True)
        print("✅ 已设置Qt::AA_ShareOpenGLContexts属性")
    except Exception as attr_error:
        print(f"⚠️ 设置Qt属性失败: {attr_error}")

    from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEngineProfile, QWebEnginePage
    from PyQt5.QtWebEngineCore import QWebEngineHttpRequest, QWebEngineUrlRequestInterceptor

    # 尝试导入Cookie相关类
    try:
        from PyQt5.QtWebEngineCore import QWebEngineCookieStore
        from PyQt5.QtNetwork import QNetworkCookie
        COOKIE_STORE_AVAILABLE = True
        print("✅ QWebEngineCookieStore 可用")
    except ImportError:
        COOKIE_STORE_AVAILABLE = False
        print("⚠️ QWebEngineCookieStore 不可用，将使用网络拦截方法")

    WEBENGINE_AVAILABLE = True
except ImportError:
    WEBENGINE_AVAILABLE = False
    COOKIE_STORE_AVAILABLE = False
    print("警告: PyQtWebEngine未安装，将回退到Selenium方式")
    # 创建占位符类
    class QWebEngineView:
        pass
    class QWebEngineProfile:
        pass
    class QWebEnginePage:
        pass





class CustomWebEnginePage(QWebEnginePage):
    """
    自定义WebEngine页面类

    功能说明:
    - 重写createWindow方法让新窗口请求在当前窗口打开
    - 支持target="_blank"链接和JavaScript window.open()在原窗口响应
    - 处理各种链接点击和导航请求
    """

    def __init__(self, profile, parent=None):
        super().__init__(profile, parent)
        self.main_browser = None  # 主浏览器窗口引用

    def set_main_browser(self, browser):
        """设置主浏览器窗口引用"""
        self.main_browser = browser

    def acceptNavigationRequest(self, url, navigation_type, is_main_frame):
        """
        重写导航请求处理方法
        处理所有导航请求，包括新窗口链接

        参数:
            url: 请求的URL
            navigation_type: 导航类型
            is_main_frame: 是否为主框架

        返回:
            bool: 是否接受导航请求
        """
        try:
            url_string = url.toString()
            print(f"🔗 导航请求: {url_string}, 类型: {navigation_type}, 主框架: {is_main_frame}")

            # 处理链接点击（包括target="_blank"和JavaScript window.open()）
            if navigation_type == QWebEnginePage.NavigationTypeLinkClicked:
                print(f"🖱️ 检测到链接点击: {url_string}")

                # 在当前窗口中打开链接，而不是创建新窗口
                if hasattr(self, 'main_browser') and self.main_browser:
                    print("🔄 在当前窗口中打开链接")
                    self.main_browser.web_view.load(url)
                    return False  # 拒绝原始导航请求，因为我们已经手动处理了
                else:
                    # 如果没有主浏览器引用，直接在当前页面加载
                    print("🔄 直接在当前页面加载链接")
                    self.load(url)
                    return False

            # 其他类型的导航请求正常处理
            return super().acceptNavigationRequest(url, navigation_type, is_main_frame)

        except Exception as e:
            print(f"❌ 处理导航请求时出错: {str(e)}")
            return super().acceptNavigationRequest(url, navigation_type, is_main_frame)

    def createWindow(self, window_type):
        """
        重写createWindow方法处理新窗口请求
        安全地拒绝新窗口创建，依赖JavaScript重写处理

        参数:
            window_type: 窗口类型 (WebBrowserTab, WebBrowserWindow, WebDialog)

        返回:
            None: 拒绝创建新窗口，让JavaScript重写处理
        """
        try:
            print(f"🔗 收到新窗口创建请求，类型: {window_type}")
            print("🚫 拒绝创建新窗口，依赖JavaScript重写处理URL跳转")

            # 直接返回None，让JavaScript的window.open重写来处理
            # 这是最安全稳定的方式
            return None

        except Exception as e:
            print(f"❌ 处理新窗口请求时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None



class KuaishouOrderBrowser(QMainWindow):
    """
    快手订单内嵌浏览器窗口

    功能说明:
    - 使用QWebEngineView内嵌浏览器组件
    - 自动设置店铺cookie并访问订单详情
    - 提供现代化的用户界面
    - 支持页面加载进度显示
    - 支持JavaScript链接点击和新窗口请求在原窗口打开
    """

    # 定义信号用于线程安全的UI更新
    from PyQt5.QtCore import pyqtSignal
    update_button_signal = pyqtSignal(str, bool)

    def __init__(self, shop_name, cookie_str, url, parent=None):
        try:
            super().__init__()  # 创建独立窗口，不设置父窗口

            # 检查WebEngine是否可用
            if not WEBENGINE_AVAILABLE:
                QMessageBox.critical(self, "错误",
                                   "PyQtWebEngine未安装，无法使用内嵌浏览器功能。\n"
                                   "请安装PyQtWebEngine或使用外部浏览器模式。")
                self.close()
                return
        except Exception as e:
            print(f"❌ 内嵌浏览器初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return

        try:
            self.shop_name = shop_name
            self.cookie_str = cookie_str
            self.target_url = url

            # 注册到全局窗口管理器
            browser_windows = get_browser_windows()
            print(f"🔧 注册窗口到管理器: '{shop_name}'")
            print(f"🔧 窗口管理器ID: {id(browser_windows)}")
            print(f"🔧 注册前窗口数量: {len(browser_windows)}")
            browser_windows[shop_name] = self
            print(f"🔧 注册后窗口数量: {len(browser_windows)}")
            print(f"🔧 窗口已成功注册: '{shop_name}' -> {id(self)}")

            # 初始化UI
            self.init_ui()

            # 连接信号槽
            self.update_button_signal.connect(self.show_button_message)

            # 初始化浏览器
            self.init_browser()

            # 创建店铺详情提示组件
            self.init_shop_detail_tooltip()

            # 开始加载页面
            self.load_page()

        except Exception as e:
            print(f"❌ 内嵌浏览器初始化过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()
            # 显示错误消息给用户
            try:
                QMessageBox.critical(self, "初始化错误",
                                   f"内嵌浏览器初始化失败:\n{str(e)}\n\n请检查软件完整性或联系技术支持。")
            except:
                pass
            self.close()

    def init_shop_detail_tooltip(self):
        """初始化店铺详情提示组件"""
        try:
            # 导入店铺详情提示类
            from tool.详情统计 import ShopDetailTooltip
            self.shop_detail_tooltip = ShopDetailTooltip(self)
            print("✅ 店铺详情提示组件初始化成功")
        except ImportError as e:
            print(f"⚠️ 无法导入ShopDetailTooltip类: {str(e)}")
            self.shop_detail_tooltip = None

    def get_window_title_by_url(self):
        """
        根据URL判断窗口类型并返回相应的标题

        功能:
        - 如果URL包含订单详情相关路径，显示"店铺名称-订单详情"
        - 如果URL是店铺后台主页，显示"店铺名称-店铺后台"
        - 其他情况显示"店铺名称-快手小店"

        返回值:
            str: 窗口标题
        """
        try:
            url = self.target_url.lower()

            # 判断是否为订单详情页面
            if '/order/detail' in url or '/zone/order/detail' in url:
                return f"{self.shop_name}-订单详情"

            # 判断是否为店铺后台主页
            elif url.endswith('/zone/home') or url.endswith('/zone/home/') or '/zone/home?' in url:
                return f"{self.shop_name}-店铺后台"

            # 判断是否为其他店铺后台页面
            elif '/zone/' in url and 'kwaixiaodian.com' in url:
                return f"{self.shop_name}-店铺后台"

            # 默认情况
            else:
                return f"{self.shop_name}-快手小店"

        except Exception as e:
            print(f"判断窗口标题时出错: {str(e)}")
            return f"{self.shop_name}-快手小店"

    def init_ui(self):
        """初始化用户界面"""
        # 根据URL判断窗口类型并设置标题
        window_title = self.get_window_title_by_url()
        self.setWindowTitle(window_title)
        self.setGeometry(100, 100, 1502, 800)  # 设置为1600*900

        # 设置无边框窗口，以便自定义标题栏
        self.setWindowFlags(Qt.FramelessWindowHint)

        # 设置窗口属性，启用圆角
        self.setAttribute(Qt.WA_TranslucentBackground, True)

        # 设置窗口图标（使用软件运行目录）
        try:
            import os
            import sys

            # 获取软件运行目录
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller打包后
                software_dir = os.path.dirname(sys.executable)
            else:
                # 开发环境
                software_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

            icon_path = os.path.join(software_dir, "config", "imges", "logo.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
                print(f"✅ 内嵌浏览器窗口图标设置成功: {icon_path}")
            else:
                print(f"⚠️ 图标文件不存在: {icon_path}")
        except Exception as e:
            print(f"⚠️ 设置内嵌浏览器窗口图标失败: {e}")
            pass

        # 创建中央窗口部件
        central_widget = QWidget()
        central_widget.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                border: 1px solid #ddd;
                border-radius: 12px;
            }
        """)
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建自定义标题栏
        self.create_custom_title_bar(main_layout)

        # 创建顶部工具栏
        self.create_toolbar(main_layout)

        # 创建浏览器视图容器（用于实现圆角效果）
        self.web_container = QFrame()
        self.web_container.setObjectName("web_container")
        self.web_container.setStyleSheet("""
            QFrame#web_container {
                border-left: 1px solid #ddd;
                border-right: 1px solid #ddd;
                border-bottom: 1px solid #ddd;
                border-bottom-left-radius: 12px;
                border-bottom-right-radius: 12px;
                background-color: white;
                padding: 2px 3px 2px 2px;
                margin: 0px;
            }
        """)

        # 创建容器布局
        container_layout = QVBoxLayout(self.web_container)
        container_layout.setContentsMargins(2, 0, 3, 2)  # 左、上、右、下边距（右边3px灰色内边距）
        container_layout.setSpacing(0)

        # 创建浏览器视图
        self.web_view = QWebEngineView()
        self.web_view.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.web_view.setStyleSheet("""
            QWebEngineView {
                border: none;
                border-radius: 10px;
                background-color: white;
            }
        """)

        # 将浏览器视图添加到容器中
        container_layout.addWidget(self.web_view)
        main_layout.addWidget(self.web_container)



        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: transparent;
                border: none;
            }
            QFrame#nav_toolbar {
                background-color: #f8f9fa;
                border-bottom: 1px solid #e0e0e0;
                border-left: 1px solid #ddd;
                border-right: 1px solid #ddd;
                padding: 0px;
            }

            QPushButton {
                background-color: #ffffff;
                color: #333;
                border: 1px solid #ddd;
                border-radius: 6px;
                font-size: 12px;
                font-weight: normal;
                text-align: center;
                padding: 0px;
                margin: 0px;
            }
            QPushButton:hover {
                background-color: #f0f0f0;
                border-color: #bbb;
            }
            QPushButton:pressed {
                background-color: #e0e0e0;
            }
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                background-color: white;
                color: #333;
                selection-background-color: #4CAF50;
                margin: 0px;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                outline: none;
                background-color: #ffffff;
            }



        """)



    def set_white_title_bar(self):
        """设置白色标题栏"""
        try:
            import ctypes

            # 获取窗口句柄
            hwnd = int(self.winId())

            # Windows API 常量
            DWMWA_USE_IMMERSIVE_DARK_MODE = 20
            DWMWA_CAPTION_COLOR = 35

            # 设置标题栏为浅色模式（白色）
            value = ctypes.c_int(0)  # 0 = 浅色模式, 1 = 深色模式
            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_USE_IMMERSIVE_DARK_MODE,
                ctypes.byref(value),
                ctypes.sizeof(value)
            )

            # 设置标题栏颜色为白色 (RGB: 255, 255, 255)
            # Windows使用BGR格式，所以白色是0x00FFFFFF
            color_value = ctypes.c_int(0x00FFFFFF)
            ctypes.windll.dwmapi.DwmSetWindowAttribute(
                hwnd,
                DWMWA_CAPTION_COLOR,
                ctypes.byref(color_value),
                ctypes.sizeof(color_value)
            )

            print("内嵌浏览器标题栏颜色已设置为白色")

        except Exception as e:
            print(f"设置标题栏颜色时出错: {str(e)}")
            # 如果设置失败，不影响程序运行

    def showEvent(self, event):
        """窗口显示事件 - 在窗口显示后设置标题栏颜色"""
        super().showEvent(event)
        # 延迟设置标题栏颜色，确保窗口完全创建
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(100, self.set_white_title_bar)

    def create_custom_title_bar(self, main_layout):
        """创建自定义标题栏"""
        # 创建标题栏容器
        title_bar = QFrame()
        title_bar.setObjectName("title_bar")
        title_bar.setFixedHeight(35)  # 标题栏高度
        title_bar.setStyleSheet("""
            QFrame#title_bar {
                background-color: #ffffff;
                border-bottom: 1px solid #e0e0e0;
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
                border: 1px solid #ddd;
                border-bottom: none;
            }
            QPushButton {
                background-color: transparent;
                border: none;
                color: #333;
                font-size: 14px;
                font-weight: normal;
                padding: 0px;
                margin: 0px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #f0f0f0;
            }
            QPushButton:pressed {
                background-color: #e0e0e0;
            }
            QPushButton#close_btn {
                border-top-right-radius: 12px;
            }
            QPushButton#close_btn:hover {
                background-color: #ff4444;
                color: white;
                border-top-right-radius: 12px;
            }
            QPushButton#close_btn:pressed {
                background-color: #cc0000;
                border-top-right-radius: 12px;
            }
            QLabel {
                color: #333;
                font-size: 13px;
                font-weight: normal;
                padding-left: 10px;
            }
        """)

        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(0)

        # 窗口图标和标题
        from PyQt5.QtWidgets import QLabel
        window_title = self.get_window_title_by_url()
        self.title_label = QLabel(window_title)

        # 为标题标签添加鼠标事件，支持店铺详情显示
        self.title_label.setMouseTracking(True)
        self.title_label.mouseMoveEvent = lambda event: self.title_label_mouse_move_event(event, self.title_label)
        self.title_label.leaveEvent = lambda event: self.title_label_leave_event(event)

        title_layout.addWidget(self.title_label)

        # 添加弹性空间，将按钮推到右边
        title_layout.addStretch()

        # 最小化按钮
        minimize_btn = QPushButton("🗕")
        minimize_btn.setFixedSize(45, 35)
        minimize_btn.setToolTip("最小化")
        minimize_btn.clicked.connect(self.showMinimized)
        title_layout.addWidget(minimize_btn)

        # 最大化/还原按钮
        self.maximize_btn = QPushButton("🗖")
        self.maximize_btn.setFixedSize(45, 35)
        self.maximize_btn.setToolTip("最大化")
        self.maximize_btn.clicked.connect(self.toggle_maximize)
        title_layout.addWidget(self.maximize_btn)

        # 关闭按钮
        close_btn = QPushButton("✕")
        close_btn.setObjectName("close_btn")
        close_btn.setFixedSize(45, 35)
        close_btn.setToolTip("关闭")
        close_btn.clicked.connect(self.close)
        title_layout.addWidget(close_btn)

        main_layout.addWidget(title_bar)

        # 保存标题栏引用，用于拖拽
        self.title_bar = title_bar

        # 添加鼠标事件处理，用于拖拽窗口
        self.title_bar.mousePressEvent = self.title_bar_mouse_press_event
        self.title_bar.mouseMoveEvent = self.title_bar_mouse_move_event
        self.title_bar.mouseDoubleClickEvent = self.title_bar_double_click_event

    def title_bar_mouse_press_event(self, event):
        """标题栏鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def title_bar_mouse_move_event(self, event):
        """标题栏鼠标移动事件"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def title_bar_double_click_event(self, event):
        """标题栏双击事件"""
        if event.button() == Qt.LeftButton:
            self.toggle_maximize()
            event.accept()

    def title_label_mouse_move_event(self, event, title_label):
        """标题标签鼠标移动事件 - 显示店铺详情"""
        try:
            if not hasattr(self, 'shop_detail_tooltip') or not self.shop_detail_tooltip:
                return

            # 从标题中提取店铺名称
            title_text = title_label.text()
            shop_name = self.extract_shop_name_from_title(title_text)

            if shop_name:
                # 将鼠标位置转换为全局坐标
                global_pos = title_label.mapToGlobal(event.pos())

                # 显示店铺详情信息框
                self.shop_detail_tooltip.show_shop_detail(shop_name, global_pos)
                print(f"显示店铺详情: {shop_name}")

        except Exception as e:
            print(f"处理标题标签鼠标移动事件时出错: {str(e)}")

    def title_label_leave_event(self, event):
        """标题标签鼠标离开事件 - 隐藏店铺详情"""
        try:
            if hasattr(self, 'shop_detail_tooltip') and self.shop_detail_tooltip:
                self.shop_detail_tooltip.hide()
        except Exception as e:
            print(f"处理标题标签鼠标离开事件时出错: {str(e)}")

    def extract_shop_name_from_title(self, title_text):
        """从窗口标题中提取店铺名称"""
        try:
            # 标题格式：店铺名称-订单详情 或 店铺名称-店铺后台 或 店铺名称-快手小店
            if '-' in title_text:
                shop_name = title_text.split('-')[0].strip()
                return shop_name
            else:
                # 如果没有分隔符，返回整个标题
                return title_text.strip()
        except Exception as e:
            print(f"提取店铺名称时出错: {str(e)}")
            return None

    def toggle_maximize(self):
        """切换最大化/还原"""
        if self.isMaximized():
            self.showNormal()
            self.maximize_btn.setText("🗖")
            self.maximize_btn.setToolTip("最大化")
            print("窗口还原")
        else:
            self.showMaximized()
            self.maximize_btn.setText("🗗")
            self.maximize_btn.setToolTip("还原")
            print("窗口最大化")

    def create_toolbar(self, main_layout):
        """创建顶部工具栏"""
        # 创建导航工具栏
        nav_toolbar = QFrame()
        nav_toolbar.setObjectName("nav_toolbar")
        nav_toolbar.setFixedHeight(50)  # 增加高度以便更好对齐

        nav_layout = QHBoxLayout(nav_toolbar)
        nav_layout.setContentsMargins(8, 8, 8, 8)  # 统一边距
        nav_layout.setSpacing(8)  # 设置控件间距

        # 后退按钮
        back_btn = QPushButton("←")
        back_btn.setFixedSize(36, 34)  # 统一按钮大小
        back_btn.setToolTip("后退")
        back_btn.clicked.connect(self.go_back)
        nav_layout.addWidget(back_btn)

        # 前进按钮
        forward_btn = QPushButton("→")
        forward_btn.setFixedSize(36, 34)
        forward_btn.setToolTip("前进")
        forward_btn.clicked.connect(self.go_forward)
        nav_layout.addWidget(forward_btn)

        # 刷新按钮
        refresh_btn = QPushButton("⟳")
        refresh_btn.setFixedSize(36, 34)
        refresh_btn.setToolTip("刷新")
        refresh_btn.clicked.connect(self.refresh_page)
        nav_layout.addWidget(refresh_btn)

        # 主页按钮
        home_btn = QPushButton("🏠")
        home_btn.setFixedSize(36, 34)
        home_btn.setToolTip("快手小店主页")
        home_btn.clicked.connect(self.go_to_home)
        nav_layout.addWidget(home_btn)

        # 地址栏
        from PyQt5.QtWidgets import QLineEdit
        self.address_bar = QLineEdit()
        self.address_bar.setPlaceholderText("输入网址...")
        self.address_bar.setFixedHeight(34)  # 设置固定高度与按钮对齐
        self.address_bar.returnPressed.connect(self.navigate_to_url)
        nav_layout.addWidget(self.address_bar)

        # 复制按钮
        copy_btn = QPushButton("📋")
        copy_btn.setFixedSize(36, 34)
        copy_btn.setToolTip("复制地址栏网址")
        copy_btn.clicked.connect(self.copy_url)
        nav_layout.addWidget(copy_btn)

        # 快手小店功能按钮
        self.create_kuaishou_buttons(nav_layout)

        # 转到按钮
        go_btn = QPushButton("转到")
        go_btn.setFixedSize(50, 34)  # 设置固定大小
        go_btn.clicked.connect(self.navigate_to_url)
        nav_layout.addWidget(go_btn)

        main_layout.addWidget(nav_toolbar)

    def create_kuaishou_buttons(self, nav_layout):
        """创建快手小店功能按钮"""
        # 快手小店功能按钮配置
        kuaishou_buttons = [
            {
                "text": "快分销",
                "url": "https://cps.kwaixiaodian.com/zone-cps/home",
                "tooltip": "快手分销中心"
            },
            {
                "text": "图文创作",
                "url": "https://s.kwaixiaodian.com/zone/les/visual",
                "tooltip": "图文创作工具"
            },
            {
                "text": "保证金",
                "url": "https://s.kwaixiaodian.com/zone/fund/accounting/deposit/recharge/home",
                "tooltip": "保证金管理"
            },
            {
                "text": "生意通",
                "url": "https://syt.kwaixiaodian.com/zones/rankingList/industry_search_rank",
                "tooltip": "生意通数据分析"
            },
            {
                "text": "运费",
                "url": "https://s.kwaixiaodian.com/zone/insurance/freight/index",
                "tooltip": "运费设置"
            },
            {
                "text": "达人推广",
                "url": "https://cps.kwaixiaodian.com/pc/promoter/selection-center/home",
                "tooltip": "达人推广中心"
            }
        ]

        # 创建按钮
        for btn_config in kuaishou_buttons:
            btn = QPushButton(btn_config["text"])
            btn.setFixedSize(70, 34)  # 设置按钮大小
            btn.setToolTip(btn_config["tooltip"])
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #4162B9;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 12px;
                    font-weight: normal;
                    text-align: center;
                    padding: 0px;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #3651A8;
                    border-radius: 8px;
                }
                QPushButton:pressed {
                    background-color: #2D4397;
                    border-radius: 8px;
                }
            """)

            # 绑定点击事件，使用lambda捕获URL
            btn.clicked.connect(lambda checked=False, url=btn_config["url"]: self.navigate_to_kuaishou_url(url))
            nav_layout.addWidget(btn)

        # 添加保存cookie按钮
        self.create_save_cookie_button(nav_layout)

    def create_save_cookie_button(self, nav_layout):
        """创建保存cookie按钮"""
        # 保存cookie按钮
        self.save_cookie_btn = QPushButton("保存Cookie")
        self.save_cookie_btn.setFixedSize(90, 34)  # 稍微宽一点以容纳文字
        self.save_cookie_btn.setToolTip("保存当前浏览器cookie到账号管理文件")
        self.save_cookie_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 12px;
                font-weight: normal;
                text-align: center;
                padding: 0px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #218838;
                border-radius: 8px;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
                border-radius: 8px;
            }
        """)

        # 绑定点击事件
        self.save_cookie_btn.clicked.connect(self.save_current_cookies)
        nav_layout.addWidget(self.save_cookie_btn)

    def navigate_to_kuaishou_url(self, url):
        """导航到快手小店功能页面"""
        try:
            print(f"🔗 导航到快手小店功能页面: {url}")

            # 更新地址栏
            self.address_bar.setText(url)

            # 设置cookie并导航
            if hasattr(self, 'cookie_str') and self.cookie_str:
                print(f"使用店铺 '{self.shop_name}' 的cookie导航到: {url}")
                self.set_cookies_and_navigate_to_url(url)
            else:
                print("直接导航到目标页面")
                self.web_view.load(QUrl(url))

        except Exception as e:
            print(f"❌ 导航到快手小店功能页面时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"导航失败: {str(e)}")

    def save_current_cookies(self):
        """保存当前浏览器的cookie到账号管理文件（异步执行）"""
        try:
            print(f"🍪 开始保存店铺 '{self.shop_name}' 的cookie...")

            # 显示正在处理状态
            self.show_button_message("正在获取...", is_success=None)

            # 在后台线程中执行cookie获取，避免阻塞界面
            import threading
            from PyQt5.QtCore import QTimer

            def background_save():
                try:
                    # 使用简化的方法获取cookie
                    cookie_string = self.get_cookies_simple_fast()

                    # 使用QTimer在主线程中更新UI
                    if not cookie_string:
                        print("❌ 未获取到任何cookie")
                        QTimer.singleShot(0, lambda: self.show_button_message("获取Cookie失败", False))
                        return

                    print(f"🍪 获取到cookie，长度: {len(cookie_string)}")

                    # 保存到账号管理文件
                    success = self.save_cookie_to_account_file(cookie_string)

                    if success:
                        print(f"✅ 店铺 '{self.shop_name}' 的cookie已成功保存")
                        # 使用信号槽机制更新UI
                        self.update_button_signal.emit("保存成功", True)
                    else:
                        self.update_button_signal.emit("保存失败", False)

                except Exception as e:
                    print(f"❌ 后台保存cookie时出错: {str(e)}")
                    self.update_button_signal.emit("保存出错", False)

            # 启动后台线程
            thread = threading.Thread(target=background_save)
            thread.daemon = True
            thread.start()

        except Exception as e:
            print(f"❌ 启动保存cookie线程时出错: {str(e)}")
            self.show_button_message("启动失败", is_success=False)

    def get_cookies_simple_fast(self):
        """直接使用Chrome无头模式获取cookie（唯一可靠方法）"""
        try:
            print("🤖 使用Chrome无头模式获取完整cookie...")
            # 直接使用Chrome无头模式，这是唯一可靠的方法
            return self.get_current_browser_cookies()

        except Exception as e:
            print(f"❌ 获取cookie失败: {str(e)}")
            return ""

    def get_current_browser_cookies(self):
        """获取当前浏览器的完整cookie字符串 - 使用Chrome无头模式"""
        try:
            print("🍪 开始获取cookie（使用Chrome无头模式方法）...")

            # 方法1：使用Chrome无头模式获取完整cookie
            cookie_from_headless = self.get_cookies_from_headless_chrome()
            if cookie_from_headless:
                print(f"✅ 通过Chrome无头模式获取到完整cookie，长度: {len(cookie_from_headless)}")
                return cookie_from_headless

            # 方法2：尝试从浏览器配置文件获取cookie（备用）
            print("⚠️ Chrome无头模式失败，尝试配置文件方法...")
            cookie_from_profile = self.get_cookies_from_profile_direct()
            if cookie_from_profile:
                print(f"✅ 通过配置文件获取到完整cookie，长度: {len(cookie_from_profile)}")
                return cookie_from_profile

            # 方法3：使用JavaScript获取cookie（最后备用方案）
            print("⚠️ 配置文件方法失败，使用JavaScript方法...")
            js_code = """
            // 直接返回document.cookie的完整字符串
            document.cookie;
            """

            # 执行JavaScript并获取结果
            def handle_cookies_result(result):
                try:
                    if result and isinstance(result, str):
                        print(f"🍪 通过JavaScript获取到cookie字符串，长度: {len(result)}")
                        self._js_cookies_result = result
                    else:
                        print("⚠️ JavaScript返回的cookie格式不正确")
                        self._js_cookies_result = ""
                except Exception as e:
                    print(f"❌ 处理JavaScript cookie结果时出错: {str(e)}")
                    self._js_cookies_result = ""

            # 初始化结果变量
            self._js_cookies_result = None

            # 执行JavaScript
            if hasattr(self, 'page') and self.page:
                self.page.runJavaScript(js_code, handle_cookies_result)

                # 等待JavaScript执行完成
                from PyQt5.QtCore import QEventLoop, QTimer
                loop = QEventLoop()
                timer = QTimer()
                timer.timeout.connect(loop.quit)
                timer.start(1000)  # 等待1秒

                # 等待结果或超时
                while self._js_cookies_result is None and timer.isActive():
                    loop.processEvents()

                timer.stop()

                if self._js_cookies_result is not None:
                    return self._js_cookies_result
                else:
                    print("⚠️ JavaScript执行超时")

            return ""

        except Exception as e:
            print(f"❌ 获取浏览器cookie时出错: {str(e)}")
            return ""



    def get_cookies_from_headless_chrome(self):
        """使用Chrome无头模式获取完整cookie"""
        try:
            print("🤖 启动Chrome无头模式获取完整cookie...")

            # 导入selenium
            try:
                from selenium import webdriver
                from selenium.webdriver.chrome.options import Options
                from selenium.webdriver.chrome.service import Service
                import time
                import os
            except ImportError:
                print("❌ Selenium未安装，无法使用Chrome无头模式")
                return ""

            # 获取当前页面的cookie（用于登录）
            current_cookies = self.get_current_page_cookies_simple()
            if not current_cookies:
                print("❌ 无法获取当前页面cookie用于登录")
                return ""

            print(f"📋 当前页面cookie长度: {len(current_cookies)}")

            # 设置Chrome选项（极速优化）
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-web-security')
            chrome_options.add_argument('--allow-running-insecure-content')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')
            chrome_options.add_argument('--disable-images')  # 禁用图片加载
            chrome_options.add_argument('--disable-javascript')  # 禁用JavaScript
            # 极速优化参数
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-features=TranslateUI,VizDisplayCompositor')
            chrome_options.add_argument('--disable-ipc-flooding-protection')
            chrome_options.add_argument('--no-first-run')
            chrome_options.add_argument('--no-default-browser-check')
            chrome_options.add_argument('--disable-default-apps')
            chrome_options.add_argument('--disable-sync')
            chrome_options.add_argument('--disable-background-networking')
            chrome_options.add_argument('--disable-background-downloads')
            chrome_options.add_argument('--disable-component-update')
            chrome_options.add_argument('--disable-client-side-phishing-detection')
            chrome_options.add_argument('--disable-hang-monitor')
            chrome_options.add_argument('--disable-prompt-on-repost')
            chrome_options.add_argument('--disable-domain-reliability')
            chrome_options.add_argument('--disable-logging')
            chrome_options.add_argument('--silent')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')

            # 使用固定路径：软件目录/tool/chromedriver.exe
            import os
            import sys

            # 获取软件运行目录
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller打包后
                software_dir = os.path.dirname(sys.executable)
            else:
                # 开发环境
                software_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

            chromedriver_path = os.path.join(software_dir, 'tool', 'chromedriver.exe')

            print(f"🔧 使用Chrome驱动路径: {chromedriver_path}")

            if not os.path.exists(chromedriver_path):
                print(f"❌ Chrome驱动不存在: {chromedriver_path}")
                print(f"💡 请确保chromedriver.exe文件位于: {os.path.join(software_dir, 'tool')}")
                return ""

            # 创建Service对象
            try:
                service = Service(chromedriver_path)

                # 启动Chrome
                print("🚀 启动Chrome无头浏览器...")
                driver = webdriver.Chrome(service=service, options=chrome_options)
                print("✅ Chrome无头浏览器启动成功")
            except Exception as e:
                print(f"❌ Chrome无头浏览器启动失败: {str(e)}")
                print(f"🔧 使用的驱动路径: {chromedriver_path}")
                print("💡 请确保chromedriver.exe文件存在于软件目录中")
                return ""

            try:
                # 极速优化：直接访问目标页面并设置cookie
                print("🚀 极速模式：直接访问目标页面...")
                driver.get("https://s.kwaixiaodian.com")

                # 一次性批量设置cookie（使用JavaScript）
                print("⚡ 一次性批量设置cookie...")

                # 构建JavaScript代码，一次性设置所有cookie
                js_cookie_commands = []
                cookie_pairs = current_cookies.split('; ')

                for cookie_pair in cookie_pairs:
                    if '=' in cookie_pair:
                        name, value = cookie_pair.split('=', 1)
                        name = name.strip()
                        value = value.strip()
                        # 构建cookie设置命令
                        js_cookie_commands.append(f"document.cookie = '{name}={value}; domain=.kwaixiaodian.com; path=/';")

                # 一次性执行所有cookie设置
                if js_cookie_commands:
                    js_code = '\n'.join(js_cookie_commands)
                    try:
                        driver.execute_script(js_code)
                        print(f"✅ 一次性设置 {len(js_cookie_commands)} 个cookie成功")
                    except Exception as e:
                        print(f"⚠️ JavaScript批量设置失败，回退到逐个设置: {str(e)}")
                        # 回退到原来的方法
                        success_count = 0
                        for cookie_pair in cookie_pairs:
                            if '=' in cookie_pair:
                                name, value = cookie_pair.split('=', 1)
                                try:
                                    driver.add_cookie({
                                        'name': name.strip(),
                                        'value': value.strip(),
                                        'domain': '.kwaixiaodian.com'
                                    })
                                    success_count += 1
                                except Exception:
                                    pass
                        print(f"✅ 逐个设置 {success_count}/{len(cookie_pairs)} 个cookie")

                # 最小化等待：直接访问后台页面
                print("🎯 直接访问后台页面...")
                driver.get("https://s.kwaixiaodian.com/zone/home")
                time.sleep(1)  # 减少等待时间到1秒

                # 立即获取所有cookie
                print("🍪 获取完整cookie...")
                all_cookies = driver.get_cookies()

                # 转换为cookie字符串
                cookie_pairs = []
                for cookie in all_cookies:
                    cookie_pairs.append(f"{cookie['name']}={cookie['value']}")

                cookie_string = '; '.join(cookie_pairs)

                # 检查关键cookie
                if 'kuaishou.shop.b_st=' in cookie_string:
                    print("✅ 包含关键cookie: kuaishou.shop.b_st")
                else:
                    print("⚠️ 缺少关键cookie: kuaishou.shop.b_st")

                print(f"🎉 Chrome无头模式成功获取cookie，总数: {len(all_cookies)}，长度: {len(cookie_string)}")
                return cookie_string

            finally:
                # 关闭浏览器
                print("🔒 关闭Chrome无头浏览器...")
                driver.quit()

        except Exception as e:
            print(f"❌ Chrome无头模式获取cookie失败: {str(e)}")
            return ""

    def get_current_page_cookies_simple(self):
        """简单获取当前页面的cookie用于登录"""
        try:
            if hasattr(self, 'page') and self.page:
                js_code = "document.cookie;"

                def handle_result(result):
                    self._simple_cookie_result = result if result else ""

                self._simple_cookie_result = None
                self.page.runJavaScript(js_code, handle_result)

                # 等待结果
                from PyQt5.QtCore import QEventLoop, QTimer
                loop = QEventLoop()
                timer = QTimer()
                timer.timeout.connect(loop.quit)
                timer.start(1000)

                while self._simple_cookie_result is None and timer.isActive():
                    loop.processEvents()

                timer.stop()
                return self._simple_cookie_result or ""

            return ""
        except Exception as e:
            print(f"❌ 获取当前页面cookie失败: {str(e)}")
            return ""

    def get_cookies_from_actual_request(self):
        """通过模拟用户操作获取完整cookie"""
        try:
            print("🚀 尝试通过模拟用户操作获取完整cookie...")

            # 更简单直接的方法：模拟用户在开发者工具中的操作
            js_code = """
            (function() {
                try {
                    // 方法1：尝试从浏览器存储中获取所有cookie
                    let allCookies = '';

                    // 获取当前页面的所有cookie
                    const documentCookies = document.cookie;
                    console.log('Document cookies:', documentCookies);

                    // 尝试从localStorage和sessionStorage获取可能的token
                    const localStorageData = {};
                    const sessionStorageData = {};

                    try {
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            localStorageData[key] = localStorage.getItem(key);
                        }
                    } catch (e) {}

                    try {
                        for (let i = 0; i < sessionStorage.length; i++) {
                            const key = sessionStorage.key(i);
                            sessionStorageData[key] = sessionStorage.getItem(key);
                        }
                    } catch (e) {}

                    console.log('LocalStorage:', localStorageData);
                    console.log('SessionStorage:', sessionStorageData);

                    // 尝试从window对象中获取可能的cookie信息
                    const windowCookieInfo = {};
                    if (window.__INITIAL_STATE__) {
                        windowCookieInfo.initialState = window.__INITIAL_STATE__;
                    }
                    if (window.__NUXT__) {
                        windowCookieInfo.nuxt = window.__NUXT__;
                    }

                    console.log('Window cookie info:', windowCookieInfo);

                    // 尝试通过网络请求获取cookie
                    const xhr = new XMLHttpRequest();
                    xhr.open('GET', window.location.href, false); // 同步请求
                    xhr.withCredentials = true;

                    // 重写setRequestHeader来捕获cookie
                    const originalSetRequestHeader = xhr.setRequestHeader;
                    let capturedHeaders = {};
                    xhr.setRequestHeader = function(name, value) {
                        capturedHeaders[name] = value;
                        return originalSetRequestHeader.call(this, name, value);
                    };

                    try {
                        xhr.send();
                        console.log('XHR response headers:', xhr.getAllResponseHeaders());
                        console.log('XHR captured headers:', capturedHeaders);
                    } catch (e) {
                        console.log('XHR failed:', e);
                    }

                    // 返回document.cookie，这是最可靠的方法
                    return documentCookies;

                } catch (error) {
                    console.log('模拟用户操作失败:', error);
                    return document.cookie || '';
                }
            })();
            """

            # 执行JavaScript并获取结果
            def handle_simulation_result(result):
                try:
                    if result and isinstance(result, str) and len(result) > 0:
                        print(f"🚀 通过模拟用户操作获取到cookie，长度: {len(result)}")
                        # 检查是否包含关键cookie
                        if 'kuaishou.shop.b_st=' in result:
                            print("✅ 包含关键cookie: kuaishou.shop.b_st")
                        else:
                            print("⚠️ 缺少关键cookie: kuaishou.shop.b_st")
                            print(f"📋 当前cookie内容: {result[:200]}...")
                        self._simulation_result = result
                    else:
                        print("⚠️ 模拟用户操作返回的cookie格式不正确或为空")
                        self._simulation_result = ""
                except Exception as e:
                    print(f"❌ 处理模拟用户操作cookie结果时出错: {str(e)}")
                    self._simulation_result = ""

            # 初始化结果变量
            self._simulation_result = None

            # 执行JavaScript
            if hasattr(self, 'page') and self.page:
                self.page.runJavaScript(js_code, handle_simulation_result)

                # 等待JavaScript执行完成
                from PyQt5.QtCore import QEventLoop, QTimer
                loop = QEventLoop()
                timer = QTimer()
                timer.timeout.connect(loop.quit)
                timer.start(3000)  # 等待3秒

                # 等待结果或超时
                while self._simulation_result is None and timer.isActive():
                    loop.processEvents()

                timer.stop()

                if self._simulation_result is not None:
                    return self._simulation_result
                else:
                    print("⚠️ 模拟用户操作执行超时")

            return ""

        except Exception as e:
            print(f"❌ 通过模拟用户操作获取cookie时出错: {str(e)}")
            return ""

    def get_cookies_from_network_headers(self):
        """通过多种方式获取完整的Cookie头，包括HttpOnly cookie"""
        try:
            print("🌐 尝试通过多种网络方式获取完整cookie...")

            # 方法1：通过开发者工具API获取所有cookie
            js_code_devtools = """
            (async function() {
                try {
                    // 尝试使用Chrome DevTools API获取所有cookie
                    if (window.chrome && window.chrome.runtime) {
                        return new Promise((resolve) => {
                            chrome.cookies.getAll({}, (cookies) => {
                                const cookieString = cookies.map(c => c.name + '=' + c.value).join('; ');
                                resolve(cookieString);
                            });
                        });
                    }

                    // 备用方案：通过网络请求拦截
                    const originalFetch = window.fetch;
                    let capturedCookies = '';

                    // 重写fetch来捕获cookie
                    window.fetch = function(...args) {
                        const request = new Request(...args);
                        // 记录请求中的cookie
                        if (request.headers.get('Cookie')) {
                            capturedCookies = request.headers.get('Cookie');
                        }
                        return originalFetch.apply(this, args);
                    };

                    // 发起一个API请求来触发cookie发送
                    const response = await fetch('/rest/pc/product/manage/kcf/item/manager/queryList', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        credentials: 'include',
                        body: JSON.stringify({
                            "managerTab": "ON_SALE",
                            "pagination": {"curPage": 1, "pageSize": 1},
                            "queryForm": {"soldQuantityPeriodQuery": "", "itemProfileQuery": "normal"},
                            "tableSort": {"volumeFilter": ""}
                        })
                    });

                    // 恢复原始fetch
                    window.fetch = originalFetch;

                    // 如果捕获到cookie，返回它
                    if (capturedCookies) {
                        return capturedCookies;
                    }

                    // 最后备用：返回document.cookie
                    return document.cookie;

                } catch (error) {
                    console.log('DevTools方法失败:', error);
                    return document.cookie;
                }
            })();
            """

            # 执行JavaScript并获取结果
            def handle_devtools_result(result):
                try:
                    if result and isinstance(result, str):
                        print(f"🔧 通过开发者工具API获取到cookie，长度: {len(result)}")
                        # 检查是否包含关键cookie
                        if 'kuaishou.shop.b_st=' in result:
                            print("✅ 包含关键cookie: kuaishou.shop.b_st")
                        else:
                            print("⚠️ 缺少关键cookie: kuaishou.shop.b_st")
                        self._devtools_cookies_result = result
                    else:
                        print("⚠️ 开发者工具API返回的cookie格式不正确")
                        self._devtools_cookies_result = ""
                except Exception as e:
                    print(f"❌ 处理开发者工具cookie结果时出错: {str(e)}")
                    self._devtools_cookies_result = ""

            # 初始化结果变量
            self._devtools_cookies_result = None

            # 执行JavaScript
            if hasattr(self, 'page') and self.page:
                self.page.runJavaScript(js_code_devtools, handle_devtools_result)

                # 等待JavaScript执行完成
                from PyQt5.QtCore import QEventLoop, QTimer
                loop = QEventLoop()
                timer = QTimer()
                timer.timeout.connect(loop.quit)
                timer.start(3000)  # 等待3秒

                # 等待结果或超时
                while self._devtools_cookies_result is None and timer.isActive():
                    loop.processEvents()

                timer.stop()

                if self._devtools_cookies_result is not None:
                    return self._devtools_cookies_result
                else:
                    print("⚠️ 开发者工具API执行超时")

            # 方法2：如果开发者工具API失败，使用简单的document.cookie
            print("🔄 开发者工具API失败，使用document.cookie备用方案...")
            js_code_simple = "document.cookie;"

            def handle_simple_result(result):
                try:
                    if result and isinstance(result, str):
                        print(f"📄 通过document.cookie获取到cookie，长度: {len(result)}")
                        self._simple_cookies_result = result
                    else:
                        print("⚠️ document.cookie返回的cookie格式不正确")
                        self._simple_cookies_result = ""
                except Exception as e:
                    print(f"❌ 处理document.cookie结果时出错: {str(e)}")
                    self._simple_cookies_result = ""

            self._simple_cookies_result = None

            if hasattr(self, 'page') and self.page:
                self.page.runJavaScript(js_code_simple, handle_simple_result)

                loop = QEventLoop()
                timer = QTimer()
                timer.timeout.connect(loop.quit)
                timer.start(1000)  # 等待1秒

                while self._simple_cookies_result is None and timer.isActive():
                    loop.processEvents()

                timer.stop()

                if self._simple_cookies_result is not None:
                    return self._simple_cookies_result

            return ""

        except Exception as e:
            print(f"❌ 通过网络请求头获取cookie时出错: {str(e)}")
            return ""

    def get_cookies_from_profile_direct(self):
        """直接从Chrome主配置文件SQLite数据库获取完整cookie"""
        try:
            print("🔍 尝试从Chrome主配置文件SQLite数据库直接获取完整cookie...")

            # 方法1：尝试从当前WebEngine配置文件获取
            if hasattr(self, 'page') and self.page:
                profile = self.page.profile()
                if profile and hasattr(profile, 'persistentStoragePath'):
                    storage_path = profile.persistentStoragePath()
                    print(f"📁 当前配置文件路径: {storage_path}")

                    import os
                    cookie_file_path = os.path.join(storage_path, 'Cookies')

                    if os.path.exists(cookie_file_path):
                        print(f"📄 找到当前配置文件cookie数据库: {cookie_file_path}")
                        result = self.read_chrome_cookies_from_sqlite(cookie_file_path)
                        if result and len(result) > 100:  # 只有当获取到足够多的cookie时才返回
                            print(f"✅ 当前配置文件cookie足够完整，长度: {len(result)}")
                            return result
                        else:
                            print(f"⚠️ 当前配置文件cookie不完整，长度: {len(result) if result else 0}，尝试主配置文件")

            # 方法2：尝试从用户的主Chrome配置文件获取
            print("🔍 当前配置文件无cookie，尝试从用户主Chrome配置文件获取...")
            import os

            # 获取用户名
            username = os.getenv('USERNAME') or os.getenv('USER')
            print(f"👤 当前用户: {username}")

            # Windows Chrome配置文件路径 - 更全面的路径列表
            chrome_paths = [
                # 标准Chrome路径
                os.path.expanduser(r'~\AppData\Local\Google\Chrome\User Data\Default\Cookies'),
                os.path.expanduser(r'~\AppData\Local\Google\Chrome\User Data\Profile 1\Cookies'),
                os.path.expanduser(r'~\AppData\Local\Google\Chrome\User Data\Profile 2\Cookies'),
                os.path.expanduser(r'~\AppData\Local\Google\Chrome\User Data\Profile 3\Cookies'),

                # 直接路径
                f'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Cookies',
                f'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 1\\Cookies',
                f'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Profile 2\\Cookies',

                # Edge浏览器路径
                os.path.expanduser(r'~\AppData\Local\Microsoft\Edge\User Data\Default\Cookies'),
                f'C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\Cookies',

                # Chrome Beta/Dev路径
                os.path.expanduser(r'~\AppData\Local\Google\Chrome Beta\User Data\Default\Cookies'),
                os.path.expanduser(r'~\AppData\Local\Google\Chrome Dev\User Data\Default\Cookies'),
            ]

            print(f"🔍 检查 {len(chrome_paths)} 个可能的Chrome配置文件路径...")

            for i, chrome_cookie_path in enumerate(chrome_paths):
                print(f"  [{i+1}] 检查: {chrome_cookie_path}")
                if os.path.exists(chrome_cookie_path):
                    print(f"📄 找到主Chrome配置文件cookie数据库: {chrome_cookie_path}")
                    result = self.read_chrome_cookies_from_sqlite(chrome_cookie_path)
                    if result and len(result) > 100:
                        print(f"✅ 从主Chrome配置文件成功获取cookie，长度: {len(result)}")
                        return result
                    else:
                        print(f"⚠️ 该配置文件cookie不完整，长度: {len(result) if result else 0}")
                else:
                    print(f"❌ 路径不存在")

            print("⚠️ 未找到任何可用的Chrome cookie数据库")
            return ""

        except Exception as e:
            print(f"❌ 从配置文件获取cookie时出错: {str(e)}")
            return ""

    def read_chrome_cookies_from_sqlite(self, cookie_file_path):
        """从Chrome的SQLite数据库中读取完整cookie"""
        try:
            import sqlite3
            import os

            # 获取当前页面的域名
            current_url = self.page.url()
            domain = current_url.host()
            print(f"🌐 当前域名: {domain}")

            # 复制cookie文件到临时位置（避免文件锁定）
            import tempfile
            import shutil
            temp_cookie_file = os.path.join(tempfile.gettempdir(), f"temp_cookies_{os.getpid()}.db")

            try:
                shutil.copy2(cookie_file_path, temp_cookie_file)
                print(f"📋 已复制cookie文件到临时位置: {temp_cookie_file}")
            except Exception as e:
                print(f"⚠️ 复制cookie文件失败，直接读取原文件: {str(e)}")
                temp_cookie_file = cookie_file_path

            # 连接SQLite数据库
            conn = sqlite3.connect(temp_cookie_file)
            cursor = conn.cursor()

            # 查询cookie表结构
            cursor.execute("PRAGMA table_info(cookies)")
            columns = [column[1] for column in cursor.fetchall()]
            print(f"📊 Cookie表字段: {columns}")

            # 先查看数据库中所有的域名
            cursor.execute("SELECT DISTINCT host_key FROM cookies ORDER BY host_key")
            all_domains = cursor.fetchall()
            print(f"📋 数据库中所有域名: {[d[0] for d in all_domains[:10]]}...")  # 只显示前10个

            # 查询相关域名的cookie - 使用更宽泛的匹配
            query = """
            SELECT name, value, encrypted_value, host_key, path, expires_utc,
                   is_secure, is_httponly, creation_utc, last_access_utc
            FROM cookies
            WHERE host_key LIKE ? OR host_key LIKE ? OR host_key LIKE ? OR host_key LIKE ?
            ORDER BY creation_utc DESC
            """

            # 更宽泛的域名匹配模式
            domain_patterns = [
                f'%{domain}%',           # 包含完整域名
                f'%.{domain}',           # 以.域名结尾
                f'{domain}',             # 完全匹配
                f'%kwaixiaodian%'        # 包含kwaixiaodian的任何域名
            ]
            cursor.execute(query, domain_patterns)

            cookies = cursor.fetchall()
            print(f"🍪 查询到 {len(cookies)} 个相关cookie")

            if not cookies:
                print("❌ 未找到相关域名的cookie")
                conn.close()
                return ""

            # 处理cookie数据
            cookie_pairs = []
            for cookie in cookies:
                name, value, encrypted_value, host_key, _, _, is_secure, is_httponly, _, _ = cookie

                print(f"  🍪 Cookie: {name}")
                print(f"    Host: {host_key}")
                print(f"    HttpOnly: {bool(is_httponly)}")
                print(f"    Secure: {bool(is_secure)}")
                print(f"    Value长度: {len(value) if value else 0}")
                print(f"    EncryptedValue长度: {len(encrypted_value) if encrypted_value else 0}")

                # 优先使用未加密的value
                if value:
                    cookie_pairs.append(f"{name}={value}")
                    print(f"    ✅ 使用未加密值")
                elif encrypted_value:
                    # 尝试解密encrypted_value
                    decrypted_value = self.decrypt_chrome_cookie_value(encrypted_value)
                    if decrypted_value:
                        cookie_pairs.append(f"{name}={decrypted_value}")
                        print(f"    ✅ 使用解密值")
                    else:
                        print(f"    ❌ 解密失败，跳过此cookie")
                else:
                    print(f"    ⚠️ 无可用值，跳过此cookie")

            conn.close()

            # 清理临时文件
            if temp_cookie_file != cookie_file_path:
                try:
                    os.remove(temp_cookie_file)
                except:
                    pass

            if cookie_pairs:
                cookie_string = "; ".join(cookie_pairs)
                print(f"✅ 成功从SQLite获取到 {len(cookie_pairs)} 个cookie，总长度: {len(cookie_string)}")
                return cookie_string
            else:
                print("❌ 没有可用的cookie")
                return ""

        except Exception as e:
            print(f"❌ 读取Chrome SQLite cookie时出错: {str(e)}")
            import traceback
            print(f"❌ 详细错误: {traceback.format_exc()}")
            return ""

    def decrypt_chrome_cookie_value(self, encrypted_value):
        """解密Chrome cookie的encrypted_value字段"""
        try:
            if not encrypted_value:
                return ""

            # Windows下的解密方法
            if os.name == 'nt':
                try:
                    import win32crypt
                    decrypted_data = win32crypt.CryptUnprotectData(encrypted_value, None, None, None, 0)
                    return decrypted_data[1].decode('utf-8')
                except ImportError:
                    print("⚠️ win32crypt模块不可用，无法解密")
                    return ""
                except Exception as e:
                    print(f"⚠️ Windows解密失败: {str(e)}")
                    return ""
            else:
                # Linux/Mac下的解密方法（需要更复杂的实现）
                print("⚠️ 当前系统不支持cookie解密")
                return ""

        except Exception as e:
            print(f"❌ 解密cookie值时出错: {str(e)}")
            return ""

    def get_cookies_from_cookie_store(self):
        """使用QWebEngineCookieStore获取所有cookie（包括HttpOnly）"""
        try:
            # 检查CookieStore是否可用
            if not COOKIE_STORE_AVAILABLE:
                print("❌ QWebEngineCookieStore 不可用，跳过此方法")
                return ""

            if not hasattr(self, 'page') or not self.page:
                print("❌ 页面对象不存在")
                return ""

            profile = self.page.profile()
            if not profile:
                print("❌ 无法获取WebEngine配置文件")
                return ""

            # 检查cookieStore方法是否存在
            if not hasattr(profile, 'cookieStore'):
                print("❌ 当前PyQt5版本不支持cookieStore方法")
                return ""

            cookie_store = profile.cookieStore()
            if not cookie_store:
                print("❌ 无法获取Cookie存储")
                return ""

            # 获取当前页面的URL
            current_url = self.page.url()
            domain = current_url.host()

            print(f"🔍 正在从CookieStore获取域名 '{domain}' 的cookie...")

            # 初始化cookie收集器
            self._collected_cookies = []

            # 定义cookie收集回调
            def collect_cookie(cookie):
                try:
                    # 检查cookie是否属于当前域名或相关域名
                    cookie_domain = cookie.domain()
                    if (domain in cookie_domain or cookie_domain in domain or
                        'kwaixiaodian.com' in cookie_domain or 'kuaishou.com' in cookie_domain):
                        name = bytes(cookie.name()).decode('utf-8')
                        value = bytes(cookie.value()).decode('utf-8')
                        self._collected_cookies.append(f"{name}={value}")
                        print(f"  📝 收集到cookie: {name}={value[:30]}{'...' if len(value) > 30 else ''}")
                except Exception as e:
                    print(f"❌ 处理cookie时出错: {str(e)}")

            # 修复：先连接信号，再调用loadAllCookies
            print("🔄 连接cookie信号并加载现有cookie...")

            # 连接所有相关信号
            cookie_store.cookieAdded.connect(collect_cookie)

            # 尝试获取所有现有cookie的方法
            try:
                # 方法1：直接遍历所有cookie（如果支持）
                if hasattr(cookie_store, 'getAllCookies'):
                    print("🔍 使用getAllCookies方法...")
                    all_cookies = cookie_store.getAllCookies()
                    for cookie in all_cookies:
                        collect_cookie(cookie)

                # 方法2：使用loadAllCookies触发信号
                elif hasattr(cookie_store, 'loadAllCookies'):
                    cookie_store.loadAllCookies()
                    print("✅ loadAllCookies()方法调用成功")
                else:
                    print("❌ 当前版本不支持cookie获取方法")
                    return ""
            except Exception as e:
                print(f"❌ 调用cookie方法时出错: {str(e)}")
                return ""

            # 等待cookie收集
            from PyQt5.QtCore import QTimer, QEventLoop
            loop = QEventLoop()
            timer = QTimer()
            timer.timeout.connect(loop.quit)
            timer.start(1500)  # 等待1.5秒收集cookie

            # 处理事件循环
            print("⏳ 开始等待cookie收集...")
            start_time = len(self._collected_cookies)
            elapsed = 0
            while timer.isActive():
                loop.processEvents()
                elapsed += 100
                if elapsed % 300 == 0:  # 每300ms打印一次状态
                    print(f"  ⏰ 已等待 {elapsed}ms，当前收集到 {len(self._collected_cookies)} 个cookie")
                # 如果已经收集到cookie，再等待一点时间确保收集完整
                if len(self._collected_cookies) > start_time and elapsed > 500:
                    print(f"⚡ 已收集到cookie，再等待一点时间确保完整")
                    break

                # 短暂休眠
                import time
                time.sleep(0.1)

            timer.stop()
            print(f"⏰ 等待结束，总共收集到 {len(self._collected_cookies)} 个cookie")

            # 如果没有收集到cookie，快速结束
            if not self._collected_cookies:
                print("⚠️ CookieStore方法未收集到cookie，将使用JavaScript方法")

            # 断开信号连接
            try:
                cookie_store.cookieAdded.disconnect(collect_cookie)
            except:
                pass

            if self._collected_cookies:
                cookie_string = "; ".join(self._collected_cookies)
                print(f"✅ CookieStore收集到 {len(self._collected_cookies)} 个cookie")
                return cookie_string
            else:
                print("⚠️ CookieStore未收集到任何cookie")
                return ""

        except Exception as e:
            print(f"❌ 使用CookieStore获取cookie时出错: {str(e)}")
            return ""

    def save_cookie_to_account_file(self, cookie_string):
        """保存cookie到账号管理文件"""
        try:
            import json
            import os

            # 获取账号管理文件路径（使用软件运行目录）
            import sys

            # 获取软件运行目录
            if hasattr(sys, '_MEIPASS'):
                # PyInstaller打包后
                software_dir = os.path.dirname(sys.executable)
            else:
                # 开发环境
                software_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

            config_path = os.path.join(software_dir, 'config', '账号管理.json')

            if not os.path.exists(config_path):
                print(f"❌ 账号管理文件不存在: {config_path}")
                return False

            # 读取现有数据
            with open(config_path, 'r', encoding='utf-8') as f:
                account_data = json.load(f)

            # 获取账号数据列表
            accounts = account_data.get('data', [])

            if not accounts:
                print("❌ 账号管理文件中没有账号数据")
                return False

            # 查找并更新对应店铺的cookie
            updated = False

            # 提取当前店铺的基础名称（去掉_后面的部分）
            current_shop_base_name = self.shop_name.split('_')[0].strip()
            print(f"🔍 查找店铺: '{self.shop_name}' (基础名称: '{current_shop_base_name}')")

            for account in accounts:
                if not isinstance(account, dict):
                    continue

                account_shop_name = account.get('店铺名称', '').strip()

                # 提取账号文件中的基础店铺名称
                account_base_name = account_shop_name.split('_')[0].strip()

                # 支持多种匹配方式
                match_found = False

                # 方式1：完全匹配
                if account_shop_name == self.shop_name:
                    match_found = True
                    print(f"✅ 完全匹配: '{account_shop_name}' == '{self.shop_name}'")

                # 方式2：基础名称匹配（支持 玉涵时尚女装_达人后台 格式）
                elif account_base_name == current_shop_base_name and current_shop_base_name:
                    match_found = True
                    print(f"✅ 基础名称匹配: '{account_base_name}' == '{current_shop_base_name}'")

                # 方式3：当前名称是账号名称的前缀
                elif account_shop_name.startswith(current_shop_base_name) and current_shop_base_name:
                    match_found = True
                    print(f"✅ 前缀匹配: '{account_shop_name}' 包含 '{current_shop_base_name}'")

                if match_found:
                    # 直接更新cookie字段 - 保存原始完整字符串
                    account['cookie'] = cookie_string
                    updated = True
                    print(f"🎉 成功更新店铺 '{account_shop_name}' 的cookie")
                    break

            if not updated:
                print(f"❌ 未找到店铺 '{self.shop_name}' 的账号信息")
                print("📋 可用的店铺列表:")
                for account in accounts:
                    if isinstance(account, dict):
                        shop_name = account.get('店铺名称', '未知')
                        print(f"  - {shop_name}")
                return False

            # 保存更新后的数据
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(account_data, f, ensure_ascii=False, indent=4)

            return True

        except Exception as e:
            print(f"❌ 保存cookie到文件时出错: {str(e)}")
            return False

    def show_button_message(self, message, is_success=True):
        """在保存cookie按钮上显示提示消息"""
        try:
            if not hasattr(self, 'save_cookie_btn'):
                return

            # 保存原始文本和样式
            if not hasattr(self, '_original_btn_text'):
                self._original_btn_text = self.save_cookie_btn.text()
                self._original_btn_style = self.save_cookie_btn.styleSheet()

            # 设置提示消息和样式
            self.save_cookie_btn.setText(message)

            if is_success is None:
                # 正在处理样式 - 蓝色
                processing_style = """
                    QPushButton {
                        background-color: #007bff;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        font-size: 12px;
                        font-weight: bold;
                        text-align: center;
                        padding: 0px;
                        margin: 2px;
                    }
                """
                self.save_cookie_btn.setStyleSheet(processing_style)
                # 正在处理时不自动恢复，等待结果
                return
            elif is_success:
                # 成功样式 - 绿色
                success_style = """
                    QPushButton {
                        background-color: #28a745;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        font-size: 12px;
                        font-weight: bold;
                        text-align: center;
                        padding: 0px;
                        margin: 2px;
                    }
                """
                self.save_cookie_btn.setStyleSheet(success_style)
            else:
                # 失败样式 - 红色
                error_style = """
                    QPushButton {
                        background-color: #dc3545;
                        color: white;
                        border: none;
                        border-radius: 8px;
                        font-size: 12px;
                        font-weight: bold;
                        text-align: center;
                        padding: 0px;
                        margin: 2px;
                    }
                """
                self.save_cookie_btn.setStyleSheet(error_style)

            # 2秒后恢复原始状态
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(2000, self.restore_button_state)

        except Exception as e:
            print(f"❌ 显示按钮消息时出错: {str(e)}")

    def restore_button_state(self):
        """恢复保存cookie按钮的原始状态"""
        try:
            if hasattr(self, 'save_cookie_btn') and hasattr(self, '_original_btn_text'):
                self.save_cookie_btn.setText(self._original_btn_text)
                self.save_cookie_btn.setStyleSheet(self._original_btn_style)
        except Exception as e:
            print(f"❌ 恢复按钮状态时出错: {str(e)}")

    def get_current_time(self):
        """获取当前时间字符串"""
        try:
            from datetime import datetime
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        except Exception as e:
            print(f"❌ 获取当前时间时出错: {str(e)}")
            return "未知时间"

    def init_browser(self):
        """初始化浏览器设置"""
        # 达人后台浏览器使用完全独立的配置文件和Cookie会话
        # 避免与邀约API的会话冲突
        import time
        profile_name = f"daren_backend_{self.shop_name}_{int(time.time())}_{id(self)}"
        self.profile = QWebEngineProfile(profile_name)
        print(f"🔒 达人后台：创建独立配置文件 {profile_name}")

        # 设置独立的存储路径，确保完全隔离
        import tempfile
        import os
        temp_dir = tempfile.mkdtemp(prefix=f"kuaishou_daren_{int(time.time())}_")
        self.profile.setPersistentStoragePath(temp_dir)
        self.profile.setCachePath(os.path.join(temp_dir, "cache"))
        print(f"🔒 达人后台：设置独立存储路径 {temp_dir}")

        # 设置用户代理
        self.profile.setHttpUserAgent(
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
            "(KHTML, like Gecko) Chrome/********* Safari/537.36"
        )

        # 配置独立的网络设置
        self.profile.setHttpCacheType(QWebEngineProfile.MemoryHttpCache)
        self.profile.setHttpCacheMaximumSize(50 * 1024 * 1024)  # 50MB缓存

        print(f"✅ 达人后台：独立浏览器配置初始化完成")

        # 启用JavaScript相关设置
        from PyQt5.QtWebEngineWidgets import QWebEngineSettings
        settings = self.profile.settings()
        settings.setAttribute(QWebEngineSettings.JavascriptEnabled, True)
        settings.setAttribute(QWebEngineSettings.JavascriptCanOpenWindows, True)
        settings.setAttribute(QWebEngineSettings.JavascriptCanAccessClipboard, True)
        settings.setAttribute(QWebEngineSettings.LocalStorageEnabled, True)
        settings.setAttribute(QWebEngineSettings.AllowRunningInsecureContent, True)

        print("✅ JavaScript和新窗口功能已启用")

        # 创建自定义页面并设置配置文件
        try:
            self.page = CustomWebEnginePage(self.profile, self.web_view)
            self.page.set_main_browser(self)  # 设置主浏览器引用
            self.web_view.setPage(self.page)
            print("✅ WebEngine页面创建成功")
        except Exception as e:
            print(f"❌ 创建WebEngine页面失败: {str(e)}")
            # 使用默认配置文件，不需要删除，只需清除引用
            self.profile = None
            raise Exception(f"WebEngine页面创建失败: {str(e)}")

        # 连接页面加载信号
        self.web_view.loadStarted.connect(self.on_load_started)
        self.web_view.loadProgress.connect(self.on_load_progress)
        self.web_view.loadFinished.connect(self.on_load_finished)
        self.web_view.urlChanged.connect(self.on_url_changed)

        # 连接页面加载完成信号，用于重新设置cookie
        self.web_view.loadFinished.connect(self.on_page_load_finished)

        # 注意：newWindowRequested信号在PyQt5中不存在，只在Qt6.2+中可用
        # 我们依赖createWindow方法来处理新窗口请求

    def load_page(self):
        """加载页面"""
        print(f"开始加载快手订单页面: {self.target_url}")

        # 直接设置cookie并访问目标页面，无需跳转
        print("正在预设cookie并直接访问订单详情页面...")

        # 直接设置cookie并跳转到目标页面
        print("直接设置cookie并跳转到订单详情页面...")
        self.set_cookies_and_navigate_direct()

    def set_cookies_and_navigate_direct(self):
        """直接设置cookie并导航到目标页面，无需多次跳转"""
        print("开始设置cookies并直接跳转...")

        try:
            # 解析cookie字符串
            cookies = self.parse_cookie_string(self.cookie_str)

            if not cookies:
                print("Cookie解析失败")
                QMessageBox.warning(self, "错误", "Cookie解析失败，无法访问订单详情")
                return

            # 先设置cookies到浏览器配置
            self.set_cookies_to_browser(cookies)

            # 直接导航到目标页面，无需等待
            print(f"Cookie设置完成，直接跳转到订单详情页面: {self.target_url}")
            self.navigate_to_target()

        except Exception as e:
            print(f"设置cookie时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"设置cookie失败: {str(e)}")

    def parse_cookie_string(self, cookie_str):
        """解析cookie字符串"""
        cookies = []

        try:
            if not cookie_str:
                return cookies

            # 分割cookie字符串
            cookie_pairs = cookie_str.split(';')

            for pair in cookie_pairs:
                pair = pair.strip()
                if '=' in pair:
                    name, value = pair.split('=', 1)
                    cookies.append({
                        'name': name.strip(),
                        'value': value.strip(),
                        'domain': '.kwaixiaodian.com',
                        'path': '/'
                    })

            print(f"成功解析 {len(cookies)} 个cookie")
            return cookies

        except Exception as e:
            print(f"解析cookie时出错: {str(e)}")
            return []

    def set_cookies_to_browser(self, cookies):
        """设置cookies到浏览器 - 使用独立Cookie会话"""
        try:
            print(f"🔒 达人后台：开始设置独立Cookie会话")

            # 使用独立配置文件的cookie存储
            profile = self.web_view.page().profile()
            cookie_store = profile.cookieStore()

            # 清除任何现有的cookies，确保完全独立
            cookie_store.deleteAllCookies()
            print(f"🔒 达人后台：已清除现有cookies，确保会话独立")

            # 批量设置cookies到独立会话
            for cookie in cookies:
                # 创建QNetworkCookie对象
                from PyQt5.QtNetwork import QNetworkCookie

                qcookie = QNetworkCookie()
                qcookie.setName(cookie['name'].encode())
                qcookie.setValue(cookie['value'].encode())

                # 设置域名，确保适用于快手相关域名
                domain = cookie.get('domain', '.kwaixiaodian.com')
                if not domain.startswith('.'):
                    domain = '.' + domain
                qcookie.setDomain(domain)

                # 设置路径
                path = cookie.get('path', '/')
                qcookie.setPath(path)

                # 设置安全属性
                if cookie.get('secure'):
                    qcookie.setSecure(True)
                if cookie.get('httpOnly'):
                    qcookie.setHttpOnly(True)

                # 添加到独立cookie存储
                cookie_store.setCookie(qcookie, QUrl("https://cps.kwaixiaodian.com"))

            print(f"🔒 达人后台：已通过独立会话设置 {len(cookies)} 个cookie")

        except Exception as e:
            print(f"QNetworkCookie设置失败，回退到JavaScript方式: {str(e)}")
            # 回退到JavaScript方式
            js_code = ""
            for cookie in cookies:
                js_code += f"""
                document.cookie = "{cookie['name']}={cookie['value']}; domain={cookie['domain']}; path={cookie['path']}";
                """

            # 安全执行JavaScript
            self.safe_run_javascript(js_code, f"设置 {len(cookies)} 个cookie")

    def navigate_to_target(self):
        """导航到目标页面"""
        print(f"导航到目标页面: {self.target_url}")
        self.web_view.load(QUrl(self.target_url))

    def refresh_page(self):
        """刷新页面 - 确保使用当前店铺的cookie"""
        print(f"刷新页面，重新设置店铺 '{self.shop_name}' 的cookie")

        # 重新设置cookie并刷新
        try:
            # 解析cookie字符串
            cookies = self.parse_cookie_string(self.cookie_str)

            if cookies:
                print(f"重新设置 {len(cookies)} 个cookie用于刷新")
                # 设置cookie到浏览器
                self.set_cookies_to_browser(cookies)

                # 延迟刷新，确保cookie设置完成
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(500, self._do_refresh)
            else:
                print("Cookie解析失败，直接刷新")
                self._do_refresh()

        except Exception as e:
            print(f"刷新时设置cookie出错: {str(e)}")
            self._do_refresh()

    def _do_refresh(self):
        """执行实际的刷新操作"""
        self.web_view.reload()

    def go_back(self):
        """后退"""
        if self.web_view.history().canGoBack():
            self.web_view.back()

    def go_forward(self):
        """前进"""
        if self.web_view.history().canGoForward():
            self.web_view.forward()

    def navigate_to_url(self):
        """导航到地址栏中的URL"""
        url = self.address_bar.text().strip()
        if url:
            # 如果URL不包含协议，自动添加https://
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url

            print(f"导航到: {url}")

            # 如果是快手小店相关URL，先设置cookie再导航
            if 'kwaixiaodian.com' in url:
                print(f"检测到快手小店URL，使用店铺 '{self.shop_name}' 的cookie")
                self.set_cookies_and_navigate_to_url(url)
            else:
                self.web_view.load(QUrl(url))

    def set_cookies_and_navigate_to_url(self, url):
        """设置cookie并导航到指定URL"""
        try:
            # 解析cookie字符串
            cookies = self.parse_cookie_string(self.cookie_str)

            if cookies:
                print(f"为URL导航设置 {len(cookies)} 个cookie")
                # 设置cookie到浏览器
                self.set_cookies_to_browser(cookies)

                # 延迟导航，确保cookie设置完成
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(500, lambda: self.web_view.load(QUrl(url)))
            else:
                print("Cookie解析失败，直接导航")
                self.web_view.load(QUrl(url))

        except Exception as e:
            print(f"设置cookie并导航时出错: {str(e)}")
            self.web_view.load(QUrl(url))

    def go_to_home(self):
        """跳转到快手小店主页（使用已设置的cookie）"""
        home_url = "https://s.kwaixiaodian.com/zone/home"
        print(f"跳转到快手小店主页: {home_url}")

        # 确保使用已设置的cookie跳转
        if hasattr(self, 'cookie_str') and self.cookie_str:
            print("使用已设置的cookie跳转到主页...")
            # 重新设置cookie并跳转
            self.set_cookies_and_navigate_to_home(home_url)
        else:
            print("未找到cookie数据，直接跳转...")
            self.web_view.load(QUrl(home_url))

    def copy_url(self):
        """复制地址栏网址到剪贴板"""
        url = self.address_bar.text().strip()
        if url:
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(url)
            print(f"已复制网址到剪贴板: {url}")
        else:
            print("地址栏为空，无法复制")

    def set_cookies_and_navigate_to_home(self, url):
        """设置cookie并导航到主页"""
        try:
            if hasattr(self, 'cookie_str') and self.cookie_str:
                # 解析cookie字符串
                cookies = self.parse_cookie_string(self.cookie_str)

                if cookies:
                    print(f"重新设置 {len(cookies)} 个cookie用于主页跳转")

                    # 设置cookie到浏览器
                    self.set_cookies_to_browser(cookies)

                    print(f"Cookie设置完成，跳转到主页: {url}")
                else:
                    print("Cookie解析失败，直接跳转")

            # 导航到目标URL
            self.web_view.load(QUrl(url))

        except Exception as e:
            print(f"设置cookie并导航到主页时出错: {str(e)}")
            # 如果出错，直接导航
            self.web_view.load(QUrl(url))

    def on_url_changed(self, url):
        """URL变化时更新地址栏和窗口标题"""
        url_string = url.toString()
        self.address_bar.setText(url_string)

        # 更新当前URL并重新计算窗口标题
        self.target_url = url_string
        new_title = self.get_window_title_by_url()

        # 更新窗口标题
        self.setWindowTitle(new_title)

        # 更新自定义标题栏中的标题
        if hasattr(self, 'title_label'):
            self.title_label.setText(new_title)
            # 重新设置鼠标事件，确保店铺详情功能正常
            self.title_label.setMouseTracking(True)
            self.title_label.mouseMoveEvent = lambda event: self.title_label_mouse_move_event(event, self.title_label)
            self.title_label.leaveEvent = lambda event: self.title_label_leave_event(event)

        print(f"URL变化: {url_string}")
        print(f"窗口标题更新为: {new_title}")

    def on_load_started(self):
        """页面开始加载"""
        try:
            print("页面开始加载...")
            # 清除之前可能存在的JavaScript错误状态
            self._js_error_count = 0
        except Exception as e:
            print(f"页面加载开始处理出错: {str(e)}")

    def on_load_progress(self, progress):
        """页面加载进度"""
        try:
            # 不显示进度条，只在控制台输出进度
            if progress % 20 == 0:  # 每20%输出一次
                print(f"页面加载进度: {progress}%")
        except Exception as e:
            print(f"页面加载进度处理出错: {str(e)}")

    def on_load_finished(self, success):
        """页面加载完成 - 增强的异常处理"""
        try:
            if success:
                print("页面加载成功")
                # 延迟注入JavaScript处理器，确保页面完全加载
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(1000, self.inject_error_handler)
            else:
                print("页面加载失败")
                # 不立即显示警告对话框，避免阻塞UI
                print("⚠️ 页面加载失败，请检查网络连接")
        except Exception as e:
            print(f"页面加载完成处理出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def inject_error_handler(self):
        """注入JavaScript错误处理器，捕获并处理页面JavaScript错误"""
        try:
            error_handler_js = """
            // 捕获JavaScript错误
            window.addEventListener('error', function(e) {
                console.log('JavaScript错误已捕获:', e.message, e.filename, e.lineno);
                // 对于EsImage重复安装警告，不做处理
                if (e.message && e.message.includes('EsImage')) {
                    console.log('EsImage重复安装警告已忽略');
                    return true; // 阻止默认错误处理
                }
                // 对于dataValueFormatter错误，尝试修复
                if (e.message && e.message.includes('dataValueFormatter')) {
                    console.log('dataValueFormatter错误已捕获，尝试修复');
                    return true; // 阻止默认错误处理
                }
                return false; // 允许其他错误正常处理
            });

            // 捕获未处理的Promise拒绝
            window.addEventListener('unhandledrejection', function(e) {
                console.log('未处理的Promise拒绝已捕获:', e.reason);
                e.preventDefault(); // 阻止默认处理
            });

            // 重写window.open方法，让它在当前窗口中打开链接
            var originalOpen = window.open;
            window.open = function(url, name, features) {
                console.log('拦截window.open调用:', url, name, features);

                // 处理相对URL和绝对URL
                if (url) {
                    var targetUrl = url;

                    // 如果是相对URL，转换为绝对URL
                    if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('//')) {
                        var base = window.location.protocol + '//' + window.location.host;
                        if (url.startsWith('/')) {
                            targetUrl = base + url;
                        } else {
                            var path = window.location.pathname;
                            if (path.endsWith('/')) {
                                targetUrl = base + path + url;
                            } else {
                                targetUrl = base + path.substring(0, path.lastIndexOf('/') + 1) + url;
                            }
                        }
                    }

                    console.log('重定向到:', targetUrl);

                    // 在当前窗口中打开链接
                    window.location.href = targetUrl;
                } else {
                    console.log('window.open调用没有URL参数');
                }

                // 返回null，表示没有打开新窗口
                return null;
            };

            // 重写document.createElement，拦截动态创建的链接
            var originalCreateElement = document.createElement;
            document.createElement = function(tagName) {
                var element = originalCreateElement.call(document, tagName);

                if (tagName.toLowerCase() === 'a') {
                    // 监听动态创建的链接点击
                    element.addEventListener('click', function(e) {
                        if (this.target === '_blank' || this.target === '_new') {
                            e.preventDefault();
                            console.log('拦截动态链接点击:', this.href);
                            if (this.href) {
                                window.location.href = this.href;
                            }
                        }
                    });
                }

                return element;
            };

            // 处理页面中已存在的target="_blank"链接
            function processExistingLinks() {
                var links = document.querySelectorAll('a[target="_blank"], a[target="_new"]');
                console.log('找到', links.length, '个需要处理的链接');

                links.forEach(function(link) {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('拦截现有链接点击:', this.href);
                        if (this.href) {
                            window.location.href = this.href;
                        }
                    });
                });
            }

            // 页面加载完成后处理现有链接
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', processExistingLinks);
            } else {
                processExistingLinks();
            }

            // 监听DOM变化，处理动态添加的链接
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            // 检查新添加的节点是否是链接
                            if (node.tagName === 'A' && (node.target === '_blank' || node.target === '_new')) {
                                node.addEventListener('click', function(e) {
                                    e.preventDefault();
                                    console.log('拦截动态添加的链接点击:', this.href);
                                    if (this.href) {
                                        window.location.href = this.href;
                                    }
                                });
                            }

                            // 检查新添加节点的子元素
                            var childLinks = node.querySelectorAll && node.querySelectorAll('a[target="_blank"], a[target="_new"]');
                            if (childLinks) {
                                childLinks.forEach(function(link) {
                                    link.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        console.log('拦截子元素链接点击:', this.href);
                                        if (this.href) {
                                            window.location.href = this.href;
                                        }
                                    });
                                });
                            }
                        }
                    });
                });
            });

            observer.observe(document.body, { childList: true, subtree: true });

            console.log('JavaScript错误处理器、window.open重写和链接拦截已注入');
            """

            self.safe_run_javascript(error_handler_js, "注入JavaScript错误处理器和window.open重写")

        except Exception as e:
            print(f"注入JavaScript处理器失败: {str(e)}")



    def on_page_load_finished(self, success):
        """页面加载完成后重新设置cookie（确保cookie持久化）"""
        if success:
            current_url = self.web_view.url().toString()
            print(f"页面加载完成: {current_url}")

            # 如果是快手小店相关页面，重新设置cookie确保登录状态
            if 'kwaixiaodian.com' in current_url:
                print(f"检测到快手小店页面，重新设置店铺 '{self.shop_name}' 的cookie")

                try:
                    # 解析cookie字符串
                    cookies = self.parse_cookie_string(self.cookie_str)

                    if cookies:
                        print(f"重新设置 {len(cookies)} 个cookie确保登录状态")
                        # 使用JavaScript方式设置cookie，更可靠
                        self.set_cookies_via_javascript(cookies)

                        # 纯手动模式，不自动保存cookie
                        print("✅ Cookie设置完成，如需保存请手动点击保存Cookie按钮")
                    else:
                        print("Cookie解析失败，无法重新设置")

                except Exception as e:
                    print(f"重新设置cookie时出错: {str(e)}")







    def safe_run_javascript(self, js_code, description="执行JavaScript"):
        """
        安全执行JavaScript代码，带有完善的异常处理

        参数:
            js_code (str): 要执行的JavaScript代码
            description (str): 操作描述，用于日志输出
        """
        try:
            if not self.page:
                print(f"❌ {description} 失败: 页面对象不存在")
                return False

            if not js_code or not js_code.strip():
                print(f"❌ {description} 失败: JavaScript代码为空")
                return False

            print(f"🔧 开始{description}...")

            # 使用回调函数来处理JavaScript执行结果
            def js_callback(result):
                try:
                    if result is not None:
                        print(f"✅ {description} 成功")
                    else:
                        print(f"⚠️ {description} 完成，但无返回值")
                except Exception as e:
                    print(f"❌ {description} 回调处理出错: {str(e)}")

            # 执行JavaScript，带有回调处理
            self.page.runJavaScript(js_code, js_callback)
            return True

        except Exception as e:
            print(f"❌ {description} 执行出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def set_cookies_via_javascript(self, cookies):
        """通过JavaScript设置cookie"""
        try:
            js_code = ""
            for cookie in cookies:
                js_code += f"""
                document.cookie = "{cookie['name']}={cookie['value']}; domain={cookie['domain']}; path={cookie['path']}; expires=Fri, 31 Dec 2025 23:59:59 GMT";
                """

            # 安全执行JavaScript
            self.safe_run_javascript(js_code, f"重新设置 {len(cookies)} 个cookie")

        except Exception as e:
            print(f"JavaScript设置cookie失败: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭事件 - 增强的资源清理和异常处理"""
        try:
            print(f"🔄 开始关闭店铺 '{self.shop_name}' 的浏览器窗口...")

            # 1. 先停止页面加载，防止在关闭过程中继续加载
            try:
                if hasattr(self, 'web_view') and self.web_view:
                    print("🛑 停止页面加载...")
                    self.web_view.stop()
                    # 断开所有信号连接，防止在清理过程中触发回调
                    try:
                        self.web_view.loadStarted.disconnect()
                        self.web_view.loadProgress.disconnect()
                        self.web_view.loadFinished.disconnect()
                        self.web_view.urlChanged.disconnect()
                    except:
                        pass  # 忽略断开连接时的错误
            except Exception as e:
                print(f"⚠️ 停止页面加载时出错: {str(e)}")

            # 2. 清理页面对象，防止JavaScript继续执行
            try:
                if hasattr(self, 'page') and self.page:
                    print("🧹 清理页面对象...")
                    # 先从WebView中移除页面引用
                    if hasattr(self, 'web_view') and self.web_view:
                        self.web_view.setPage(None)
                    # 然后清理页面对象
                    page_to_delete = self.page
                    self.page = None
                    # 立即删除页面对象，确保在配置文件清理前完成
                    page_to_delete.deleteLater()
                    # 强制处理删除事件
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                    print("✅ 页面对象已清理")
            except Exception as e:
                print(f"⚠️ 清理页面对象时出错: {str(e)}")

            # 3. 清理WebView对象
            try:
                if hasattr(self, 'web_view') and self.web_view:
                    print("🧹 清理WebView对象...")
                    # 确保页面已经从WebView中移除
                    self.web_view.setPage(None)
                    webview_to_delete = self.web_view
                    self.web_view = None
                    webview_to_delete.deleteLater()
                    # 强制处理删除事件
                    from PyQt5.QtWidgets import QApplication
                    QApplication.processEvents()
                    print("✅ WebView对象已清理")
            except Exception as e:
                print(f"⚠️ 清理WebView对象时出错: {str(e)}")

            # 4. 清理独立配置文件对象和临时存储
            try:
                if hasattr(self, 'profile') and self.profile:
                    print("🧹 清理达人后台独立配置文件...")

                    # 获取临时存储路径
                    temp_storage_path = None
                    try:
                        temp_storage_path = self.profile.persistentStoragePath()
                    except:
                        pass

                    profile_to_delete = self.profile
                    self.profile = None

                    # 延迟删除配置文件，避免WebEngine资源冲突
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(1000, profile_to_delete.deleteLater)

                    # 延迟清理临时存储目录
                    if temp_storage_path and temp_storage_path.startswith('/tmp') or temp_storage_path.startswith('C:\\Users'):
                        def cleanup_temp_storage():
                            try:
                                import shutil
                                import os
                                if os.path.exists(temp_storage_path):
                                    shutil.rmtree(temp_storage_path, ignore_errors=True)
                                    print(f"🧹 已清理临时存储目录: {temp_storage_path}")
                            except Exception as e:
                                print(f"⚠️ 清理临时存储目录失败: {e}")

                        QTimer.singleShot(3000, cleanup_temp_storage)

                    print("🕐 达人后台独立配置文件已安排延迟删除")
            except Exception as e:
                print(f"⚠️ 清理独立配置文件时出错: {str(e)}")

            # 5. 从全局管理器中移除
            try:
                browser_windows = get_browser_windows()
                if self.shop_name in browser_windows:
                    del browser_windows[self.shop_name]
                    print(f"✅ 已从窗口管理器中移除店铺 '{self.shop_name}' 的浏览器窗口")
            except Exception as e:
                print(f"⚠️ 从窗口管理器移除时出错: {str(e)}")

        except Exception as e:
            print(f"❌ 清理窗口资源时出错: {str(e)}")
            import traceback
            traceback.print_exc()

        try:
            # 调用父类的关闭事件
            super().closeEvent(event)
            print(f"✅ 店铺 '{self.shop_name}' 的浏览器窗口已完全关闭")
        except Exception as e:
            print(f"❌ 调用父类关闭事件时出错: {str(e)}")
            # 即使父类关闭失败，也要接受关闭事件
            event.accept()


def find_existing_browser_window(shop_name):
    """
    查找已存在的店铺浏览器窗口

    参数:
        shop_name (str): 店铺名称

    返回:
        KuaishouOrderBrowser: 如果找到返回窗口实例，否则返回None
    """
    try:
        browser_windows = get_browser_windows()

        print(f"🔍 开始查找店铺 '{shop_name}' 的已存在窗口...")
        print(f"📊 当前窗口管理器状态: {len(browser_windows)} 个窗口")
        print(f"📊 窗口管理器ID: {id(browser_windows)}")

        # 清理已关闭的窗口
        closed_windows = []
        for name, window in browser_windows.items():
            try:
                # 检查窗口是否仍然有效
                if not window:
                    closed_windows.append(name)
                    print(f"🗑️ 标记清理窗口 '{name}': 窗口对象为None")
                elif not hasattr(window, 'isVisible'):
                    closed_windows.append(name)
                    print(f"🗑️ 标记清理窗口 '{name}': 窗口对象无效")
                else:
                    # 安全地检查窗口状态
                    try:
                        is_visible = window.isVisible()
                        is_hidden = window.isHidden() if hasattr(window, 'isHidden') else False

                        # 如果窗口已隐藏且不可见，可能已关闭
                        if not is_visible and is_hidden:
                            closed_windows.append(name)
                            print(f"🗑️ 标记清理窗口 '{name}': 窗口已隐藏且不可见")
                        else:
                            print(f"✅ 窗口 '{name}' 状态正常 (可见: {is_visible}, 隐藏: {is_hidden})")
                    except RuntimeError as re:
                        # 窗口对象已被删除
                        closed_windows.append(name)
                        print(f"🗑️ 标记清理窗口 '{name}': 窗口对象已被删除 ({str(re)})")
                    except Exception as ve:
                        # 其他检查错误
                        print(f"⚠️ 检查窗口 '{name}' 状态时出错: {str(ve)}")
                        # 不标记为关闭，保持在管理器中

            except Exception as e:
                closed_windows.append(name)
                print(f"🗑️ 标记清理窗口 '{name}': 检查时出错 {str(e)}")

        # 移除已关闭的窗口
        for name in closed_windows:
            if name in browser_windows:
                del browser_windows[name]
                print(f"🧹 已清理窗口: {name}")

        print(f"📊 清理后窗口管理器状态: {len(browser_windows)} 个窗口")

        # 处理店铺名称，去掉括号和分数
        clean_shop_name = shop_name
        if '(' in shop_name and shop_name.endswith(')'):
            clean_shop_name = shop_name.split('(')[0].strip()
            print(f"🧹 清理店铺名称: '{shop_name}' -> '{clean_shop_name}' (英文括号)")
        elif '（' in shop_name and shop_name.endswith('）'):
            clean_shop_name = shop_name.split('（')[0].strip()
            print(f"🧹 清理店铺名称: '{shop_name}' -> '{clean_shop_name}' (中文括号)")
        else:
            print(f"📝 店铺名称无需清理: '{shop_name}'")

        # 查找匹配的窗口（支持多种匹配方式）
        print(f"🔍 开始遍历 {len(browser_windows)} 个窗口进行匹配...")
        for name, window in browser_windows.items():
            try:
                print(f"🔍 检查窗口: '{name}'")

                # 方法1: 直接匹配店铺名称（用于窗口管理器中的键）
                clean_window_name = name
                if '(' in name and name.endswith(')'):
                    clean_window_name = name.split('(')[0].strip()
                    print(f"  🧹 清理窗口名称: '{name}' -> '{clean_window_name}' (英文括号)")
                elif '（' in name and name.endswith('）'):
                    clean_window_name = name.split('（')[0].strip()
                    print(f"  🧹 清理窗口名称: '{name}' -> '{clean_window_name}' (中文括号)")
                else:
                    print(f"  📝 窗口名称无需清理: '{name}'")

                # 比较清理后的名称
                print(f"  🔍 方法1比较: '{clean_window_name}' vs '{clean_shop_name}'")
                if clean_window_name == clean_shop_name:
                    print(f"✅ 找到已存在的店铺浏览器窗口: '{name}' (直接匹配: '{clean_shop_name}')")
                    return window

                # 方法2: 通过窗口标题匹配（格式：店铺名称－快手订单详情）
                if hasattr(window, 'windowTitle'):
                    window_title = window.windowTitle()
                    print(f"  📋 窗口标题: '{window_title}'")

                    if '－' in window_title or '-' in window_title:
                        # 提取 "－" 或 "-" 之前的店铺名称
                        if '－' in window_title:
                            title_shop_name = window_title.split('－')[0].strip()
                            print(f"  ✂️ 从标题提取店铺名称 (中文分隔符): '{title_shop_name}'")
                        else:
                            title_shop_name = window_title.split('-')[0].strip()
                            print(f"  ✂️ 从标题提取店铺名称 (英文分隔符): '{title_shop_name}'")

                        # 清理标题中的店铺名称
                        clean_title_shop_name = title_shop_name
                        if '(' in title_shop_name and title_shop_name.endswith(')'):
                            clean_title_shop_name = title_shop_name.split('(')[0].strip()
                            print(f"  🧹 清理标题店铺名称: '{title_shop_name}' -> '{clean_title_shop_name}' (英文括号)")
                        elif '（' in title_shop_name and title_shop_name.endswith('）'):
                            clean_title_shop_name = title_shop_name.split('（')[0].strip()
                            print(f"  🧹 清理标题店铺名称: '{title_shop_name}' -> '{clean_title_shop_name}' (中文括号)")
                        else:
                            print(f"  📝 标题店铺名称无需清理: '{title_shop_name}'")

                        # 比较清理后的名称
                        print(f"  🔍 方法2比较: '{clean_title_shop_name}' vs '{clean_shop_name}'")
                        if clean_title_shop_name == clean_shop_name:
                            print(f"✅ 找到已存在的店铺浏览器窗口: '{window_title}' (标题匹配: '{clean_shop_name}')")
                            return window
                    else:
                        print(f"  ⚠️ 窗口标题不包含分隔符，跳过标题匹配")
                else:
                    print(f"  ⚠️ 窗口没有windowTitle方法，跳过标题匹配")

                print(f"  ❌ 窗口 '{name}' 不匹配")

            except Exception as e:
                print(f"❌ 检查窗口 '{name}' 时出错: {str(e)}")
                continue

        print(f"未找到店铺 '{shop_name}' 的已存在浏览器窗口")
        return None

    except Exception as e:
        print(f"查找已存在浏览器窗口时出错: {str(e)}")
        return None


def activate_browser_window(window):
    """
    激活已存在的浏览器窗口

    参数:
        window (KuaishouOrderBrowser): 要激活的窗口实例

    返回:
        bool: 激活成功返回True，失败返回False
    """
    try:
        if not window:
            return False

        # 检查窗口是否仍然有效
        if not hasattr(window, 'show') or not hasattr(window, 'raise_') or not hasattr(window, 'activateWindow'):
            print("窗口对象无效，无法激活")
            return False

        print(f"正在激活店铺 '{window.shop_name}' 的浏览器窗口...")

        # 如果窗口被最小化，先恢复
        if hasattr(window, 'isMinimized') and window.isMinimized():
            print("窗口已最小化，正在恢复...")
            window.showNormal()

        # 显示窗口
        window.show()

        # 将窗口提到前台
        window.raise_()

        # 激活窗口
        window.activateWindow()

        # 设置焦点
        if hasattr(window, 'setFocus'):
            window.setFocus()

        print(f"成功激活店铺 '{window.shop_name}' 的浏览器窗口")
        return True

    except Exception as e:
        print(f"激活浏览器窗口时出错: {str(e)}")
        return False




def create_kuaishou_order_browser(shop_name, cookie_str, url, parent=None):
    """
    创建快手订单浏览器窗口（支持重复检测和窗口激活）

    参数:
        shop_name (str): 店铺名称
        cookie_str (str): cookie字符串
        url (str): 订单详情URL
        parent: 父窗口

    返回:
        KuaishouOrderBrowser: 浏览器窗口实例
    """
    if not WEBENGINE_AVAILABLE:
        return None

    # 添加详细的调试信息
    browser_windows = get_browser_windows()
    print(f"=== 开始检测店铺 '{shop_name}' 的浏览器窗口 ===")
    print(f"目标URL: {url}")
    print(f"全局窗口管理器ID: {id(browser_windows)}")
    print(f"当前窗口管理器中的窗口数量: {len(browser_windows)}")
    for name in browser_windows.keys():
        print(f"  - 已注册窗口: '{name}'")

    # 检查是否已存在该店铺的浏览器窗口
    existing_window = find_existing_browser_window(shop_name)
    if existing_window:
        print(f"✅ 检测到店铺 '{shop_name}' 已有打开的浏览器窗口，正在激活...")

        # 尝试激活已存在的窗口
        if activate_browser_window(existing_window):
            print(f"✅ 成功激活店铺 '{shop_name}' 的已存在浏览器窗口")

            # 如果URL不同，导航到新URL
            if hasattr(existing_window, 'target_url') and existing_window.target_url != url:
                print(f"🔄 检测到URL变化，正在导航到新页面...")
                print(f"原URL: {existing_window.target_url}")
                print(f"新URL: {url}")
                existing_window.target_url = url
                if hasattr(existing_window, 'web_view'):
                    existing_window.web_view.load(QUrl(url))
                if hasattr(existing_window, 'address_bar'):
                    existing_window.address_bar.setText(url)
            else:
                print(f"📍 URL相同，无需导航")

            print(f"🎉 返回已存在的窗口实例")
            return existing_window
        else:
            print(f"❌ 激活已存在窗口失败，将创建新窗口")
    else:
        print(f"❌ 未找到店铺 '{shop_name}' 的已存在浏览器窗口")

    # 创建新的浏览器窗口
    print(f"🆕 为店铺 '{shop_name}' 创建新的浏览器窗口...")
    browser = KuaishouOrderBrowser(shop_name, cookie_str, url, parent)
    print(f"✅ 新窗口创建完成，已注册到窗口管理器")
    return browser


# 测试函数
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 测试数据
    test_shop_name = "测试店铺"
    test_cookie = "test=value; another=value2"
    test_url = "https://s.kwaixiaodian.com/zone/order/detail?entranceScene=1&id=123456"

    # 创建浏览器窗口
    browser = create_kuaishou_order_browser(test_shop_name, test_cookie, test_url)

    if browser:
        browser.show()
        sys.exit(app.exec_())
    else:
        print("无法创建浏览器窗口")
