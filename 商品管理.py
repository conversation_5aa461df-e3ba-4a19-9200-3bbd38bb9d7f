#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import json
import re
import threading
import time
import webbrowser
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView, QLabel, QComboBox,
                            QPushButton, QFrame, QCheckBox, QLineEdit, QToolButton,
                            QRadioButton, QTreeWidget, QTreeWidgetItem, QMessageBox, QProgressBar, QMenu, QScrollBar, QToolTip, QScrollArea)
from PyQt5.QtGui import QIcon, QColor, QFont, QPalette, QPainter, QPainterPath, QPen, QCursor, QStandardItem, QPixmap, QBrush
from PyQt5.QtCore import Qt, QDate, QTime, QDateTime, QSize, QRect, QEvent, QThread, pyqtSignal, QPoint, QTimer, QItemSelectionModel, QMutex, QWaitCondition
import math
from tool.分类树 import CategoryTree
from tool.快手cokie_api import KuaishouCookieAPI
from tool.快手api import KuaishouAPI
from tool.date import ModernCalendar
import math

class CategoryPieChart(QWidget):
    """类目饼状图Widget"""

    def __init__(self, category_data, parent=None):
        super().__init__(parent)
        self.category_data = category_data  # [(类目名, 数量, 颜色), ...]
        self.setFixedSize(450, 300)  # 增加宽度：450x300
        self.setWindowFlags(Qt.ToolTip | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setMouseTracking(True)  # 启用鼠标跟踪
        self.header_view = None  # 表头引用

        # 预定义颜色
        self.colors = [
            QColor(255, 99, 132),   # 红色
            QColor(255, 159, 64),   # 橙色
            QColor(255, 205, 86),   # 黄色
            QColor(75, 192, 192),   # 青色
            QColor(54, 162, 235),   # 蓝色
            QColor(153, 102, 255),  # 紫色
            QColor(255, 99, 255),   # 粉色
            QColor(128, 128, 128),  # 灰色
            QColor(255, 140, 0),    # 深橙色
            QColor(50, 205, 50),    # 绿色
        ]

    def paintEvent(self, event):
        """绘制饼状图"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 设置背景
        painter.fillRect(self.rect(), QColor(255, 255, 255, 240))

        # 绘制边框
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        painter.drawRect(self.rect().adjusted(0, 0, -1, -1))

        if not self.category_data:
            # 没有数据时显示提示
            painter.setPen(QColor(100, 100, 100))
            painter.setFont(QFont("Microsoft YaHei", 12))
            painter.drawText(self.rect(), Qt.AlignCenter, "暂无类目数据")
            return

        # 计算总数
        total = sum(count for _, count in self.category_data)
        if total == 0:
            # 绘制一个空的圆形提示
            painter.setPen(QPen(QColor(200, 200, 200), 2))
            painter.setBrush(QBrush(QColor(240, 240, 240)))
            painter.drawEllipse(pie_rect)
            painter.setPen(QColor(100, 100, 100))
            painter.setFont(QFont("Microsoft YaHei", 10))
            painter.drawText(pie_rect, Qt.AlignCenter, "暂无数据")
            return

        # 饼图区域 - 增大饼图尺寸
        pie_rect = QRect(20, 35, 150, 150)

        # 绘制标题
        painter.setPen(QColor(50, 50, 50))
        painter.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        painter.drawText(QRect(0, 5, self.width(), 25), Qt.AlignCenter, "🎯 类目分布统计")

        # 绘制饼图 - 确保完整的圆形
        start_angle = 0

        # 显示的类目数据
        display_data = self.category_data[:10] if len(self.category_data) > 10 else self.category_data

        # 如果有超过10个类目，将其他类目合并为"其他"
        if len(self.category_data) > 10:
            other_count = sum(count for _, count in self.category_data[10:])
            display_data = self.category_data[:10] + [("其他", other_count)]

        # 计算所有角度，确保总和为360度
        angles = []
        total_calculated_angle = 0

        for i, (category, count) in enumerate(display_data[:-1]):  # 除了最后一个
            percentage = count / total
            angle = percentage * 360 * 16  # Qt使用1/16度为单位
            angles.append(int(angle))
            total_calculated_angle += int(angle)

        # 最后一个扇形使用剩余角度，确保完整的圆
        if display_data:
            remaining_angle = 360 * 16 - total_calculated_angle
            angles.append(remaining_angle)

        # 绘制所有扇形
        for i, (category, count) in enumerate(display_data):
            span_angle = angles[i]

            # 选择颜色
            if i < len(self.colors):
                color = self.colors[i]
            else:
                # 如果颜色不够，使用灰色作为"其他"
                color = QColor(200, 200, 200)

            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color, 1))  # 使用同色边框，避免白色分隔线

            # 绘制扇形
            if span_angle > 0:  # 只绘制有效角度的扇形
                painter.drawPie(pie_rect, start_angle, span_angle)

            start_angle += span_angle

        # 绘制图例 - 调整位置和布局
        legend_x = 190  # 向右移动
        legend_y = 40
        painter.setFont(QFont("Microsoft YaHei", 10))  # 稍大字体

        # 使用与饼图相同的显示数据
        for i, (category, count) in enumerate(display_data):
            percentage = (count / total) * 100

            # 绘制颜色方块 - 稍大一些
            if i < len(self.colors):
                color = self.colors[i]
            else:
                color = QColor(200, 200, 200)  # "其他"类目使用灰色

            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color))
            painter.drawRect(legend_x, legend_y + i * 20, 14, 14)  # 增大方块

            # 绘制文本 - 允许更长的文本
            painter.setPen(QColor(50, 50, 50))
            text = f"{category}: {count}个 ({percentage:.1f}%)"
            if len(text) > 28:  # 增加文本长度限制
                text = text[:25] + "..."
            painter.drawText(legend_x + 20, legend_y + i * 20 + 11, text)

        # 绘制总计信息
        painter.setPen(QColor(100, 100, 100))
        painter.setFont(QFont("Microsoft YaHei", 10))  # 稍大字体
        total_text = f"总计: {total}个商品，{len(self.category_data)}个类目"
        painter.drawText(QRect(0, self.height() - 30, self.width(), 25), Qt.AlignCenter, total_text)

    def set_header_view(self, header_view):
        """设置表头引用"""
        self.header_view = header_view

    def enterEvent(self, event):
        """鼠标进入饼状图"""
        super().enterEvent(event)
        if self.header_view:
            # 停止隐藏定时器
            self.header_view.hide_timer.stop()

    def leaveEvent(self, event):
        """鼠标离开饼状图"""
        super().leaveEvent(event)
        if self.header_view:
            # 开始隐藏倒计时
            self.header_view.hide_timer.start(500)





class CategoryAnalysisWindow(QWidget):
    """类目分析窗口"""

    def __init__(self, product_manager, parent=None):
        super().__init__(parent)
        self.product_manager = product_manager
        self.setWindowTitle("类目分析")
        self.setFixedSize(1020, 500)  # 修改窗口大小为1020*500
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        self.setWindowModality(Qt.ApplicationModal)  # 模态窗口

        # 设置窗口图标
        try:
            import os
            icon_path = os.path.join(os.path.dirname(__file__), "config", "imges", "logo.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"设置窗口图标失败: {str(e)}")

        # 设置窗口样式 - 自定义白色标题栏
        self.setStyleSheet("""
            CategoryAnalysisWindow {
                background-color: #f8f9fa;
                font-family: 'Microsoft YaHei';
            }
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Microsoft YaHei';
            }
            QLabel {
                color: #2c3e50;
            }
        """)

        self.init_ui()
        self.load_category_data()

        # 延迟设置白色标题栏，确保窗口完全创建
        QTimer.singleShot(50, self.set_white_title_bar)

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 主要内容区域
        content_layout = QHBoxLayout()

        # 左侧：饼状图
        self.pie_chart = CategoryPieChart([])
        self.pie_chart.setFixedSize(400, 400)  # 调整大小适应新窗口
        content_layout.addWidget(self.pie_chart)

        # 右侧：详细统计区域
        stats_widget = QWidget()
        stats_main_layout = QVBoxLayout(stats_widget)

        # 统计标题
        stats_title = QLabel("📈 详细统计")
        stats_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                padding: 5px 0;
                border-bottom: 1px solid #bdc3c7;
                margin-bottom: 10px;
            }
        """)
        stats_main_layout.addWidget(stats_title)

        # 多列统计容器
        stats_scroll = QScrollArea()
        stats_scroll.setWidgetResizable(True)
        stats_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        stats_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        stats_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #ecf0f1;
                border-radius: 5px;
                background-color: white;
            }
        """)

        # 统计内容Widget
        self.stats_content_widget = QWidget()
        self.stats_content_layout = QHBoxLayout(self.stats_content_widget)
        self.stats_content_layout.setSpacing(15)
        self.stats_content_layout.setContentsMargins(15, 15, 15, 15)

        # 初始化多列标签
        self.stats_columns = []
        for i in range(3):  # 创建3列
            column_widget = QWidget()
            column_layout = QVBoxLayout(column_widget)
            column_layout.setContentsMargins(0, 0, 0, 0)
            column_layout.setSpacing(8)

            column_label = QLabel("正在加载...")
            column_label.setStyleSheet("""
                QLabel {
                    font-size: 13px;
                    color: #7f8c8d;
                    background-color: transparent;
                    padding: 5px;
                }
            """)
            column_label.setWordWrap(True)
            column_label.setAlignment(Qt.AlignTop)

            column_layout.addWidget(column_label)
            column_layout.addStretch()

            self.stats_columns.append(column_label)
            self.stats_content_layout.addWidget(column_widget)

        stats_scroll.setWidget(self.stats_content_widget)
        stats_main_layout.addWidget(stats_scroll)

        content_layout.addWidget(stats_widget)

        layout.addLayout(content_layout)

        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        refresh_btn = QPushButton("🔄 刷新数据")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3E5FAC;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #354A94;
            }
            QPushButton:pressed {
                background-color: #2A3F7C;
            }
        """)
        refresh_btn.clicked.connect(self.load_category_data)
        button_layout.addWidget(refresh_btn)

        close_btn = QPushButton("❌ 关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                margin-left: 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def load_category_data(self):
        """加载类目数据"""
        try:
            category_count = {}
            total_count = 0

            table = self.product_manager.product_table
            row_count = table.rowCount()

            # 找到类目列
            category_column = -1
            for col in range(table.columnCount()):
                header_item = table.horizontalHeaderItem(col)
                if header_item and header_item.text() == "类目":
                    category_column = col
                    break

            if category_column == -1:
                for col in self.stats_columns:
                    col.setText("未找到类目列")
                return

            # 统计类目
            for row in range(row_count):
                category_item = table.item(row, category_column)
                if category_item:
                    category = category_item.text().strip()
                    if category:
                        category_count[category] = category_count.get(category, 0) + 1
                        total_count += 1

            if total_count == 0:
                for col in self.stats_columns:
                    col.setText("暂无类目数据")
                return

            # 按数量排序
            sorted_categories = sorted(category_count.items(), key=lambda x: x[1], reverse=True)

            # 更新饼状图
            self.pie_chart.category_data = sorted_categories
            self.pie_chart.update()

            # 更新统计信息
            self.update_stats_columns(sorted_categories, total_count)

        except Exception as e:
            print(f"加载类目数据失败: {str(e)}")
            for col in self.stats_columns:
                col.setText(f"加载失败: {str(e)}")

    def update_stats_columns(self, sorted_categories, total_count):
        """更新多列统计显示"""
        # 计算每列显示的类目数量
        items_per_column = len(sorted_categories) // 3
        if len(sorted_categories) % 3 > 0:
            items_per_column += 1

        # 为每列准备数据
        for col_idx in range(3):
            start_idx = col_idx * items_per_column
            end_idx = min(start_idx + items_per_column, len(sorted_categories))

            if start_idx >= len(sorted_categories):
                # 如果这列没有数据，显示空
                self.stats_columns[col_idx].setText("")
                continue

            column_lines = []

            # 第一列显示总计信息
            if col_idx == 0:
                column_lines.append(f"📊 总计统计")
                column_lines.append(f"商品总数: {total_count}个")
                column_lines.append(f"类目总数: {len(sorted_categories)}个")
                column_lines.append("")

            # 显示该列的类目
            for i in range(start_idx, end_idx):
                category, count = sorted_categories[i]
                percentage = (count / total_count) * 100

                # 排名显示
                if i < 3:
                    rank_emoji = ["🥇", "🥈", "🥉"][i]
                else:
                    rank_emoji = f"{i+1}."

                column_lines.append(f"{rank_emoji} {category}")
                column_lines.append(f"   {count}个 ({percentage:.1f}%)")
                column_lines.append("")

            self.stats_columns[col_idx].setText("\n".join(column_lines))

    def center_on_parent(self):
        """在父窗口中居中显示"""
        try:
            if self.parent():
                # 确保父窗口几何信息是最新的
                parent = self.parent()
                parent.update()  # 强制更新父窗口
                QApplication.processEvents()  # 处理待处理的事件

                # 获取父窗口的实际几何信息
                parent_geometry = parent.frameGeometry()  # 使用frameGeometry包含标题栏

                # 计算居中位置
                x = parent_geometry.x() + (parent_geometry.width() - self.width()) // 2
                y = parent_geometry.y() + (parent_geometry.height() - self.height()) // 2

                # 确保窗口不会超出屏幕边界
                screen_geometry = QApplication.desktop().availableGeometry()

                # 调整X坐标
                if x < screen_geometry.x():
                    x = screen_geometry.x()
                elif x + self.width() > screen_geometry.right():
                    x = screen_geometry.right() - self.width()

                # 调整Y坐标
                if y < screen_geometry.y():
                    y = screen_geometry.y()
                elif y + self.height() > screen_geometry.bottom():
                    y = screen_geometry.bottom() - self.height()

                print(f"🔧 [DEBUG] 窗口居中: 父窗口={parent_geometry}, 子窗口位置=({x}, {y})")
                self.move(x, y)
            else:
                # 如果没有父窗口，在屏幕中央显示
                screen_geometry = QApplication.desktop().availableGeometry()
                x = screen_geometry.x() + (screen_geometry.width() - self.width()) // 2
                y = screen_geometry.y() + (screen_geometry.height() - self.height()) // 2
                print(f"🔧 [DEBUG] 屏幕居中: 屏幕={screen_geometry}, 窗口位置=({x}, {y})")
                self.move(x, y)

        except Exception as e:
            print(f"窗口居中失败: {str(e)}")
            # 备用方案：简单的屏幕居中
            screen = QApplication.desktop().screenGeometry()
            self.move((screen.width() - self.width()) // 2, (screen.height() - self.height()) // 2)

    def set_white_title_bar(self):
        """设置白色标题栏 - 与主窗口保持一致"""
        import sys
        if sys.platform == 'win32':
            try:
                import ctypes
                DWMWA_CAPTION_COLOR = 35  # Windows 11 22000以上版本适用

                # 将颜色值从 #FFFFFF 转换为 COLORREF 值 (BGR格式)
                # 白色的 COLORREF 值为 0x00FFFFFF
                caption_color = ctypes.c_int(0x00FFFFFF)

                # 设置窗口标题栏颜色
                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    int(self.winId()),
                    DWMWA_CAPTION_COLOR,
                    ctypes.byref(caption_color),
                    ctypes.sizeof(caption_color)
                )
                print("类目分析窗口标题栏颜色已设置为白色")
            except Exception as e:
                print(f"设置类目分析窗口标题栏颜色失败: {str(e)}")


class CustomScrollBar(QScrollBar):
    """自定义滚动条，支持汉化右键菜单"""

    def __init__(self, orientation, parent=None):
        super().__init__(orientation, parent)
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_custom_context_menu)

    def show_custom_context_menu(self, position):
        """显示自定义汉化右键菜单"""
        try:
            context_menu = QMenu(self)

            # 设置菜单样式
            context_menu.setStyleSheet("""
                QMenu {
                    background-color: white;
                    border: 1px solid #d0d0d0;
                    border-radius: 6px;
                    padding: 4px;
                    font-family: 'Microsoft YaHei';
                    font-size: 12px;
                }
                QMenu::item {
                    padding: 6px 20px;
                    border-radius: 4px;
                    margin: 1px;
                }
                QMenu::item:selected {
                    background-color: #e3f2fd;
                    color: #1976d2;
                }
                QMenu::item:disabled {
                    color: #999999;
                }
                QMenu::separator {
                    height: 1px;
                    background-color: #e0e0e0;
                    margin: 4px 8px;
                }
            """)

            # 添加汉化菜单项
            if self.orientation() == Qt.Vertical:
                # 垂直滚动条菜单
                context_menu.addAction("📄 滚动到顶部", lambda: self.setValue(self.minimum()))
                context_menu.addAction("📄 滚动到底部", lambda: self.setValue(self.maximum()))
                context_menu.addSeparator()
                context_menu.addAction("⬆️ 向上翻页", lambda: self.setValue(max(self.minimum(), self.value() - self.pageStep())))
                context_menu.addAction("⬇️ 向下翻页", lambda: self.setValue(min(self.maximum(), self.value() + self.pageStep())))
                context_menu.addSeparator()
                context_menu.addAction("🎯 滚动到此处", lambda: self.scroll_to_position(position))
            else:
                # 水平滚动条菜单
                context_menu.addAction("📄 滚动到最左", lambda: self.setValue(self.minimum()))
                context_menu.addAction("📄 滚动到最右", lambda: self.setValue(self.maximum()))
                context_menu.addSeparator()
                context_menu.addAction("⬅️ 向左翻页", lambda: self.setValue(max(self.minimum(), self.value() - self.pageStep())))
                context_menu.addAction("➡️ 向右翻页", lambda: self.setValue(min(self.maximum(), self.value() + self.pageStep())))
                context_menu.addSeparator()
                context_menu.addAction("🎯 滚动到此处", lambda: self.scroll_to_position(position))

            # 显示菜单
            context_menu.exec_(self.mapToGlobal(position))

        except Exception as e:
            print(f"显示滚动条右键菜单失败: {str(e)}")

    def scroll_to_position(self, position):
        """根据点击位置计算并滚动到对应位置"""
        try:
            if self.orientation() == Qt.Vertical:
                # 垂直滚动条
                total_height = self.height()
                click_ratio = position.y() / total_height
            else:
                # 水平滚动条
                total_width = self.width()
                click_ratio = position.x() / total_width

            # 计算目标值
            value_range = self.maximum() - self.minimum()
            target_value = self.minimum() + int(value_range * click_ratio)

            # 设置滚动位置
            self.setValue(target_value)

        except Exception as e:
            print(f"滚动到指定位置失败: {str(e)}")

class CalendarManager:
    """日历管理器 - 确保同时只显示一个日历"""
    _instance = None
    _current_popup = None
    _current_button = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def show_calendar(self, button, popup):
        # 隐藏当前显示的日历
        if self._current_popup and self._current_popup != popup:
            self._current_popup.hide()
            if self._current_button:
                # 移除之前的全局事件过滤器
                QApplication.instance().removeEventFilter(self._current_button)

        self._current_popup = popup
        self._current_button = button

    def hide_current_calendar(self):
        if self._current_popup:
            self._current_popup.hide()
            if self._current_button:
                # 移除全局事件过滤器
                QApplication.instance().removeEventFilter(self._current_button)
            self._current_popup = None
            self._current_button = None

class ModernDateButton(QPushButton):
    """现代化日期选择按钮，点击弹出自定义日历"""

    dateChanged = pyqtSignal(QDate)

    def __init__(self, parent=None):
        super(ModernDateButton, self).__init__(parent)
        self.selected_date = QDate(2026, 4, 8)  # 默认日期
        self.selected_time = QTime(0, 0)  # 默认时间 00:00
        self.calendar_popup = None
        self.calendar_manager = CalendarManager.get_instance()

        # 设置按钮样式 - 调整宽度以容纳日期时间
        self.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px 8px;
                background-color: white;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                text-align: left;
                min-width: 130px;
            }
            QPushButton:hover {
                border-color: #80bdff;
            }
            QPushButton:pressed {
                background-color: #f8f9fa;
            }
        """)

        # 更新显示文本
        self.updateText()

        # 连接点击事件
        self.clicked.connect(self.showCalendar)

    def updateText(self):
        """更新按钮显示文本 - 显示到小时分钟"""
        if self.selected_date.isValid():
            # 创建一个包含时间的QDateTime对象
            datetime = QDateTime(self.selected_date, self.selected_time)
            self.setText(datetime.toString("yyyy-MM-dd hh:mm"))
        else:
            self.setText("选择日期时间")

    def showCalendar(self):
        """显示日历弹窗 - 在软件内部显示"""
        # 如果当前日历已经显示，则隐藏它
        if self.calendar_popup and self.calendar_popup.isVisible():
            self.calendar_manager.hide_current_calendar()
            return

        if self.calendar_popup is None:
            # 获取主窗口
            main_window = self.window()

            self.calendar_popup = QWidget(main_window)
            self.calendar_popup.setWindowFlags(Qt.Widget)
            # 不设置额外样式，保持原来的日历样式

            layout = QVBoxLayout(self.calendar_popup)
            layout.setContentsMargins(0, 0, 0, 0)

            # 创建现代化日历
            self.calendar = ModernCalendar()
            self.calendar.setSelectedDate(self.selected_date)
            self.calendar.clicked.connect(self.onDateSelected)

            layout.addWidget(self.calendar)

            # 设置固定大小
            self.calendar_popup.setFixedSize(300, 350)

        # 设置日历的当前日期
        self.calendar.setSelectedDate(self.selected_date)

        # 计算在主窗口内的位置
        button_pos = self.mapTo(self.window(), QPoint(0, 0))
        popup_x = button_pos.x()
        popup_y = button_pos.y() + self.height() + 5

        # 确保弹窗不超出主窗口边界
        main_window = self.window()
        if popup_x + 300 > main_window.width():
            popup_x = main_window.width() - 300 - 10
        if popup_y + 350 > main_window.height():
            popup_y = button_pos.y() - 350 - 5

        self.calendar_popup.move(popup_x, popup_y)

        # 使用日历管理器显示日历
        self.calendar_manager.show_calendar(self, self.calendar_popup)
        self.calendar_popup.show()
        self.calendar_popup.raise_()  # 确保显示在最前面

        # 安装事件过滤器，点击其他地方时隐藏日历
        QApplication.instance().installEventFilter(self)

    def onDateSelected(self, date):
        """日期选择回调 - 确保只保存日期部分"""
        if date.isValid():
            # 如果传入的是QDateTime，转换为QDate
            if hasattr(date, 'date'):
                self.selected_date = date.date()
            else:
                self.selected_date = date
            self.updateText()
            self.dateChanged.emit(self.selected_date)

        # 使用日历管理器隐藏日历
        self.calendar_manager.hide_current_calendar()

    def setDate(self, date):
        """设置日期"""
        self.selected_date = date
        self.updateText()

    def setTime(self, time):
        """设置时间"""
        self.selected_time = time
        self.updateText()

    def setDateTime(self, datetime):
        """设置日期时间"""
        self.selected_date = datetime.date()
        self.selected_time = datetime.time()
        self.updateText()

    def date(self):
        """获取日期"""
        return self.selected_date

    def time(self):
        """获取时间"""
        return self.selected_time

    def dateTime(self):
        """获取日期时间"""
        return QDateTime(self.selected_date, self.selected_time)

    def eventFilter(self, obj, event):
        """事件过滤器 - 点击其他地方时隐藏日历"""
        if (self.calendar_popup and self.calendar_popup.isVisible() and
            event.type() == QEvent.MouseButtonPress):

            # 获取点击的控件
            clicked_widget = QApplication.widgetAt(event.globalPos())

            # 检查点击的控件是否在日历弹窗内或是按钮本身
            if clicked_widget:
                # 检查是否点击了按钮本身
                if clicked_widget == self:
                    return super().eventFilter(obj, event)

                # 检查是否点击了日历弹窗或其子控件
                parent = clicked_widget
                while parent:
                    if parent == self.calendar_popup:
                        return super().eventFilter(obj, event)
                    parent = parent.parent()

                # 如果点击的不是日历相关控件，隐藏日历
                self.calendar_manager.hide_current_calendar()

        return super().eventFilter(obj, event)

def get_config_path(filename):
    """获取配置文件的完整路径"""
    try:
        # 获取脚本所在目录
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe文件
            script_dir = os.path.dirname(sys.executable)
        else:
            # 如果是开发环境
            script_dir = os.path.dirname(os.path.abspath(__file__))

        # 构建配置文件完整路径
        config_path = os.path.join(script_dir, 'config', filename)

        return config_path
    except Exception as e:
        print(f"获取配置文件路径失败: {str(e)}")
        return os.path.join('config', filename)

class CheckBoxNumberWidget(QWidget):
    """复选框和序号组合的自定义Widget"""
    def __init__(self, number, parent=None):
        super().__init__(parent)

        # 创建水平布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 0, 5, 0)
        layout.setSpacing(5)

        # 创建复选框
        self.checkbox = QCheckBox()
        self.checkbox.setFixedSize(16, 16)

        # 创建序号标签
        self.number_label = QLabel(str(number))
        self.number_label.setAlignment(Qt.AlignCenter)

        # 添加到布局
        layout.addWidget(self.checkbox)
        layout.addWidget(self.number_label)
        layout.addStretch()  # 添加弹性空间

    def isChecked(self):
        """获取复选框状态"""
        return self.checkbox.isChecked()

    def setChecked(self, checked):
        """设置复选框状态"""
        self.checkbox.setChecked(checked)


class ImageLoader(QThread):
    """异步图片加载器"""
    imageLoaded = pyqtSignal(QPixmap)
    loadFailed = pyqtSignal(str)

    def __init__(self, image_url, target_size):
        super().__init__()
        self.image_url = image_url
        self.target_size = target_size
        self._is_cancelled = False

    def cancel(self):
        """取消加载"""
        self._is_cancelled = True

    def run(self):
        """异步加载图片"""
        try:
            if self._is_cancelled:
                return

            import urllib.request
            from PyQt5.QtGui import QPixmap

            # 下载图片数据
            with urllib.request.urlopen(self.image_url, timeout=3) as response:
                if self._is_cancelled:
                    return
                image_data = response.read()

            if self._is_cancelled:
                return

            # 创建QPixmap
            pixmap = QPixmap()
            if pixmap.loadFromData(image_data):
                # 缩放图片以适应目标大小，保持宽高比
                scaled_pixmap = pixmap.scaled(
                    self.target_size,
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
                if not self._is_cancelled:
                    self.imageLoaded.emit(scaled_pixmap)
            else:
                if not self._is_cancelled:
                    self.loadFailed.emit("图片格式错误")

        except Exception as e:
            if not self._is_cancelled:
                self.loadFailed.emit(f"加载失败: {str(e)}")
    def load_single_shop_products(self, shop_name, config_data):
        """加载单个店铺的商品"""
        try:
            print(f"开始加载店铺 {shop_name} 的商品...")
            
            # 清理店铺名称，去掉商品数量信息 [数字/数字]
            import re
            clean_shop_name = re.sub(r'\[\d+/\d+\]', '', shop_name).strip()
            print(f"清理后的店铺名称: '{clean_shop_name}'")
            
            # 查找店铺配置
            account_info = self.find_shop_info(config_data, clean_shop_name)
            if not account_info:
                print(f"未找到店铺 {shop_name} 的配置信息")
                self.progress_updated.emit(f"未找到店铺 {shop_name} 的配置信息")
                return []
            
            shop_id = account_info.get('店铺ID', '')
            if not shop_id:
                print(f"店铺 {shop_name} 的配置中没有找到店铺ID")
                self.progress_updated.emit(f"店铺 {shop_name} 的配置中没有找到店铺ID")
                return []
            
            print(f"店铺 {shop_name} 的店铺ID: {shop_id}")
            
            # 创建API实例
            api = KuaishouCookieAPI()
            print(f"正在切换到店铺 {shop_id}...")
            if not api.switch_account(shop_id):
                print(f"切换到店铺 {shop_id} 失败")
                self.progress_updated.emit(f"切换到店铺 {shop_id} 失败")
                return []
            
            # 获取商品列表
            all_products = []
            page = 1
            print(f"开始获取店铺 {shop_name} 的商品列表...")
            
            while True:
                if self.should_stop:
                    break
                
                print(f"正在获取第 {page} 页商品...")
                result = api.get_product_list(manager_tab="ON_SALE", cur_page=page, page_size=50)
                print(f"API调用结果: {result.get('result')}, 错误信息: {result.get('error_msg', '无')}")
                
                if result.get('result') != 1:
                    print(f"获取商品列表失败: {result.get('error_msg', '未知错误')}")
                    self.progress_updated.emit(f"获取商品列表失败: {result.get('error_msg', '未知错误')}")
                    break
                
                data = result.get('data', {})
                products = data.get('dataSource', [])
                print(f"第 {page} 页获取到 {len(products)} 个商品")
                
                if not products:
                    print(f"第 {page} 页没有商品，停止加载")
                    break
                
                # 添加店铺名称到每个商品（使用清理后的店铺名称）
                for product in products:
                    product['shop_name'] = clean_shop_name
                
                all_products.extend(products)
                page += 1

                # 移除10页限制，加载所有商品
                # 继续加载直到没有更多商品
            
            print(f"店铺 {shop_name} 总共加载了 {len(all_products)} 个商品")
            return all_products
            
        except Exception as e:
            print(f"加载店铺 {shop_name} 商品失败: {str(e)}")
            import traceback
            traceback.print_exc()
            self.progress_updated.emit(f"加载店铺 {shop_name} 商品异常: {str(e)}")
            return []
    

    
    def query_shop_supplier_info(self, shop_name, config_data, start_row, product_count):
        """查询单个店铺的上家信息（并发50线程）"""
        try:
            from concurrent.futures import ThreadPoolExecutor, as_completed
            import threading
            import re
            
            # 清理店铺名称，去掉商品数量信息 [数字/数字]
            clean_shop_name = re.sub(r'\[\d+/\d+\]', '', shop_name).strip()
            print(f"查询上家信息 - 清理后的店铺名称: '{clean_shop_name}'")
            
            # 查找店铺配置
            account_info = self.find_shop_info(config_data, clean_shop_name)
            if not account_info:
                return 0
            
            access_token = account_info.get('accesstoken')
            if not access_token:
                return 0
            
            # 收集需要查询的商品（上家列为空的）
            products_to_query = []
            for i in range(product_count):
                row = start_row + i
                
                # 检查上家列是否为空
                supplier_item = self.product_table.item(row, 14)
                if supplier_item and supplier_item.text().strip():
                    continue  # 已有上家信息，跳过
                
                # 获取商品ID
                product_id_item = self.product_table.item(row, 2)
                if product_id_item:
                    product_id = product_id_item.text().strip()
                    products_to_query.append({
                        'row': row,
                        'product_id': product_id
                    })
            
            if not products_to_query:
                return 0
            
            # 并发查询上家信息
            success_count = 0
            processed_count = 0
            count_lock = threading.Lock()
            
            def query_single_product(product):
                """查询单个商品的上家信息"""
                try:
                    row = product['row']
                    product_id = product['product_id']
                    
                    # 创建API实例
                    api = KuaishouAPI(access_token=access_token)
                    
                    # 调用商品详情接口
                    result = api.get_item_detail(int(product_id))
                    
                    if result.get('success'):
                        data = result.get('data', {})
                        sku_infos = data.get('skuInfos', [])
                        
                        if sku_infos:
                            sku_nick = sku_infos[0].get('skuNick', '')
                            if sku_nick:
                                # 处理上家信息：提取纯数字
                                processed_sku_nick = self.extract_numbers_only(sku_nick)
                                
                                # 线程安全地更新数据
                                with count_lock:
                                    self.supplier_data[product_id] = processed_sku_nick
                                
                                return (row, processed_sku_nick, True)
                            else:
                                return (row, '未找到上家信息', False)
                        else:
                            return (row, '无SKU信息', False)
                    else:
                        return (row, '查询失败', False)
                        
                except Exception as e:
                    print(f"查询商品 {product_id} 详情失败: {str(e)}")
                    return (row, f'查询异常', False)
            
            # 使用线程池并发查询
            with ThreadPoolExecutor(max_workers=50) as executor:
                future_to_product = {executor.submit(query_single_product, product): product for product in products_to_query}
                
                for future in as_completed(future_to_product):
                    if self.should_stop:
                        break
                        
                    try:
                        row, result_text, success = future.result()
                        
                        # 发送行更新信号
                        self.row_updated.emit(row, result_text)
                        
                        # 更新计数
                        with count_lock:
                            processed_count += 1
                            if success:
                                success_count += 1
                                
                        # 更新进度
                        progress_text = f"店铺 {shop_name} 上家查询: {processed_count}/{len(products_to_query)}"
                        self.progress_updated.emit(progress_text)
                        
                    except Exception as e:
                        print(f"处理查询结果时出错: {str(e)}")
                        with count_lock:
                            processed_count += 1
            
            # 保存查询到的上家信息到SQLite数据库（按店铺保存）
            self.save_supplier_data_to_db(shop_name)

            return success_count

        except Exception as e:
            print(f"查询店铺 {shop_name} 上家信息失败: {str(e)}")
            return 0
    
    def find_shop_info(self, config_data, shop_name):
        """从配置数据中查找店铺信息"""
        try:
            if 'data' in config_data:
                for shop_data in config_data['data']:
                    if shop_data.get('店铺名称') == shop_name:
                        return shop_data
            return None
        except Exception as e:
            print(f"查找店铺信息时出错: {str(e)}")
            return None
    
    def extract_numbers_only(self, text):
        """提取字符串中的纯数字"""
        try:
            import re
            numbers = re.findall(r'\d+', str(text))
            if numbers:
                result = ''.join(numbers)
                print(f"上家信息处理: '{text}' -> '{result}'")
                return result
            else:
                print(f"上家信息中未找到数字: '{text}'")
                return text
        except Exception as e:
            print(f"处理上家信息时出错: {str(e)}")
            return text
    
    def save_supplier_data_to_db(self, shop_name):
        """按店铺保存上家信息到SQLite数据库（高性能解决方案）"""
        try:
            import sqlite3

            # 检查是否有上家数据和店铺名称
            if not hasattr(self, 'supplier_data') or not self.supplier_data or not shop_name:
                print(f"警告：无法保存店铺 {shop_name} 的上家信息，supplier_data为空或店铺名称缺失")
                return

            # 创建上家信息目录
            supplier_dir = get_config_path("上家信息")
            if not os.path.exists(supplier_dir):
                os.makedirs(supplier_dir)

            # 使用SQLite数据库文件
            db_file_path = os.path.join(supplier_dir, f"{shop_name}.db")

            # 连接数据库
            conn = sqlite3.connect(db_file_path)
            cursor = conn.cursor()

            # 创建表（如果不存在）- 添加supplier_name字段存储上家店铺名称
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS supplier_info (
                    product_id TEXT PRIMARY KEY,
                    supplier_id TEXT NOT NULL,
                    supplier_name TEXT DEFAULT '',
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 检查并添加supplier_name列（兼容现有数据库）
            try:
                cursor.execute("PRAGMA table_info(supplier_info)")
                columns = [column[1] for column in cursor.fetchall()]
                if 'supplier_name' not in columns:
                    cursor.execute('ALTER TABLE supplier_info ADD COLUMN supplier_name TEXT DEFAULT ""')
                    print("✅ 数据库已升级：添加supplier_name字段")
            except Exception as alter_error:
                print(f"⚠️ 数据库升级警告: {str(alter_error)}")  # 不影响主要功能

            # 创建索引（提高查询性能）
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_product_id ON supplier_info(product_id)
            ''')

            # 批量插入或更新数据（使用UPSERT）
            new_count = 0
            update_count = 0

            for product_id, supplier_data in self.supplier_data.items():
                # 兼容旧格式（只有supplier_id）和新格式（包含supplier_name）
                if isinstance(supplier_data, dict):
                    supplier_id = supplier_data.get('supplier_id', '')
                    supplier_name = supplier_data.get('supplier_name', '')
                else:
                    # 旧格式，只有supplier_id
                    supplier_id = str(supplier_data)
                    supplier_name = ''

                # 检查是否已存在
                cursor.execute('SELECT supplier_id, supplier_name FROM supplier_info WHERE product_id = ?', (product_id,))
                existing = cursor.fetchone()

                if existing:
                    # 如果数据不同则更新（检查supplier_id或supplier_name是否有变化）
                    existing_supplier_id = existing[0] if existing[0] else ''
                    existing_supplier_name = existing[1] if len(existing) > 1 and existing[1] else ''

                    if existing_supplier_id != supplier_id or existing_supplier_name != supplier_name:
                        cursor.execute('''
                            UPDATE supplier_info
                            SET supplier_id = ?, supplier_name = ?, updated_time = CURRENT_TIMESTAMP
                            WHERE product_id = ?
                        ''', (supplier_id, supplier_name, product_id))
                        update_count += 1
                else:
                    # 插入新数据，包含上家名称
                    cursor.execute('''
                        INSERT INTO supplier_info (product_id, supplier_id, supplier_name)
                        VALUES (?, ?, ?)
                    ''', (product_id, supplier_id, supplier_name))
                    new_count += 1

            # 提交事务
            conn.commit()

            # 获取总记录数
            cursor.execute('SELECT COUNT(*) FROM supplier_info')
            total_count = cursor.fetchone()[0]

            # 关闭连接
            conn.close()

            print(f"店铺 {shop_name} 上家信息已保存到SQLite: 新增 {new_count} 条，更新 {update_count} 条，总计 {total_count} 条记录")

        except Exception as e:
            print(f"保存上家信息失败: {str(e)}")

    def load_supplier_data_from_db(self, shop_name):
        """按店铺从SQLite数据库加载上家信息"""
        try:
            import sqlite3
            import json

            if not shop_name:
                return {}

            # 从店铺文件读取
            supplier_dir = get_config_path("上家信息")
            if os.path.exists(supplier_dir):

                # 优先尝试SQLite数据库
                db_file_path = os.path.join(supplier_dir, f"{shop_name}.db")
                if os.path.exists(db_file_path):
                    try:
                        conn = sqlite3.connect(db_file_path)
                        cursor = conn.cursor()
                        # 查询时同时获取supplier_id和supplier_name
                        cursor.execute('SELECT product_id, supplier_id, supplier_name FROM supplier_info')
                        rows = cursor.fetchall()
                        conn.close()

                        # 构建包含上家ID和上家名称的数据结构
                        supplier_data = {}
                        for row in rows:
                            product_id = row[0]
                            supplier_id = row[1] if row[1] else ''
                            supplier_name = row[2] if len(row) > 2 and row[2] else ''

                            supplier_data[product_id] = {
                                'supplier_id': supplier_id,
                                'supplier_name': supplier_name
                            }

                        print(f"从SQLite数据库加载上家信息: {db_file_path}，共 {len(supplier_data)} 条记录")
                        return supplier_data
                    except Exception as e:
                        print(f"读取店铺 {shop_name} SQLite数据库失败: {str(e)}")

                # 兼容旧的JSON文件
                json_file_path = os.path.join(supplier_dir, f"{shop_name}.json")
                if os.path.exists(json_file_path):
                    try:
                        with open(json_file_path, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            if content:
                                supplier_data = json.loads(content)
                                print(f"从JSON文件加载上家信息: {json_file_path}，共 {len(supplier_data)} 条记录")
                                return supplier_data
                    except Exception as e:
                        print(f"读取店铺 {shop_name} JSON文件失败: {str(e)}")

            print(f"店铺 {shop_name} 没有上家信息缓存文件")
            return {}

        except Exception as e:
            print(f"加载上家信息失败: {str(e)}")
            return {}

    def get_shop_names_from_table(self):
        """从表格中获取所有店铺名称"""
        try:
            shop_names = set()
            row_count = self.product_table.rowCount()

            # 找到店铺列的索引
            shop_column = -1
            for col in range(self.product_table.columnCount()):
                header_item = self.product_table.horizontalHeaderItem(col)
                if header_item and header_item.text() == "店铺":
                    shop_column = col
                    break

            if shop_column == -1:
                print("未找到店铺列")
                return set()

            # 遍历表格获取店铺名称
            for row in range(row_count):
                shop_item = self.product_table.item(row, shop_column)
                if shop_item and shop_item.text().strip():
                    shop_name = shop_item.text().strip()
                    # 清理店铺名称，去掉评分等信息
                    if '(' in shop_name and shop_name.endswith(')'):
                        shop_name = shop_name.split('(')[0].strip()
                    elif '（' in shop_name and shop_name.endswith('）'):
                        shop_name = shop_name.split('（')[0].strip()

                    if shop_name:
                        shop_names.add(shop_name)

            print(f"从表格中获取到 {len(shop_names)} 个店铺: {list(shop_names)}")
            return shop_names

        except Exception as e:
            print(f"获取表格店铺名称失败: {str(e)}")
            return set()


class ShopByShopQueryThread(QThread):
    """逐店铺查询管理器 - 每个店铺单独处理完整流程，支持实时加载和自动下架"""
    progress_updated = pyqtSignal(str)  # 总体进度更新信号
    shop_started = pyqtSignal(str, int, int)  # 开始处理店铺信号 (店铺名, 当前序号, 总数)
    shop_products_loaded = pyqtSignal(str, list)  # 店铺商品加载完成信号 (店铺名, 商品列表)
    page_products_loaded = pyqtSignal(str, list)  # 单页商品实时加载信号 (店铺名, 单页商品列表) - 新增
    shop_query_completed = pyqtSignal(str, int, int)  # 店铺查询完成信号 (店铺名, 成功数, 总数)
    shop_failed = pyqtSignal(str, str)  # 店铺处理失败信号 (店铺名, 错误信息)
    all_completed = pyqtSignal(int, int, int, int)  # 全部完成信号 (总成功数, 总商品数, 成功店铺数, 总店铺数)
    row_status_updated = pyqtSignal(int, str, str)  # 行状态更新信号 (行号, 状态文本, 状态颜色)
    supplier_name_updated = pyqtSignal(int, str)  # 上家名称更新信号 (行号, 上家名称)
    # 新增自动下架相关信号
    auto_delist_required = pyqtSignal(list)  # 自动下架需求信号 (需要下架的商品列表)
    shop_auto_delist_required = pyqtSignal(str, list)  # 单个店铺自动下架需求信号 (店铺名, 需要下架的商品列表)
    all_completed_with_auto_delist = pyqtSignal(int, int, int, int, int)  # 全部完成且有自动下架信号 (总成功数, 总商品数, 成功店铺数, 总店铺数, 待下架数)

    def __init__(self, checked_shops, product_manager=None):
        super().__init__()
        self.checked_shops = checked_shops
        self.product_manager = product_manager  # 添加 ProductManager 实例引用
        self.should_stop = False
        self.total_success = 0
        self.total_products = 0
        self.success_shops = 0
        self.kuaishou_api = KuaishouCookieAPI()
        self.all_products_to_auto_delist = []  # 所有需要自动下架的商品列表
        self.total_auto_delist_success = 0  # 统计所有店铺下架成功的商品数量

        # 添加等待机制，用于等待每个店铺的自动下架完成
        self.auto_delist_mutex = QMutex()
        self.auto_delist_wait_condition = QWaitCondition()
        self.auto_delist_completed = False
        self.current_shop_processing = None  # 当前正在处理的店铺名称

    def find_column_by_header_name(self, header_name):
        """通过表头名查找列索引"""
        for col in range(self.product_manager.product_table.columnCount()):
            header_item = self.product_manager.product_table.horizontalHeaderItem(col)
            if header_item and header_item.text() == header_name:
                return col
        return -1

    def find_table_row_by_product_id(self, product_id):
        """根据商品ID查找对应的表格行号"""
        try:
            # 获取表格行数
            row_count = self.product_manager.product_table.rowCount()

            # 通过表头名找到商品ID列
            product_id_col = self.find_column_by_header_name("商品ID")
            if product_id_col == -1:
                print("未找到商品ID列")
                return -1

            # 遍历表格，查找商品ID列
            for row in range(row_count):
                item = self.product_manager.product_table.item(row, product_id_col)
                if item and item.text() == str(product_id):
                    return row

            # 如果没找到，返回-1
            return -1
        except Exception as e:
            print(f"查找商品ID {product_id} 对应的表格行时出错: {str(e)}")
            return -1

    def stop(self):
        """停止查询"""
        self.should_stop = True

    def wait_for_auto_delist_completion(self, shop_name):
        """等待指定店铺的自动下架操作完成（设置合理超时，确保可靠性）"""
        try:
            print(f"开始等待店铺 {shop_name} 的自动下架操作完成...")
            self.auto_delist_mutex.lock()
            try:
                # 重置完成标志
                self.auto_delist_completed = False

                # 设置合理的超时时间（5分钟），避免无限等待
                timeout_ms = 300000  # 5分钟
                if not self.auto_delist_wait_condition.wait(self.auto_delist_mutex, timeout_ms):
                    print(f"⚠️ 等待店铺 {shop_name} 自动下架完成超时（5分钟），继续下一个店铺")
                    print(f"⚠️ 这可能表示下架操作遇到了问题，请检查网络连接和API状态")
                else:
                    print(f"✅ 店铺 {shop_name} 自动下架操作已完成，继续下一个店铺")
            finally:
                self.auto_delist_mutex.unlock()
        except Exception as e:
            print(f"❌ 等待店铺 {shop_name} 自动下架完成时出错: {str(e)}")

    def notify_auto_delist_completed(self, success_count=0):
        """通知自动下架操作完成"""
        try:
            # 更新总的下架成功统计
            self.total_auto_delist_success += success_count
            print(f"店铺下架完成，成功 {success_count} 个，总计成功 {self.total_auto_delist_success} 个")

            self.auto_delist_mutex.lock()
            try:
                self.auto_delist_completed = True
                self.auto_delist_wait_condition.wakeAll()
            finally:
                self.auto_delist_mutex.unlock()
        except Exception as e:
            print(f"通知自动下架完成时出错: {str(e)}")

    def run(self):
        """逐店铺执行查询"""
        try:
            for i, shop_name in enumerate(self.checked_shops, 1):
                if self.should_stop:
                    break

                # 发送开始处理信号
                self.shop_started.emit(shop_name, i, len(self.checked_shops))

                # 处理单个店铺的完整流程
                try:
                    success_count, total_count = self.process_single_shop_complete(shop_name)

                    # 累计统计
                    self.total_success += success_count
                    self.total_products += total_count

                    if success_count > 0 or total_count > 0:
                        self.success_shops += 1

                    # 发送店铺完成信号
                    self.shop_query_completed.emit(shop_name, success_count, total_count)

                except Exception as e:
                    error_msg = f"处理失败: {str(e)}"
                    print(f"店铺 {shop_name} 处理异常: {error_msg}")
                    self.shop_failed.emit(shop_name, error_msg)
                    continue  # 继续处理下一个店铺

            # 发送全部完成信号
            if not self.should_stop:
                auto_delist_count = len(self.all_products_to_auto_delist)
                print(f"逐店铺查询完成，总计处理了 {auto_delist_count} 个需要自动下架的商品，成功下架 {self.total_auto_delist_success} 个")

                if auto_delist_count > 0:
                    # 有自动下架需求，发送带下架统计的完成信号
                    self.all_completed_with_auto_delist.emit(
                        self.total_success, self.total_products,
                        self.success_shops, len(self.checked_shops),
                        self.total_auto_delist_success  # 传递下架成功数量
                    )
                else:
                    # 没有自动下架需求，发送普通完成信号
                    self.all_completed.emit(self.total_success, self.total_products,
                                          self.success_shops, len(self.checked_shops))

        except Exception as e:
            print(f"逐店铺查询异常: {str(e)}")
            import traceback
            traceback.print_exc()
            self.progress_updated.emit(f"查询异常: {str(e)}")

    def process_single_shop_complete(self, shop_name):
        """处理单个店铺的完整流程：切换店铺→加载商品→立即查询状态"""
        try:
            # 清理店铺名称，去掉商品数量信息
            import re
            clean_shop_name = re.sub(r'\[\d+/\d+\]', '', shop_name).strip()

            # 步骤1：切换到店铺
            self.progress_updated.emit(f"正在切换到店铺: {clean_shop_name}")
            if not self.switch_to_shop(clean_shop_name):
                raise Exception(f"切换到店铺 {clean_shop_name} 失败")

            # 步骤2：加载店铺商品（实时加载，每页都会通过信号发送到主界面显示）
            self.progress_updated.emit(f"正在加载店铺 {clean_shop_name} 的商品...")
            products = self.load_shop_products(clean_shop_name)

            if not products:
                self.progress_updated.emit(f"店铺 {clean_shop_name} 没有商品")
                # 发送空商品列表信号，让主界面清空表格
                self.shop_products_loaded.emit(clean_shop_name, [])
                return 0, 0

            # 发送商品加载完成信号，让主界面显示商品
            self.shop_products_loaded.emit(clean_shop_name, products)

            # 步骤3：检查是否有上家信息，如果没有则先查询上家信息
            if self.product_manager:
                products_with_supplier = self.product_manager.filter_products_with_supplier(products)
                print(f"🔍 [DEBUG] filter_products_with_supplier 返回结果: 类型={type(products_with_supplier)}, 长度={len(products_with_supplier) if products_with_supplier else 'None'}, 值={products_with_supplier}")
            else:
                print("ProductManager 实例未传递，无法筛选上家信息")
                products_with_supplier = []

            print(f"🔍 [DEBUG] 检查条件 'not products_with_supplier': {not products_with_supplier}")
            if not products_with_supplier:
                # 没有上家信息的商品，先查询上家信息
                self.progress_updated.emit(f"店铺 {clean_shop_name} 没有上家信息，正在查询上家信息...")

                # 查询上家信息
                missing_products = []
                for i, product in enumerate(products):
                    missing_products.append({
                        'row': i,
                        'product_id': product.get('itemId', '')
                    })

                if missing_products:
                    # 调用查询上家信息的方法
                    if self.product_manager:
                        print(f"🔍 [DEBUG] 准备调用 query_shop_supplier_info_for_status_check，店铺: {clean_shop_name}, 商品数量: {len(missing_products)}")
                        try:
                            success_count = self.product_manager.query_shop_supplier_info_for_status_check(clean_shop_name, missing_products)
                            print(f"🔍 [DEBUG] query_shop_supplier_info_for_status_check 调用完成，返回值: {success_count}")
                        except Exception as e:
                            print(f"🔍 [DEBUG] query_shop_supplier_info_for_status_check 调用异常: {str(e)}")
                            success_count = 0
                    else:
                        print("ProductManager 实例未传递，无法查询上家信息")
                        success_count = 0

                    # 重新筛选有上家信息的商品（强制重新加载缓存）
                    if self.product_manager:
                        products_with_supplier = self.product_manager.filter_products_with_supplier(products, force_reload_cache=True)
                    else:
                        products_with_supplier = []

                    if not products_with_supplier:
                        self.progress_updated.emit(f"店铺 {clean_shop_name} 查询上家信息后仍无可查询的商品")
                        return 0, len(products)
                else:
                    self.progress_updated.emit(f"店铺 {clean_shop_name} 没有需要查询上家状态的商品")
                    return 0, len(products)

            # 步骤4：立即查询上家状态（不等待，直接在当前店铺处理完成）
            self.progress_updated.emit(f"正在查询店铺 {clean_shop_name} 的 {len(products_with_supplier)} 个商品状态...")
            success_count = self.query_supplier_status_for_shop_immediate(products_with_supplier, clean_shop_name)

            return success_count, len(products_with_supplier)

        except Exception as e:
            error_msg = f"处理店铺 {shop_name} 时出错: {str(e)}"
            print(error_msg)
            raise Exception(error_msg)

    def switch_to_shop(self, shop_name):
        """切换到指定店铺"""
        try:
            # 从配置文件获取店铺信息
            config_data = self.load_config_data()
            if not config_data:
                return False

            # 查找店铺配置
            account_info = self.find_shop_info(config_data, shop_name)
            if not account_info:
                print(f"未找到店铺 {shop_name} 的配置信息")
                return False

            shop_id = account_info.get('店铺ID', '')
            if not shop_id:
                print(f"店铺 {shop_name} 的配置中没有找到店铺ID")
                return False

            # 切换到指定店铺
            if not self.kuaishou_api.switch_account(shop_id):
                print(f"切换到店铺 {shop_name} 失败")
                return False

            # 验证cookies是否有效
            if not self.kuaishou_api.validate_cookies():
                print(f"店铺 {shop_name} 的Cookies无效")
                return False

            return True

        except Exception as e:
            print(f"切换店铺 {shop_name} 异常: {str(e)}")
            return False

    def load_shop_products(self, shop_name):
        """加载店铺商品 - 支持并发加载和实时显示"""
        try:
            from concurrent.futures import ThreadPoolExecutor, as_completed
            import threading

            all_products = []
            total_pages = None

            # 首先获取第一页来确定总页数
            print(f"正在获取店铺 {shop_name} 商品信息...")
            self.progress_updated.emit(f"正在获取店铺 {shop_name} 商品信息...|0")

            result = self.kuaishou_api.get_product_list(manager_tab="ON_SALE", cur_page=1, page_size=50)

            if result.get('result') != 1:
                print(f"获取店铺 {shop_name} 商品列表失败: {result.get('error_msg', '未知错误')}")
                return []

            data = result.get('data', {})
            total_count = data.get('total', 0)

            if total_count == 0:
                print(f"店铺 {shop_name} 没有商品")
                self.progress_updated.emit(f"店铺 {shop_name} 没有商品|100")
                return []

            page_size = 50
            total_pages = (total_count + page_size - 1) // page_size  # 向上取整
            print(f"店铺 {shop_name} 总计 {total_count} 个商品，共 {total_pages} 页")

            # 线程安全的变量
            products_lock = threading.Lock()
            completed_pages = 0

            def load_single_page(page_num):
                """加载单页商品数据"""
                try:
                    if self.should_stop:
                        return None

                    result = self.kuaishou_api.get_product_list(manager_tab="ON_SALE", cur_page=page_num, page_size=50)

                    if result.get('result') != 1:
                        print(f"获取店铺 {shop_name} 第 {page_num} 页失败: {result.get('error_msg', '未知错误')}")
                        return None

                    data = result.get('data', {})
                    products = data.get('dataSource', [])

                    if products:
                        # 添加店铺名称到每个商品
                        for product in products:
                            product['shop_name'] = shop_name

                        print(f"成功加载店铺 {shop_name} 第 {page_num} 页，共 {len(products)} 个商品")
                        return (page_num, products)
                    else:
                        print(f"店铺 {shop_name} 第 {page_num} 页没有商品数据")
                        return None

                except Exception as e:
                    print(f"加载店铺 {shop_name} 第 {page_num} 页异常: {str(e)}")
                    return None

            # 根据商品数量动态调整并发数
            if total_count <= 2000:
                concurrent_count = 10
                print(f"商品数量 {total_count} ≤ 2000，使用 {concurrent_count} 个并发")
            else:
                concurrent_count = 6
                print(f"商品数量 {total_count} > 2000，使用 {concurrent_count} 个并发，并添加延时")

            current_page = 1

            while current_page <= total_pages and not self.should_stop:
                # 计算当前批次要加载的页面
                batch_pages = []
                for i in range(concurrent_count):
                    if current_page + i <= total_pages:
                        batch_pages.append(current_page + i)
                    else:
                        break

                print(f"并发加载店铺 {shop_name} 第 {batch_pages[0]}-{batch_pages[-1]} 页（共 {len(batch_pages)} 页）")

                # 顺序加载当前批次的页面，每个请求之间添加延时
                batch_results = []
                for page in batch_pages:
                    if self.should_stop:
                        break

                    # 加载单页数据
                    result = load_single_page(page)
                    if result:
                        batch_results.append(result)
                        page_num, products = result

                        with products_lock:
                            all_products.extend(products)
                            completed_pages += 1

                            # 实时发送单页商品数据
                            self.page_products_loaded.emit(shop_name, products)

                            # 更新进度
                            progress_percent = int(completed_pages / total_pages * 100)
                            progress_percent = min(progress_percent, 99)  # 确保不超过99%
                            progress_msg = f"已加载店铺 {shop_name} {completed_pages}/{total_pages} 页商品..."
                            self.progress_updated.emit(f"{progress_msg}|{progress_percent}")

                            print(f"已处理店铺 {shop_name} 第 {page_num} 页，当前总计 {len(all_products)} 个商品")

                    # 每个页面请求之间添加延时，避免同时间并发
                    if page < batch_pages[-1]:  # 不是最后一页
                        import time
                        time.sleep(0.5)  # 500ms延时
                        print(f"页面 {page} 加载完成，等待500ms后加载下一页")

                current_page += len(batch_pages)

                # 如果商品数量大于2000，在批次之间添加1秒延时
                if total_count > 2000 and current_page <= total_pages:
                    import time
                    print(f"商品数量 > 2000，批次间延时1秒...")
                    time.sleep(1)

            # 发送加载完成信号 - 不设置为100%，因为还有后续的上家状态查询
            self.progress_updated.emit(f"店铺 {shop_name} 商品加载完成，共 {len(all_products)} 个商品，开始查询上家状态...|50")
            print(f"店铺 {shop_name} 商品加载完成，总计 {len(all_products)} 个商品")

            return all_products

        except Exception as e:
            print(f"加载店铺 {shop_name} 商品异常: {str(e)}")
            return []



    def query_supplier_status_for_shop(self, products):
        """查询店铺商品的上家状态 - 基于现有SupplierStatusThread的真实查询逻辑"""
        try:
            from tool.阿里巴巴接口 import AlibabaAPI
            import json
            import time
            import os
            from concurrent.futures import ThreadPoolExecutor, as_completed

            # 筛选有上家信息的商品
            products_to_query = []
            for product in products:
                # 从商品数据中提取上家信息
                # 这里需要根据实际的商品数据结构来获取上家ID
                supplier_id = self.extract_supplier_id_from_product(product)
                if supplier_id:
                    products_to_query.append({
                        'product': product,
                        'supplier_id': supplier_id,
                        'product_id': product.get('itemId', ''),
                        'title': product.get('title', '')
                    })

            if not products_to_query:
                self.progress_updated.emit(f"店铺商品中没有找到上家信息")
                return 0

            self.progress_updated.emit(f"正在查询 {len(products_to_query)} 个商品的上家状态...")

            # 读取阿里巴巴分销API配置
            config_path = self.get_config_path("config.json")
            if not os.path.exists(config_path):
                self.progress_updated.emit("错误: 未找到配置文件")
                return 0

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 获取阿里巴巴分销API配置
            alibaba_fenxiao_config = config_data.get('alibabafenxiao', {})
            if not alibaba_fenxiao_config:
                self.progress_updated.emit("错误: 未找到阿里巴巴分销API配置")
                return 0

            app_key = alibaba_fenxiao_config.get('app_key')
            app_secret = alibaba_fenxiao_config.get('app_secret')
            access_token = alibaba_fenxiao_config.get('access_token')

            if not app_key or not app_secret or not access_token:
                self.progress_updated.emit("错误: 阿里巴巴分销API配置不完整")
                return 0

            # 创建阿里巴巴API实例 - 使用分销API专用配置
            print(f"使用分销API配置: app_key={app_key}, access_token={access_token[:10]}...")
            api = AlibabaAPI(app_key=app_key, app_secret=app_secret, access_token=access_token, api_type='fenxiao')

            # 验证API配置
            if not hasattr(api, 'app_key') or not api.app_key:
                self.progress_updated.emit("错误: API实例初始化失败")
                return 0

            print(f"API实例初始化成功，app_key: {api.app_key}")

            success_count = 0

            # 并发查询上家商品状态
            def query_single_product(product_info):
                """查询单个商品的上家状态"""
                supplier_id = product_info['supplier_id']
                product_id = product_info['product_id']

                try:
                    print(f"查询商品 {product_id} 的上家 {supplier_id} 状态")

                    # 调用阿里巴巴分销商品详情接口
                    result = api.get_fenxiao_product_info(supplier_id)

                    # 增加延时确保API稳定性
                    time.sleep(0.1)

                    print(f"API调用结果: success={result.get('success')}")

                    # 判断查询结果
                    if result.get('success'):
                        # 获取API返回的原始数据和处理过的数据
                        raw_data = result.get('raw_data', {})
                        product_info_data = result.get('product_info', {})

                        # 优先从处理过的product_info获取状态信息
                        product_status = product_info_data.get('status', '') if isinstance(product_info_data, dict) else ''

                        # 从原始数据获取API级别的错误信息
                        api_success = raw_data.get('success') if isinstance(raw_data, dict) else None
                        error_code = raw_data.get('errorCode', '') if isinstance(raw_data, dict) else ''
                        error_msg = raw_data.get('errorMsg', '') if isinstance(raw_data, dict) else ''

                        # 如果处理过的数据中没有状态，则从原始数据中获取
                        if not product_status and isinstance(raw_data, dict):
                            product_info_raw = raw_data.get('productInfo', {})
                            if isinstance(product_info_raw, dict):
                                product_status = product_info_raw.get('status', '')

                        # 提取并保存上家名称（supplier_login_id）
                        supplier_name = ''
                        if isinstance(product_info_data, dict):
                            supplier_name = product_info_data.get('supplier_login_id', '') or product_info_data.get('supplier_name', '')

                        # 如果从处理过的数据中没有获取到，尝试从原始数据获取
                        if not supplier_name and isinstance(raw_data, dict):
                            product_info_raw = raw_data.get('productInfo', {})
                            if isinstance(product_info_raw, dict):
                                supplier_name = product_info_raw.get('supplierLoginId', '') or product_info_raw.get('supplierName', '')

                        # 如果成功获取到上家名称，更新supplier_data
                        if supplier_name and hasattr(self, 'supplier_data'):
                            # 更新supplier_data为新格式（包含supplier_id和supplier_name）
                            current_supplier_data = self.supplier_data.get(product_id, {})
                            if isinstance(current_supplier_data, dict):
                                current_supplier_data['supplier_name'] = supplier_name
                            else:
                                # 旧格式转换为新格式
                                self.supplier_data[product_id] = {
                                    'supplier_id': str(current_supplier_data) if current_supplier_data else supplier_id,
                                    'supplier_name': supplier_name
                                }
                            print(f"  → 已保存上家名称: {supplier_name}")

                        # 判断商品状态
                        if error_code == '400_3' or '已下架' in str(error_msg):
                            status_text = "已下架"
                            print(f"  → 商品已下架 (错误码: {error_code})")
                        elif error_code == '400_2' or '不存在' in str(error_msg):
                            status_text = "不存在"
                            print(f"  → 商品不存在 (错误码: {error_code})")
                        elif api_success is True and product_status == "published":
                            status_text = "在线"
                            print(f"  → 商品在线")
                        elif product_status in ["expired", "auto expired", "member expired"]:
                            status_text = "已过期"
                            print(f"  → 商品已过期: {product_status}")
                        elif product_status in ["deleted", "member deleted"]:
                            status_text = "已删除"
                            print(f"  → 商品已删除: {product_status}")
                        elif product_status == "auditing":
                            status_text = "审核中"
                            print(f"  → 商品审核中")
                        elif product_status == "untread":
                            status_text = "审核未通过"
                            print(f"  → 商品审核未通过")
                        else:
                            status_text = product_status or "状态未知"
                            print(f"  → 其他状态: {status_text}")

                        return True  # 查询成功
                    else:
                        print(f"  → API调用失败")
                        return False

                except Exception as e:
                    print(f"查询商品 {product_id} 异常: {str(e)}")
                    return False

            # 使用线程池并发查询
            with ThreadPoolExecutor(max_workers=50) as executor:
                future_to_product = {executor.submit(query_single_product, product_info): product_info
                                   for product_info in products_to_query}

                for future in as_completed(future_to_product):
                    if self.should_stop:
                        break

                    try:
                        result = future.result()
                        if result:
                            success_count += 1
                    except Exception as e:
                        print(f"查询任务异常: {str(e)}")

            self.progress_updated.emit(f"上家状态查询完成: 成功 {success_count}/{len(products_to_query)}")
            return success_count

        except Exception as e:
            print(f"查询商品上家状态异常: {str(e)}")
            self.progress_updated.emit(f"查询上家状态异常: {str(e)}")
            return 0

    def query_supplier_status_for_shop_immediate(self, products, shop_name):
        """立即查询店铺商品的上家状态并更新表格显示"""
        try:
            from tool.阿里巴巴接口 import AlibabaAPI
            import json
            import time
            import os
            from concurrent.futures import ThreadPoolExecutor, as_completed

            # 构建商品查询列表 - 基于传入的products参数
            products_to_query = []

            print(f"传入商品数: {len(products)}")

            # 统计上家信息来源和过滤情况
            extract_count = 0
            no_supplier_count = 0
            skipped_count = 0  # 统计被跳过的商品数量

            for i, product in enumerate(products):
                # 从商品数据中获取商品ID
                product_id = product.get('itemId', '') if isinstance(product, dict) else ''
                if not product_id:
                    skipped_count += 1
                    print(f"⚠️ 跳过第{i+1}个商品：缺少itemId，商品数据: {product}")
                    continue

                # 尝试从商品数据中提取上家ID（按店铺读取）
                supplier_id = self.extract_supplier_id_from_product(product, shop_name)
                if supplier_id:
                    extract_count += 1
                else:
                    no_supplier_count += 1

                # 获取商品标题
                title = product.get('title', '') if isinstance(product, dict) else ''

                products_to_query.append({
                    'row': i,  # 使用索引作为行号
                    'product_id': str(product_id),
                    'supplier_id': supplier_id or '',
                    'title': title
                })

            if not products_to_query:
                self.progress_updated.emit(f"店铺 {shop_name} 中没有找到有效商品")
                return 0

            print(f"商品过滤统计: 传入 {len(products)} 个 → 实际查询 {len(products_to_query)} 个 (跳过 {skipped_count} 个)")
            print(f"上家信息来源统计: 提取成功 {extract_count} 个, 无上家 {no_supplier_count} 个")
            if skipped_count > 0:
                print(f"⚠️ 警告: 有 {skipped_count} 个商品因缺少itemId被跳过，这可能导致商品数量不匹配")
            self.progress_updated.emit(f"正在查询店铺 {shop_name} 的 {len(products_to_query)} 个商品状态...")

            # 读取阿里巴巴分销API配置
            config_path = self.get_config_path("config.json")
            if not os.path.exists(config_path):
                self.progress_updated.emit("错误: 未找到配置文件")
                return 0

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 获取阿里巴巴分销API配置
            alibaba_fenxiao_config = config_data.get('alibabafenxiao', {})
            if not alibaba_fenxiao_config:
                self.progress_updated.emit("错误: 未找到阿里巴巴分销API配置")
                return 0

            app_key = alibaba_fenxiao_config.get('app_key')
            app_secret = alibaba_fenxiao_config.get('app_secret')
            access_token = alibaba_fenxiao_config.get('access_token')

            if not app_key or not app_secret or not access_token:
                self.progress_updated.emit("错误: 阿里巴巴分销API配置不完整")
                return 0

            # 创建阿里巴巴API实例
            api = AlibabaAPI(app_key=app_key, app_secret=app_secret, access_token=access_token, api_type='fenxiao')

            success_count = 0

            # 定义查询单个商品的函数
            def query_single_product_immediate(product_info):
                """查询单个商品的上家状态并立即更新表格"""
                supplier_id = product_info['supplier_id']
                product_id = product_info['product_id']
                row = product_info['row']  # 保留行号用于日志输出

                try:
                    # 检查是否有上家信息
                    if not supplier_id:
                        print(f"商品 {product_id} (第{row+1}行) 没有上家信息")
                        # 返回商品ID而不是行号，让调用者根据商品ID找到正确的表格行
                        return (product_id, "无上家信息", "#A8A8A8", True, False, "", "")

                    print(f"查询商品 {product_id} 的上家 {supplier_id} 状态")

                    # 调用阿里巴巴分销商品详情接口
                    result = api.get_fenxiao_product_info(supplier_id)

                    # 增加延时确保API稳定性，避免端口耗尽
                    time.sleep(0.5)

                    # 判断查询结果并确定状态文本和颜色
                    if result.get('success'):
                        raw_data = result.get('raw_data', {})
                        product_info_data = result.get('product_info', {})

                        product_status = product_info_data.get('status', '') if isinstance(product_info_data, dict) else ''
                        api_success = raw_data.get('success') if isinstance(raw_data, dict) else None
                        error_code = raw_data.get('errorCode', '') if isinstance(raw_data, dict) else ''
                        error_msg = raw_data.get('errorMsg', '') if isinstance(raw_data, dict) else ''

                        if not product_status and isinstance(raw_data, dict):
                            product_info_raw = raw_data.get('productInfo', {})
                            if isinstance(product_info_raw, dict):
                                product_status = product_info_raw.get('status', '')

                        # 提取并保存上家名称（supplier_login_id）
                        supplier_name = ''
                        if isinstance(product_info_data, dict):
                            supplier_name = product_info_data.get('supplier_login_id', '') or product_info_data.get('supplier_name', '')

                        # 如果从处理过的数据中没有获取到，尝试从原始数据获取
                        if not supplier_name and isinstance(raw_data, dict):
                            product_info_raw = raw_data.get('productInfo', {})
                            if isinstance(product_info_raw, dict):
                                supplier_name = product_info_raw.get('supplierLoginId', '') or product_info_raw.get('supplierName', '')

                        # 如果成功获取到上家名称，更新supplier_data
                        if supplier_name and hasattr(self.product_manager, 'supplier_data'):
                            # 调试：检查保存前的状态
                            print(f"  调试 - 准备保存上家名称: product_id={product_id}, supplier_name='{supplier_name}', supplier_id={supplier_id}")
                            print(f"  调试 - product_manager.supplier_data类型: {type(self.product_manager.supplier_data)}")
                            print(f"  调试 - product_manager.supplier_data长度: {len(self.product_manager.supplier_data)}")

                            # 更新supplier_data为新格式（包含supplier_id和supplier_name）
                            current_supplier_data = self.product_manager.supplier_data.get(product_id, {})
                            print(f"  调试 - 当前商品的supplier_data: {current_supplier_data}")

                            if isinstance(current_supplier_data, dict):
                                # 如果是空字典，需要先添加supplier_id
                                if not current_supplier_data:
                                    current_supplier_data['supplier_id'] = supplier_id
                                current_supplier_data['supplier_name'] = supplier_name
                                # 重要：将修改后的字典重新赋值给supplier_data
                                self.product_manager.supplier_data[product_id] = current_supplier_data
                                print(f"  调试 - 已更新现有字典记录")
                            else:
                                # 旧格式转换为新格式
                                self.product_manager.supplier_data[product_id] = {
                                    'supplier_id': str(current_supplier_data) if current_supplier_data else supplier_id,
                                    'supplier_name': supplier_name
                                }
                                print(f"  调试 - 已创建新字典记录")

                            # 验证保存结果
                            saved_data = self.product_manager.supplier_data.get(product_id, {})
                            print(f"  调试 - 保存后验证: {saved_data}")
                            print(f"  → 已保存上家名称: {supplier_name}")
                        elif supplier_name:
                            print(f"  ⚠️ 有上家名称但无法保存: hasattr(self.product_manager, 'supplier_data')={hasattr(self.product_manager, 'supplier_data')}")
                        else:
                            print(f"  ⚠️ 未获取到上家名称")

                        # 判断商品状态和颜色
                        needs_auto_delist = False  # 是否需要自动下架
                        auto_delist_reason = ""    # 自动下架原因

                        if error_code == '400_3' or '已下架' in str(error_msg):
                            status_text = "已下架"
                            status_color = "#FF6B6B"  # 红色
                            needs_auto_delist = True
                            auto_delist_reason = "上家商品已下架"
                        elif error_code == '400_2' or '不存在' in str(error_msg):
                            status_text = "不存在"
                            status_color = "#FF6B6B"  # 红色
                            needs_auto_delist = True
                            auto_delist_reason = "上家商品不存在"
                        elif api_success is True and product_status == "published":
                            status_text = "在线"
                            status_color = "#4ECDC4"  # 绿色
                        elif product_status in ["expired", "auto expired", "member expired"]:
                            status_text = "已过期"
                            status_color = "#FFE66D"  # 黄色
                            needs_auto_delist = True
                            auto_delist_reason = "上家商品已过期"
                        elif product_status in ["deleted", "member deleted"]:
                            status_text = "已删除"
                            status_color = "#FF6B6B"  # 红色
                            needs_auto_delist = True
                            auto_delist_reason = "上家商品已删除"
                        elif product_status == "auditing":
                            status_text = "审核中"
                            status_color = "#95E1D3"  # 浅绿色
                        elif product_status == "untread":
                            status_text = "审核未通过"
                            status_color = "#FFE66D"  # 黄色
                            needs_auto_delist = True
                            auto_delist_reason = "上家商品审核未通过"
                        else:
                            status_text = product_status or "状态未知"
                            status_color = "#A8A8A8"  # 灰色

                        print(f"  → 商品状态: {status_text}")

                        # 发送信号更新表格中的这一行
                        # 这里需要通过信号机制更新主界面的表格
                        # 由于我们在线程中，需要通过信号发送更新请求
                        # 返回商品ID而不是行号，让调用者根据商品ID找到正确的表格行
                        return (product_id, status_text, status_color, True, needs_auto_delist, auto_delist_reason, supplier_name)

                    else:
                        print(f"  → API调用失败")
                        return (product_id, "查询失败", "#FF6B6B", False, False, "", "")

                except Exception as e:
                    print(f"查询商品 {product_id} 异常: {str(e)}")
                    return (product_id, "查询异常", "#FF6B6B", False, False, "", "")

            # 使用线程池并发查询，但每个结果立即处理
            completed_count = 0
            total_count = len(products_to_query)

            print(f"开始并发查询 {total_count} 个商品，使用100个线程")

            # 使用线程池并发查询
            with ThreadPoolExecutor(max_workers=50) as executor:
                # 提交所有任务并记录
                submitted_futures = []
                future_to_product = {}

                for product_info in products_to_query:
                    future = executor.submit(query_single_product_immediate, product_info)
                    submitted_futures.append(future)
                    future_to_product[future] = product_info

                print(f"已提交 {len(submitted_futures)} 个查询任务")

                # 创建一个集合来跟踪已处理的future
                processed_futures = set()

                # 使用as_completed处理完成的任务
                for future in as_completed(submitted_futures):
                    if self.should_stop:
                        break

                    # 标记这个future已被处理
                    processed_futures.add(future)

                    try:
                        product_id, status_text, status_color, is_success, needs_auto_delist, auto_delist_reason, supplier_name = future.result()

                        # 获取对应的商品信息
                        product_info = future_to_product[future]

                        # 根据商品ID找到对应的表格行，而不是使用查询时的行号
                        actual_row = self.find_table_row_by_product_id(product_id)

                        if actual_row == -1:
                            print(f"⚠️ 未找到商品 {product_id} 对应的表格行")
                            continue

                        if is_success:
                            success_count += 1

                        # 更新完成计数和进度
                        completed_count += 1
                        # 进度计算：50% + (查询进度 * 50%)，确保进度从50%开始到100%结束
                        query_progress = completed_count / total_count
                        progress_percent = int(50 + query_progress * 50)
                        progress_msg = f"正在查询店铺 {shop_name} 上家状态: {completed_count}/{total_count}"
                        self.progress_updated.emit(f"{progress_msg}|{progress_percent}")
                        print(f"🔍 [DEBUG] 查询进度更新: {completed_count}/{total_count} -> {progress_percent}%")

                        # 如果需要自动下架，收集商品信息
                        if needs_auto_delist and auto_delist_reason:
                            auto_delist_item = {
                                'product_id': product_id,
                                'row': actual_row,  # 使用实际的表格行号
                                'status': status_text,
                                'reason': auto_delist_reason,
                                'shop_name': shop_name,
                                'title': product_info['title']
                            }
                            self.all_products_to_auto_delist.append(auto_delist_item)
                            print(f"收集自动下架商品: {product_id} - {auto_delist_reason}")

                        # 发送信号更新表格中的这一行 - 使用实际的表格行号
                        self.row_status_updated.emit(actual_row, status_text, status_color)

                        # 总是发送上家名称更新信号，确保一对一显示
                        # 如果没有上家名称就传空字符串，清空表格中的旧数据
                        self.supplier_name_updated.emit(actual_row, supplier_name if supplier_name else "")

                        print(f"商品 {product_id} -> 表格第 {actual_row} 行: {status_text}")

                    except Exception as e:
                        print(f"查询任务异常: {str(e)}")

                        # 获取对应的商品信息，确保UI更新
                        try:
                            product_info = future_to_product[future]
                            row = product_info['row']
                            # 更新UI显示查询失败状态
                            self.row_status_updated.emit(row, "查询异常", "#FF6B6B")
                            print(f"行 {row}: 查询异常")
                        except Exception as ui_error:
                            print(f"更新UI时出错: {str(ui_error)}")

                        # 即使出错也要更新进度
                        completed_count += 1
                        # 进度计算：50% + (查询进度 * 50%)，确保进度从50%开始到100%结束
                        query_progress = completed_count / total_count
                        progress_percent = int(50 + query_progress * 50)
                        progress_msg = f"正在查询店铺 {shop_name} 上家状态: {completed_count}/{total_count}"
                        self.progress_updated.emit(f"{progress_msg}|{progress_percent}")
                        print(f"🔍 [DEBUG] 查询进度更新(异常): {completed_count}/{total_count} -> {progress_percent}%")

            # 验证所有任务都已完成
            print(f"查询统计: 提交任务 {len(submitted_futures)}, as_completed处理 {len(processed_futures)}, 完成任务 {completed_count}, 成功任务 {success_count}")

            # 检查是否有未被as_completed处理的future
            unprocessed_futures = set(submitted_futures) - processed_futures
            if unprocessed_futures:
                print(f"⚠️ 发现 {len(unprocessed_futures)} 个未被as_completed处理的任务！")

                # 强制处理这些未处理的任务
                for future in unprocessed_futures:
                    try:
                        product_info = future_to_product[future]
                        row = product_info['row']

                        if future.done():
                            # 任务已完成但未被as_completed处理
                            try:
                                row, status_text, status_color, is_success, needs_auto_delist, auto_delist_reason, supplier_name = future.result()
                                print(f"强制处理已完成任务: 行 {row} - {status_text}")
                                self.row_status_updated.emit(row, status_text, status_color)

                                # 总是发送上家名称更新信号，确保一对一显示
                                self.supplier_name_updated.emit(row, supplier_name if supplier_name else "")

                                # 如果需要自动下架，收集商品信息
                                if needs_auto_delist and auto_delist_reason:
                                    auto_delist_item = {
                                        'product_id': product_info['product_id'],
                                        'row': row,
                                        'status': status_text,
                                        'reason': auto_delist_reason,
                                        'shop_name': shop_name,
                                        'title': product_info['title']
                                    }
                                    self.all_products_to_auto_delist.append(auto_delist_item)
                                    print(f"收集自动下架商品: {product_info['product_id']} - {auto_delist_reason}")

                                if is_success:
                                    success_count += 1
                                completed_count += 1
                            except Exception as result_error:
                                print(f"获取任务结果失败: 行 {row}, 错误: {str(result_error)}")
                                self.row_status_updated.emit(row, "结果异常", "#FF6B6B")
                                completed_count += 1
                        else:
                            # 任务未完成，标记为超时
                            print(f"强制更新未完成任务: 行 {row}")
                            self.row_status_updated.emit(row, "查询超时", "#FFA500")
                            completed_count += 1

                    except Exception as e:
                        print(f"强制处理任务时出错: {str(e)}")

            # 最终检查是否还有未完成的任务
            if completed_count < len(submitted_futures):
                print(f"⚠️ 警告: 仍有 {len(submitted_futures) - completed_count} 个任务未完成！")

                # 最后的兜底处理
                for i, future in enumerate(submitted_futures):
                    if not future.done():
                        try:
                            product_info = future_to_product[future]
                            row = product_info['row']
                            print(f"兜底处理未完成任务: 行 {row}")
                            self.row_status_updated.emit(row, "查询超时", "#FFA500")
                        except Exception as e:
                            print(f"兜底处理任务 {i} 时出错: {str(e)}")

            self.progress_updated.emit(f"店铺 {shop_name} 上家状态查询完成: 成功 {success_count}/{len(products_to_query)}|100")

            # 检查当前店铺是否有需要自动下架的商品
            shop_auto_delist_products = [item for item in self.all_products_to_auto_delist if item['shop_name'] == shop_name]
            if shop_auto_delist_products:
                print(f"店铺 {shop_name} 发现 {len(shop_auto_delist_products)} 个商品需要自动下架")
                # 设置当前处理的店铺
                self.current_shop_processing = shop_name
                # 发送单个店铺自动下架需求信号，立即处理该店铺的自动下架
                self.shop_auto_delist_required.emit(shop_name, shop_auto_delist_products)
                # 等待该店铺的自动下架操作完成
                self.wait_for_auto_delist_completion(shop_name)
            else:
                print(f"店铺 {shop_name} 没有需要自动下架的商品")

            # 查询完成后，保存更新的上家信息（包括上家名称）到数据库
            if self.product_manager and hasattr(self.product_manager, 'save_supplier_data_to_db'):
                try:
                    # 调试：检查内存中的supplier_data格式
                    if hasattr(self.product_manager, 'supplier_data'):
                        sample_data = dict(list(self.product_manager.supplier_data.items())[:3])
                        print(f"调试：保存前supplier_data样本: {sample_data}")

                    self.product_manager.save_supplier_data_to_db(shop_name)
                    print(f"店铺 {shop_name} 上家信息（包括上家名称）已保存到数据库")
                except Exception as save_error:
                    print(f"保存店铺 {shop_name} 上家信息到数据库失败: {str(save_error)}")

            return success_count

        except Exception as e:
            print(f"立即查询商品上家状态异常: {str(e)}")
            self.progress_updated.emit(f"查询上家状态异常: {str(e)}")
            return 0

    def extract_supplier_id_from_product(self, product, shop_name=None):
        """从商品数据中提取上家ID - 优先从上家缓存获取"""
        try:
            # 获取商品ID
            product_id = product.get('itemId', '') if isinstance(product, dict) else ''
            if not product_id:
                return None

            # 如果没有传入店铺名，尝试从商品数据中获取
            if not shop_name and isinstance(product, dict):
                shop_name = product.get('shop_name') or product.get('_shop_name')
                if shop_name:
                    # 清理店铺名称
                    if '(' in shop_name and shop_name.endswith(')'):
                        shop_name = shop_name.split('(')[0].strip()
                    elif '（' in shop_name and shop_name.endswith('）'):
                        shop_name = shop_name.split('（')[0].strip()

            # 方法1：从上家缓存中获取（优先，按店铺读取）
            supplier_id = self.get_supplier_id_from_cache(str(product_id), shop_name)
            if supplier_id:
                return supplier_id

            # 方法2：从商品数据结构中获取（备用）
            if isinstance(product, dict):
                # 检查常见的上家ID字段
                supplier_id = product.get('supplierId') or product.get('supplier_id')

                # 如果没有直接的上家ID，尝试从其他字段获取
                if not supplier_id:
                    # 从商品来源信息获取
                    source_info = product.get('sourceInfo', {})
                    if isinstance(source_info, dict):
                        supplier_id = source_info.get('supplierId') or source_info.get('supplier_id')

                # 从商品扩展信息获取
                if not supplier_id:
                    ext_info = product.get('extInfo', {})
                    if isinstance(ext_info, dict):
                        supplier_id = ext_info.get('supplierId') or ext_info.get('supplier_id')

            # 清理和验证上家ID
            if supplier_id:
                supplier_id = str(supplier_id).strip()
                # 只要不为空就是有效的上家ID
                if supplier_id:
                    return supplier_id

            return None

        except Exception as e:
            print(f"提取上家ID异常: {str(e)}")
            return None

    def get_supplier_id_from_cache(self, product_id, shop_name=None):
        """从店铺上家缓存中获取商品的上家ID（支持SQLite和JSON）"""
        try:
            import sqlite3
            import json

            # 如果没有传入店铺名，尝试从内存缓存获取
            if not shop_name and hasattr(self, 'supplier_cache') and self.supplier_cache:
                supplier_data = self.supplier_cache.get(str(product_id))
                if supplier_data:
                    # 兼容旧格式（只有supplier_id）和新格式（包含supplier_name）
                    if isinstance(supplier_data, dict):
                        supplier_id = supplier_data.get('supplier_id', '')
                    else:
                        # 旧格式，只有supplier_id
                        supplier_id = str(supplier_data)

                    supplier_id = supplier_id.strip()
                    if supplier_id:
                        return supplier_id

            # 从店铺文件读取
            if shop_name:
                supplier_dir = get_config_path("上家信息")

                # 优先尝试SQLite数据库
                db_file_path = os.path.join(supplier_dir, f"{shop_name}.db")
                if os.path.exists(db_file_path):
                    try:
                        conn = sqlite3.connect(db_file_path)
                        cursor = conn.cursor()
                        # 查询时同时获取supplier_id和supplier_name，但这个方法只返回supplier_id
                        cursor.execute('SELECT supplier_id, supplier_name FROM supplier_info WHERE product_id = ?', (str(product_id),))
                        result = cursor.fetchone()
                        conn.close()

                        if result:
                            supplier_id = str(result[0]).strip() if result[0] else ''
                            if supplier_id:
                                return supplier_id
                    except Exception as e:
                        print(f"读取店铺 {shop_name} SQLite缓存失败: {str(e)}")

                # 兼容旧的JSON文件
                json_file_path = os.path.join(supplier_dir, f"{shop_name}.json")
                if os.path.exists(json_file_path):
                    try:
                        with open(json_file_path, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            if content:
                                supplier_data = json.loads(content)
                                supplier_id = supplier_data.get(str(product_id))
                                if supplier_id:
                                    supplier_id = str(supplier_id).strip()
                                    if supplier_id:
                                        return supplier_id
                    except Exception as e:
                        print(f"读取店铺 {shop_name} JSON缓存失败: {str(e)}")

            return None

        except Exception as e:
            print(f"从缓存获取上家ID异常: {str(e)}")
            return None

    def get_config_path(self, filename):
        """获取配置文件路径"""
        try:
            # 使用全局的get_config_path函数，确保打包后路径正确
            return get_config_path(filename)
        except Exception as e:
            print(f"获取配置文件路径异常: {str(e)}")
            return filename

    def load_config_data(self):
        """加载配置数据"""
        try:
            import os
            import json

            # 获取配置文件路径 - 使用统一的配置路径获取方法
            config_path = get_config_path('账号管理.json')

            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}")
                return None

            with open(config_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 检查数据结构
            if isinstance(data, dict) and 'data' in data:
                return data['data']
            elif isinstance(data, list):
                return data
            else:
                return None

        except Exception as e:
            print(f"加载配置数据异常: {str(e)}")
            return None

    def find_shop_info(self, config_data, shop_name):
        """查找店铺信息"""
        try:
            for account in config_data:
                if isinstance(account, dict):
                    account_shop_name = account.get('店铺名称', '').strip()
                    if account_shop_name == shop_name:
                        return account
            return None

        except Exception as e:
            print(f"查找店铺信息异常: {str(e)}")
            return None

class SupplierStatusThread(QThread):
    """上家商品状态查询任务"""
    progress_updated = pyqtSignal(str)  # 进度更新信号
    row_updated = pyqtSignal(int, str, str)  # 行更新信号 (行号, 状态文本, 状态颜色)
    supplier_name_updated = pyqtSignal(int, str)  # 上家名称更新信号 (行号, 上家名称)
    query_completed = pyqtSignal(int, int)  # 查询完成信号 (成功数, 总数)
    auto_delist_required = pyqtSignal(list)  # 自动下架需求信号 (需要下架的商品列表)
    query_completed_with_auto_delist = pyqtSignal(int, int, int)  # 查询完成且有自动下架信号 (成功数, 总数, 待下架数)

    def __init__(self, products_to_query, product_manager=None, enable_auto_delist=True):
        super().__init__()
        self.products_to_query = products_to_query
        self.product_manager = product_manager  # 添加product_manager引用
        self.enable_auto_delist = enable_auto_delist  # 是否启用自动下架功能
        self.should_stop = False
        self.success_count = 0
        self.products_to_auto_delist = []  # 需要自动下架的商品列表

    def stop(self):
        """停止查询"""
        self.should_stop = True

    def run(self):
        """执行上家商品状态查询"""
        try:
            from tool.阿里巴巴接口 import AlibabaAPI
            import json
            import time
            import threading
            from concurrent.futures import ThreadPoolExecutor, as_completed

            # 读取阿里巴巴分销API配置
            config_path = get_config_path("config.json")
            if not os.path.exists(config_path):
                self.progress_updated.emit("错误: 未找到配置文件")
                return

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 获取阿里巴巴分销API配置
            alibaba_fenxiao_config = config_data.get('alibabafenxiao', {})
            if not alibaba_fenxiao_config:
                self.progress_updated.emit("错误: 未找到阿里巴巴分销API配置")
                return

            app_key = alibaba_fenxiao_config.get('app_key')
            app_secret = alibaba_fenxiao_config.get('app_secret')
            access_token = alibaba_fenxiao_config.get('access_token')

            if not app_key or not app_secret or not access_token:
                self.progress_updated.emit("错误: 阿里巴巴分销API配置不完整")
                return

            # 创建阿里巴巴API实例 - 使用分销API专用配置
            print(f"使用分销API配置: app_key={app_key}, access_token={access_token[:10]}...")
            api = AlibabaAPI(app_key=app_key, app_secret=app_secret, access_token=access_token, api_type='fenxiao')
            
            # 验证API配置
            if not hasattr(api, 'app_key') or not api.app_key:
                self.progress_updated.emit("错误: API实例初始化失败")
                return
            
            print(f"API实例初始化成功，app_key: {api.app_key}")

                        # 并发查询上家商品状态
            def query_single_product(product):
                """查询单个商品的上家状态"""
                row = product['row']
                product_id = product['product_id']  # 保留用于调试
                supplier_id = product['supplier_id']

                try:
                    print(f"查询商品 {product_id} 的上家 {supplier_id} 状态")

                    # 调用阿里巴巴分销商品详情接口
                    result = api.get_fenxiao_product_info(supplier_id)
                    
                    # 增加延时确保API稳定性，避免连接池过载
                    time.sleep(0.1)  # 100毫秒延时，平衡性能和稳定性
                    
                    print(f"API调用结果: success={result.get('success')}")
                    if not result.get('success'):
                        print(f"API调用失败详情: {result}")

                    # 判断查询结果
                    if result.get('success'):
                        print(f"查询商品 {product_id}，API调用成功")

                        # 获取API返回的原始数据和处理过的数据
                        raw_data = result.get('raw_data', {})
                        product_info = result.get('product_info', {})

                        # 优先从处理过的product_info获取状态信息
                        product_status = product_info.get('status', '') if isinstance(product_info, dict) else ''

                        # 从原始数据获取API级别的错误信息
                        api_success = raw_data.get('success') if isinstance(raw_data, dict) else None
                        error_code = raw_data.get('errorCode', '') if isinstance(raw_data, dict) else ''
                        error_msg = raw_data.get('errorMsg', '') if isinstance(raw_data, dict) else ''

                        # 如果处理过的数据中没有状态，则从原始数据中获取
                        if not product_status and isinstance(raw_data, dict):
                            product_info_raw = raw_data.get('productInfo', {})
                            if isinstance(product_info_raw, dict):
                                product_status = product_info_raw.get('status', '')

                        # 提取并保存上家名称（supplier_login_id）
                        supplier_name = ''
                        if isinstance(product_info, dict):
                            supplier_name = product_info.get('supplier_login_id', '') or product_info.get('supplier_name', '')

                        # 如果从处理过的数据中没有获取到，尝试从原始数据获取
                        if not supplier_name and isinstance(raw_data, dict):
                            product_info_raw = raw_data.get('productInfo', {})
                            if isinstance(product_info_raw, dict):
                                supplier_name = product_info_raw.get('supplierLoginId', '') or product_info_raw.get('supplierName', '')

                        # 调试：检查上家名称获取情况
                        print(f"  调试 - supplier_name: '{supplier_name}', product_id: {product_id}, supplier_id: {supplier_id}")
                        print(f"  调试 - hasattr(self.product_manager, 'supplier_data'): {hasattr(self.product_manager, 'supplier_data')}")
                        if hasattr(self.product_manager, 'supplier_data'):
                            print(f"  调试 - supplier_data类型: {type(self.product_manager.supplier_data)}, 长度: {len(self.product_manager.supplier_data)}")

                        # 如果成功获取到上家名称，更新supplier_data
                        if supplier_name and hasattr(self.product_manager, 'supplier_data'):
                            # 确保supplier_data存在
                            if not hasattr(self.product_manager, 'supplier_data') or self.product_manager.supplier_data is None:
                                self.product_manager.supplier_data = {}

                            # 更新supplier_data为新格式（包含supplier_id和supplier_name）
                            current_supplier_data = self.product_manager.supplier_data.get(product_id, {})
                            if isinstance(current_supplier_data, dict):
                                current_supplier_data['supplier_name'] = supplier_name
                                print(f"  → 已更新现有记录的上家名称: {supplier_name}")
                            else:
                                # 旧格式转换为新格式
                                self.product_manager.supplier_data[product_id] = {
                                    'supplier_id': str(current_supplier_data) if current_supplier_data else supplier_id,
                                    'supplier_name': supplier_name
                                }
                                print(f"  → 已创建新记录并保存上家名称: {supplier_name}")
                        elif supplier_name:
                            print(f"  ⚠️ 有上家名称但无法保存: supplier_data不存在")
                        else:
                            print(f"  ⚠️ 未获取到上家名称")
                        
                        # 调试信息
                        print(f"  调试 - api_success: {api_success}, error_code: '{error_code}', product_status: '{product_status}'")
                        print(f"  调试 - product_info有效: {bool(product_info)}, raw_data有效: {bool(raw_data)}")
                        
                        # 判断商品状态 - 基于1688官方API文档的状态枚举
                        if error_code == '400_3' or '已下架' in str(error_msg):
                            status_text = "已下架"
                            status_color = "#FF0000"
                            print(f"  → 商品已下架 (错误码: {error_code})")
                            # 只有启用自动下架功能时才添加到自动下架列表
                            if self.enable_auto_delist:
                                self.products_to_auto_delist.append({
                                    'product_id': product_id,
                                    'supplier_id': supplier_id,
                                    'row': row,
                                    'status': status_text,
                                    'reason': f"供应商商品已下架 (错误码: {error_code})"
                                })
                        elif error_code == '400_2' or '不存在' in str(error_msg):
                            status_text = "不存在"
                            status_color = "#FF0000"
                            print(f"  → 商品不存在 (错误码: {error_code})")
                            # 只有启用自动下架功能时才添加到自动下架列表
                            if self.enable_auto_delist:
                                self.products_to_auto_delist.append({
                                    'product_id': product_id,
                                    'supplier_id': supplier_id,
                                    'row': row,
                                    'status': status_text,
                                    'reason': f"供应商商品不存在 (错误码: {error_code})"
                                })
                        elif error_code and api_success is False:
                            status_text = f"异常({error_code})"
                            status_color = "#FF8C00"
                            print(f"  → API异常 (错误码: {error_code})")
                        elif api_success is True and product_status:
                            # 根据1688官方文档的状态枚举判断
                            if product_status == "published":
                                status_text = "在线"
                                status_color = "#008000"
                                print(f"  → 商品在线")
                            elif product_status in ["expired", "auto expired", "member expired"]:
                                status_text = "已过期"
                                status_color = "#FF0000"
                                print(f"  → 商品已过期: {product_status}")
                                # 只有启用自动下架功能时才添加到自动下架列表
                                if self.enable_auto_delist:
                                    self.products_to_auto_delist.append({
                                        'product_id': product_id,
                                        'supplier_id': supplier_id,
                                        'row': row,
                                        'status': status_text,
                                        'reason': f"供应商商品已过期: {product_status}"
                                    })
                            elif product_status in ["deleted", "member deleted"]:
                                status_text = "已删除"
                                status_color = "#FF0000"
                                print(f"  → 商品已删除: {product_status}")
                                # 只有启用自动下架功能时才添加到自动下架列表
                                if self.enable_auto_delist:
                                    self.products_to_auto_delist.append({
                                        'product_id': product_id,
                                        'supplier_id': supplier_id,
                                        'row': row,
                                        'status': status_text,
                                        'reason': f"供应商商品已删除: {product_status}"
                                    })
                            elif product_status == "auditing":
                                status_text = "审核中"
                                status_color = "#FF8C00"
                                print(f"  → 商品审核中")
                            elif product_status == "untread":
                                status_text = "审核未通过"
                                status_color = "#FF0000"
                                print(f"  → 商品审核未通过")
                            elif product_status == "TBD":
                                status_text = "待删除"
                                status_color = "#FF8C00"
                                print(f"  → 商品待删除")
                            else:
                                status_text = product_status
                                status_color = "#FF8C00"
                                print(f"  → 其他状态: {product_status}")
                        elif api_success is True:
                            status_text = "状态未知"
                            status_color = "#FF8C00"
                            print(f"  → 无法获取商品状态")
                        else:
                            status_text = "查询失败"
                            status_color = "#FF0000"
                            print(f"  → API调用失败")
                        # 更新成功计数（统计在外层completed_tasks中进行）
                        self.success_count += 1

                        return (row, status_text, status_color, True, supplier_name)
                    else:
                        # API调用失败
                        error_msg = result.get('error_message', '查询失败')
                        error_code = result.get('error_code', 'UNKNOWN')
                        print(f"API调用失败: {error_code} - {error_msg}")
                        
                        # 针对不同错误类型给出不同提示
                        if 'HTTP_400' in error_code:
                            status_text = "配置错误"
                            error_detail = "API配置或权限问题"
                        elif 'INVALID_API_KEY' in error_code:
                            status_text = "密钥错误"
                            error_detail = "AppKey无效"
                        elif 'access_token' in error_msg.lower():
                            status_text = "授权失效"
                            error_detail = "access_token过期"
                        else:
                            status_text = "查询失败"
                            error_detail = error_msg

                        return (row, status_text, "#FF0000", False, '')

                except Exception as e:
                    print(f"查询商品 {supplier_id} 状态异常: {str(e)}")
                    import traceback
                    traceback.print_exc()

                    return (row, "查询异常", "#FF0000", False, '')

            # 使用线程池并发查询 - 减少并发数避免API限流
            total_count = len(self.products_to_query)
            self.progress_updated.emit(f"开始查询 {total_count} 个商品的上家状态...")

            # 平衡性能和稳定性，使用适中的并发数
            max_workers = min(50, total_count)  # 降低到50个并发线程，避免连接池过载和内存压力
            print(f"使用 {max_workers} 个并发线程查询")

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务并记录futures
                submitted_futures = []
                for product in self.products_to_query:
                    future = executor.submit(query_single_product, product)
                    submitted_futures.append(future)
                
                print(f"已提交 {len(submitted_futures)} 个查询任务")
                
                # 统计完成的任务数量
                completed_tasks = 0
                processed_futures = set()
                
                # 使用as_completed处理完成的任务，但不依赖它来判断所有任务是否完成
                for future in as_completed(submitted_futures):
                    if self.should_stop:
                        # 如果需要停止，取消未完成的任务
                        print("收到停止信号，取消剩余任务...")
                        for remaining_future in submitted_futures:
                            if not remaining_future.done():
                                remaining_future.cancel()
                        break

                    try:
                        row, status_text, status_color, success, supplier_name = future.result()

                        # 发送行更新信号，包含上家名称
                        self.row_updated.emit(row, status_text, status_color)

                        # 如果有上家名称，发送上家名称更新信号
                        if supplier_name:
                            self.supplier_name_updated.emit(row, supplier_name)
                        
                        # 标记此future已处理
                        processed_futures.add(future)
                        completed_tasks += 1

                        # 更新进度
                        progress_text = f"上家状态查询: {completed_tasks}/{total_count}"
                        self.progress_updated.emit(progress_text)

                        # 使用success变量避免IDE警告
                        if success:
                            pass  # 成功状态已在query_single_product中处理

                        # 移除结果处理的延时，提升性能
                        # 延时应该在API请求内部控制，而不是结果处理时

                    except Exception as e:
                        print(f"处理查询结果时出错: {str(e)}")
                        processed_futures.add(future)
                        completed_tasks += 1  # 即使出错也要计入完成数量
                
                # 确保所有任务都已完成（等待剩余的future完成）
                print(f"as_completed循环结束，已处理: {len(processed_futures)}/{len(submitted_futures)}")
                
                if not self.should_stop:
                    # 等待所有未处理的任务完成
                    remaining_futures = [f for f in submitted_futures if f not in processed_futures]
                    if remaining_futures:
                        print(f"等待剩余 {len(remaining_futures)} 个任务完成...")
                        for future in remaining_futures:
                            try:
                                # 等待任务完成，不需要处理结果（可能是重复的）
                                future.result(timeout=30)
                                completed_tasks += 1
                                progress_text = f"上家状态查询: {completed_tasks}/{total_count}"
                                self.progress_updated.emit(progress_text)
                            except Exception as e:
                                print(f"等待剩余任务完成时出错: {str(e)}")
                                completed_tasks += 1  # 计入完成数量
                
                print(f"所有查询任务已真正完成！实际完成: {completed_tasks}/{total_count}")

            # 只有在所有任务真正完成后才发送完成信号
            if not self.should_stop:  # 只有未被停止的情况下才发送完成信号
                # 根据是否启用自动下架功能和是否有自动下架需求，发送不同的完成信号
                if self.enable_auto_delist and self.products_to_auto_delist:
                    print(f"发现 {len(self.products_to_auto_delist)} 个商品需要自动下架")
                    # 发送自动下架需求信号
                    self.auto_delist_required.emit(self.products_to_auto_delist)
                    # 发送包含自动下架信息的完成信号
                    self.query_completed_with_auto_delist.emit(self.success_count, total_count, len(self.products_to_auto_delist))
                else:
                    # 发送普通完成信号
                    if not self.enable_auto_delist:
                        print(f"上家状态查询完成，自动下架功能已禁用")
                    self.query_completed.emit(self.success_count, total_count)
                
                # 查询完成后，保存更新的上家信息（包括上家名称）到数据库
                if self.product_manager and hasattr(self.product_manager, 'save_supplier_data_to_db'):
                    try:
                        # 获取当前表格中的店铺信息，按店铺分组保存
                        shops_to_save = set()
                        for product_info in self.products_to_query:
                            shop_name = product_info.get('shop_name', '')
                            if shop_name:
                                # 清理店铺名称（去除评分括号）
                                if '(' in shop_name and shop_name.endswith(')'):
                                    shop_name = shop_name.split('(')[0].strip()
                                elif '（' in shop_name and shop_name.endswith('）'):
                                    shop_name = shop_name.split('（')[0].strip()
                                shops_to_save.add(shop_name)

                        # 为每个店铺保存上家信息
                        for shop_name in shops_to_save:
                            try:
                                print(f"🔍 [DEBUG] 保存店铺 {shop_name} 的上家信息到数据库")
                                self.product_manager.save_supplier_data_to_db(shop_name)
                                print(f"店铺 {shop_name} 上家信息（包括上家名称）已保存到数据库")
                            except Exception as save_error:
                                print(f"保存店铺 {shop_name} 上家信息到数据库失败: {str(save_error)}")

                    except Exception as save_error:
                        print(f"保存上家信息到数据库失败: {str(save_error)}")
                        import traceback
                        traceback.print_exc()
                else:
                    print("⚠️ 警告：无法保存上家信息到数据库，缺少保存方法或product_manager")

                # 执行资源清理，避免内存积累导致闪退
                print("执行查询后资源清理...")
                try:
                    import gc
                    gc.collect()  # 强制垃圾回收
                    print("资源清理完成")
                except Exception as cleanup_error:
                    print(f"资源清理异常: {cleanup_error}")

        except Exception as e:
            print(f"上家状态查询异常: {str(e)}")
            import traceback
            traceback.print_exc()
            self.progress_updated.emit(f"查询异常: {str(e)}")



    def extract_numbers_from_text(self, text):
        """提取文本中的纯数字"""
        try:
            import re
            # 使用正则表达式提取所有数字
            numbers = re.findall(r'\d+', str(text))
            if numbers:
                # 将所有数字连接成一个完整的数字字符串
                result = ''.join(numbers)
                return result
            else:
                return text  # 如果没有数字，返回原始文本
        except Exception as e:
            print(f"提取数字时出错: {str(e)}")
            return text  # 出错时返回原始文本

class SupplierQueryThread(QThread):
    """上家查询任务"""
    progress_updated = pyqtSignal(str)  # 进度更新信号
    row_updated = pyqtSignal(int, str, str)  # 行更新信号 (行号, 上家信息, 类目信息)
    query_completed = pyqtSignal(int, int)  # 查询完成信号 (成功数, 总数)
    
    def __init__(self, checked_shops, product_table, product_manager=None):
        super().__init__()
        self.checked_shops = checked_shops
        self.product_table = product_table
        self.product_manager = product_manager  # 添加product_manager引用
        self.should_stop = False
        self.success_count = 0
        self.total_count = 0
        self.supplier_data = {}  # 存储商品ID和上家信息的映射
        
    def stop(self):
        """停止查询"""
        self.should_stop = True
        
    def run(self):
        """执行上家查询（逐个店铺查询）"""
        try:
            from tool.快手api import KuaishouAPI
            import json
            
            # 读取账号配置
            config_path = get_config_path("账号管理.json")
            if not os.path.exists(config_path):
                self.progress_updated.emit("错误: 未找到账号配置文件")
                return
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 获取商品总数
            self.total_count = self.product_table.rowCount()
            
            # 按店铺分组需要查询的商品（只查询上家列为空的）
            shop_products = {}
            total_empty_count = 0
            
            for row in range(self.total_count):
                if self.should_stop:
                    break
                
                # 检查上家列是否为空
                supplier_item = self.product_table.item(row, 14)  # 上家列
                if supplier_item and supplier_item.text().strip():
                    continue  # 已有上家信息，跳过
                
                # 获取商品ID和店铺信息
                product_id_item = self.product_table.item(row, 2)  # 商品ID列
                shop_name_item = self.product_table.item(row, 9)   # 店铺列
                
                if not product_id_item or not shop_name_item:
                    continue
                    
                product_id = product_id_item.text().strip()
                shop_name = shop_name_item.text().strip()
                
                # 清理店铺名称（去除评分括号）
                if '(' in shop_name and shop_name.endswith(')'):
                    shop_name = shop_name.split('(')[0].strip()
                
                # 按店铺分组
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                
                shop_products[shop_name].append({
                    'row': row,
                    'product_id': product_id
                })
                total_empty_count += 1
            
            if not shop_products:
                self.progress_updated.emit("没有需要查询上家信息的商品")
                return
            
            self.progress_updated.emit(f"找到 {len(shop_products)} 个店铺，共 {total_empty_count} 个商品需要查询上家信息")
            
            # 逐个店铺查询
            processed_count = 0
            for shop_index, (shop_name, products) in enumerate(shop_products.items(), 1):
                if self.should_stop:
                    break
                
                self.progress_updated.emit(f"正在查询第 {shop_index}/{len(shop_products)} 个店铺: {shop_name} ({len(products)} 个商品)")
                
                # 查找店铺配置
                shop_info = self.find_shop_info(config_data, shop_name)
                if not shop_info:
                    self.progress_updated.emit(f"店铺 {shop_name} 未找到配置信息，跳过")
                    continue
                    
                access_token = shop_info.get('accesstoken')
                if not access_token:
                    self.progress_updated.emit(f"店铺 {shop_name} 未找到访问令牌，跳过")
                    continue
                
                # 为当前店铺使用并发50线程查询商品
                try:
                    from concurrent.futures import ThreadPoolExecutor, as_completed
                    import threading
                    
                    # 线程安全的计数器和锁
                    shop_processed_count = 0
                    count_lock = threading.Lock()
                    
                    def query_single_product_for_shop(product):
                        """为当前店铺查询单个商品的上家信息"""
                        try:
                            row = product['row']
                            product_id = product['product_id']
                            
                            # 创建API实例
                            api = KuaishouAPI(access_token=access_token)
                            
                            # 调用商品详情接口
                            result = api.get_item_detail(int(product_id))
                            
                            if result.get('success'):
                                data = result.get('data', {})
                                sku_infos = data.get('skuInfos', [])

                                # 🔧 修复：使用商品复制.py中更好更准确的三种类目匹配方法
                                original_category_name = data.get('categoryName', '')
                                product_title = data.get('title', '')

                                # 使用商品复制.py中的三种匹配方法（更准确）
                                try:
                                    from 商品复制 import ProductCopyWindow
                                    # 创建一个临时实例来调用匹配方法
                                    temp_instance = ProductCopyWindow()
                                    match_result = temp_instance._sync_match_category_by_name(original_category_name, product_title)

                                    if match_result and match_result.get('found'):
                                        # 使用三种匹配方法的结果
                                        category_name = match_result.get('full_path', '')
                                        print(f"[读取商品] ✅ 三种匹配方法成功: '{product_title[:30]}...' -> {category_name}")
                                    else:
                                        # 匹配失败，类目字段为空
                                        category_name = ""
                                        print(f"[读取商品] ⚠️ 三种匹配方法失败: '{product_title[:30]}...'，类目字段保持为空")
                                except Exception as e:
                                    # 如果调用失败，回退到原始类目
                                    category_name = ""
                                    print(f"[读取商品] ❌ 调用三种匹配方法异常: {e}，类目字段保持为空")

                                # 获取第一个SKU的skuNick
                                if sku_infos:
                                    sku_nick = sku_infos[0].get('skuNick', '')
                                    if sku_nick:
                                        # 处理上家信息：提取纯数字
                                        processed_sku_nick = self.extract_numbers_only(sku_nick)

                                        # 线程安全地更新数据（存储为字典格式，包含类目信息）
                                        with count_lock:
                                            self.supplier_data[product_id] = {
                                                'supplier_id': processed_sku_nick,
                                                'supplier_name': '',  # 上家名称暂时为空，后续查询时填充
                                                'category_name': category_name
                                            }
                                            self.success_count += 1

                                        return (row, processed_sku_nick, category_name, True)
                                    else:
                                        return (row, '未找到上家信息', category_name, False)
                                else:
                                    return (row, '无SKU信息', category_name, False)
                            else:
                                return (row, '查询失败', '', False)

                        except Exception as e:
                            print(f"查询商品 {product_id} 详情失败: {str(e)}")
                            return (row, f'查询异常', '', False)
                    
                    # 使用线程池为当前店铺并发查询
                    with ThreadPoolExecutor(max_workers=50) as executor:
                        # 提交当前店铺的所有商品查询任务
                        future_to_product = {executor.submit(query_single_product_for_shop, product): product for product in products}
                        
                        # 处理完成的任务
                        for future in as_completed(future_to_product):
                            if self.should_stop:
                                break
                                
                            try:
                                row, result_text, category_text, success = future.result()

                                # 发送行更新信号（包含上家信息和类目信息）
                                self.row_updated.emit(row, result_text, category_text)
                                
                                # 更新进度
                                with count_lock:
                                    shop_processed_count += 1
                                    processed_count += 1
                                    
                                # 发送进度更新信号
                                progress_text = f"店铺 {shop_name} 并发查询: {shop_processed_count}/{len(products)}，总进度: {processed_count}/{total_empty_count}"
                                self.progress_updated.emit(progress_text)
                                
                            except Exception as e:
                                print(f"处理店铺 {shop_name} 查询结果时出错: {str(e)}")
                                with count_lock:
                                    shop_processed_count += 1
                                    processed_count += 1
                
                except Exception as e:
                    print(f"查询店铺 {shop_name} 时出错: {str(e)}")
                    self.progress_updated.emit(f"店铺 {shop_name} 查询出错: {str(e)}")

                # 每个店铺查询完成后立即保存该店铺的上家信息到SQLite数据库
                if self.product_manager and hasattr(self.product_manager, 'save_supplier_data_to_db'):
                    try:
                        # 将当前店铺的数据合并到product_manager的supplier_data中
                        if not hasattr(self.product_manager, 'supplier_data'):
                            self.product_manager.supplier_data = {}

                        # 合并当前店铺的数据（不覆盖，而是累积）
                        for product_id, supplier_info in self.supplier_data.items():
                            self.product_manager.supplier_data[product_id] = supplier_info

                        print(f"🔍 [DEBUG] 店铺 {shop_name} 查询到 {len(self.supplier_data)} 个商品的上家信息")
                        print(f"🔍 [DEBUG] product_manager.supplier_data 总数: {len(self.product_manager.supplier_data)}")

                        # 创建临时的supplier_data，只包含当前店铺的数据
                        temp_supplier_data = self.product_manager.supplier_data.copy()
                        self.product_manager.supplier_data = self.supplier_data

                        # 保存当前店铺的数据
                        self.product_manager.save_supplier_data_to_db(shop_name)
                        print(f"店铺 {shop_name} 上家信息已保存到SQLite数据库")

                        # 恢复完整的supplier_data
                        self.product_manager.supplier_data = temp_supplier_data

                    except Exception as save_error:
                        print(f"保存店铺 {shop_name} 上家信息失败: {str(save_error)}")
                        import traceback
                        traceback.print_exc()
                else:
                    print(f"警告：无法保存店铺 {shop_name} 的上家信息，缺少保存方法")

                # 店铺之间稍微停顿
                if shop_index < len(shop_products):
                    self.msleep(200)
            
            # 发送完成信号
            self.query_completed.emit(self.success_count, total_empty_count)

            # 所有店铺的上家信息已在各自查询完成后保存到SQLite数据库
            print("所有店铺上家信息查询完成，数据已保存到SQLite数据库")
            
        except Exception as e:
            print(f"上家查询异常: {str(e)}")
            self.progress_updated.emit(f"查询异常: {str(e)}")
            
    def find_shop_info(self, config_data, shop_name):
        """从配置数据中查找店铺信息"""
        try:
            print(f"正在查找店铺 {shop_name} 的配置信息...")
            if 'data' in config_data:
                print(f"配置文件中共有 {len(config_data['data'])} 个店铺配置")
                for i, shop_data in enumerate(config_data['data']):
                    config_shop_name = shop_data.get('店铺名称', '')
                    print(f"配置 {i+1}: 店铺名称='{config_shop_name}'")
                    if config_shop_name == shop_name:
                        print(f"找到匹配的店铺配置: {shop_name}")
                        return shop_data
                print(f"未找到店铺 {shop_name} 的配置")
            else:
                print("配置文件中没有'data'字段")
            return None
        except Exception as e:
            print(f"查找店铺信息时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def extract_numbers_only(self, text):
        """提取字符串中的纯数字"""
        try:
            import re
            # 使用正则表达式提取所有数字
            numbers = re.findall(r'\d+', str(text))
            if numbers:
                # 将所有数字连接成一个完整的数字字符串
                result = ''.join(numbers)
                print(f"上家信息处理: '{text}' -> '{result}'")
                return result
            else:
                print(f"上家信息中未找到数字: '{text}'")
                return text  # 如果没有数字，返回原始文本
        except Exception as e:
            print(f"处理上家信息时出错: {str(e)}")
            return text  # 出错时返回原始文本
    



class SupplierCacheLoadThread(QThread):
    """上家缓存加载线程 - 异步加载上家信息避免阻塞UI（支持按店铺加载）"""
    cache_loaded = pyqtSignal(dict)  # 缓存加载完成信号 (supplier_data)
    loading_failed = pyqtSignal(str)  # 加载失败信号 (error_message)

    def __init__(self, shop_name=None):
        super().__init__()
        self.shop_name = shop_name

    def run(self):
        """异步从SQLite数据库加载上家缓存数据（按店铺加载）"""
        try:
            import sqlite3

            supplier_data = {}

            # 从SQLite数据库读取
            if self.shop_name:
                supplier_dir = get_config_path("上家信息")
                if os.path.exists(supplier_dir):
                    db_file_path = os.path.join(supplier_dir, f"{self.shop_name}.db")

                    if os.path.exists(db_file_path):
                        try:
                            # 连接SQLite数据库
                            conn = sqlite3.connect(db_file_path)
                            cursor = conn.cursor()

                            # 查询所有上家信息，包含supplier_id、supplier_name和category_name
                            try:
                                # 尝试查询包含类目信息的完整数据
                                cursor.execute('SELECT product_id, supplier_id, supplier_name, category_name FROM supplier_info')
                                rows = cursor.fetchall()
                                has_category = True
                            except sqlite3.OperationalError:
                                # 如果没有category_name列，则只查询原有字段
                                cursor.execute('SELECT product_id, supplier_id, supplier_name FROM supplier_info')
                                rows = cursor.fetchall()
                                has_category = False

                            # 转换为字典格式，包含上家ID、上家名称和类目信息
                            for row in rows:
                                product_id = row[0]
                                supplier_id = row[1] if row[1] else ''
                                supplier_name = row[2] if len(row) > 2 and row[2] else ''
                                category_name = row[3] if has_category and len(row) > 3 and row[3] else ''

                                supplier_data[product_id] = {
                                    'supplier_id': supplier_id,
                                    'supplier_name': supplier_name,
                                    'category_name': category_name
                                }

                            conn.close()

                            # 统计有上家名称和类目信息的记录数量
                            name_count = sum(1 for data in supplier_data.values() if data.get('supplier_name', '').strip())
                            category_count = sum(1 for data in supplier_data.values() if data.get('category_name', '').strip())
                            print(f"从SQLite数据库异步加载上家信息: {db_file_path}，共 {len(supplier_data)} 条记录")
                            print(f"  其中 {name_count} 条有上家名称，{category_count} 条有类目信息")

                            # 调试：显示前几条记录的内容
                            if supplier_data:
                                sample_items = list(supplier_data.items())[:3]
                                for product_id, data in sample_items:
                                    print(f"  样本数据: {product_id} -> supplier_id='{data.get('supplier_id', '')}', supplier_name='{data.get('supplier_name', '')}', category_name='{data.get('category_name', '')}'")

                            self.cache_loaded.emit(supplier_data)
                            return

                        except Exception as e:
                            error_msg = f"读取店铺 {self.shop_name} SQLite数据库失败: {str(e)}"
                            print(error_msg)
                            self.loading_failed.emit(error_msg)
                            return

            # 如果没有找到SQLite数据库文件，返回空数据
            print(f"店铺 {self.shop_name} 没有SQLite数据库文件，跳过上家信息加载")
            self.cache_loaded.emit({})

        except Exception as e:
            error_msg = f"上家缓存加载异常: {str(e)}"
            print(error_msg)
            self.loading_failed.emit(error_msg)


class MissingSupplierQueryThread(QThread):
    """缺失上家信息查询线程 - 异步查询缺失的上家信息"""
    query_progress = pyqtSignal(str)  # 查询进度信号 (progress_message)
    query_completed = pyqtSignal(int, int)  # 查询完成信号 (queried_count, total_count)
    query_failed = pyqtSignal(str)  # 查询失败信号 (error_message)
    row_updated = pyqtSignal(int, str, str)  # 行更新信号 (row, supplier_info, category_info)

    def __init__(self, shop_name, missing_products, product_manager):
        super().__init__()
        self.shop_name = shop_name
        self.missing_products = missing_products
        self.product_manager = product_manager
        self.should_stop = False

    def run(self):
        """异步查询缺失的上家信息"""
        try:
            from concurrent.futures import ThreadPoolExecutor, as_completed
            import threading
            import json
            import re

            # 读取配置数据 - 使用正确的配置文件路径
            config_file_path = get_config_path("账号管理.json")
            if not os.path.exists(config_file_path):
                self.query_failed.emit("账号管理.json文件不存在")
                return

            with open(config_file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 查找店铺信息
            shop_info = self.product_manager.find_shop_info(config_data, self.shop_name)
            if not shop_info:
                self.query_failed.emit(f"未找到店铺 {self.shop_name} 的配置信息")
                return

            access_token = shop_info.get('accesstoken')
            if not access_token:
                self.query_failed.emit(f"店铺 {self.shop_name} 缺少access_token")
                return

            total_count = len(self.missing_products)
            self.query_progress.emit(f"正在查询店铺 {self.shop_name} 的 {total_count} 个商品的上家信息...")

            # 并发查询上家信息
            success_count = 0
            processed_count = 0
            count_lock = threading.Lock()

            def query_single_product(product):
                """查询单个商品的上家信息"""
                try:
                    row = product['row']
                    product_id = product['product_id']

                    # 创建API实例
                    from tool.快手api import KuaishouAPI
                    api = KuaishouAPI(access_token=access_token)

                    # 调用商品详情接口
                    result = api.get_item_detail(int(product_id))

                    if result.get('success'):
                        data = result.get('data', {})
                        sku_infos = data.get('skuInfos', [])

                        # 获取类目信息
                        category_name = data.get('categoryName', '')

                        if sku_infos:
                            sku_nick = sku_infos[0].get('skuNick', '')

                            if sku_nick:
                                # 处理上家信息：提取纯数字
                                processed_sku_nick = self.extract_numbers_only(sku_nick)

                                if processed_sku_nick:  # 确保处理后的结果不为空
                                    # 线程安全地更新数据（存储为字典格式，包含类目信息）
                                    with count_lock:
                                        self.product_manager.supplier_data[product_id] = {
                                            'supplier_id': processed_sku_nick,
                                            'supplier_name': '',  # 上家名称暂时为空，后续查询时填充
                                            'category_name': category_name
                                        }

                                    return (row, processed_sku_nick, category_name, True)
                                else:
                                    return (row, '上家信息无效', category_name, False)
                            else:
                                return (row, '未找到上家信息', category_name, False)
                        else:
                            return (row, '无SKU信息', category_name, False)
                    else:
                        error_msg = result.get('message', '未知错误')
                        return (row, f'查询失败: {error_msg}', '', False)

                except Exception as e:
                    print(f"查询商品 {product_id} 详情失败: {str(e)}")
                    return (row, f'查询异常', '', False)

            # 使用线程池并发查询
            with ThreadPoolExecutor(max_workers=50) as executor:
                future_to_product = {executor.submit(query_single_product, product): product for product in self.missing_products}

                for future in as_completed(future_to_product):
                    if self.should_stop:
                        break

                    try:
                        row, result_text, category_text, success = future.result()

                        # 发送行更新信号（包含上家信息和类目信息）
                        self.row_updated.emit(row, result_text, category_text)

                        # 更新计数
                        with count_lock:
                            processed_count += 1
                            if success:
                                success_count += 1

                        # 更新进度
                        progress_text = f"店铺 {self.shop_name} 上家查询: {processed_count}/{total_count}"
                        self.query_progress.emit(progress_text)

                    except Exception as e:
                        print(f"处理查询结果时出错: {str(e)}")
                        with count_lock:
                            processed_count += 1

            # 保存查询到的上家信息到SQLite数据库
            print(f"🔍 [DEBUG] 准备保存上家信息:")
            print(f"  - 店铺名称: '{self.shop_name}'")
            print(f"  - product_manager类型: {type(self.product_manager)}")
            print(f"  - 是否有save_supplier_data_to_db方法: {hasattr(self.product_manager, 'save_supplier_data_to_db')}")
            print(f"  - product_manager.supplier_data数量: {len(getattr(self.product_manager, 'supplier_data', {}))}")

            if hasattr(self.product_manager, 'save_supplier_data_to_db') and self.shop_name:
                try:
                    self.product_manager.save_supplier_data_to_db(self.shop_name)
                    print(f"店铺 {self.shop_name} 上家信息已保存到SQLite数据库")

                    # 保存成功后，启动在线状态查询
                    print(f"🔍 开始查询店铺 {self.shop_name} 的商品在线状态...")
                    if hasattr(self.product_manager, 'start_online_status_query'):
                        self.product_manager.start_online_status_query()
                    else:
                        print("⚠️ 在线状态查询功能不可用")

                except Exception as save_error:
                    print(f"保存店铺 {self.shop_name} 上家信息到SQLite数据库失败: {str(save_error)}")
                    import traceback
                    traceback.print_exc()
            else:
                if not hasattr(self.product_manager, 'save_supplier_data_to_db'):
                    print(f"❌ 错误：product_manager没有save_supplier_data_to_db方法")
                if not self.shop_name:
                    print(f"❌ 错误：店铺名称为空")
                print(f"警告：无法保存店铺 {self.shop_name} 的上家信息到SQLite数据库，缺少保存方法或店铺名称")

            # 发送完成信号
            self.query_completed.emit(success_count, total_count)

        except Exception as e:
            error_msg = f"查询上家信息异常: {str(e)}"
            print(error_msg)
            self.query_failed.emit(error_msg)

    def extract_numbers_only(self, text):
        """提取字符串中的纯数字"""
        try:
            import re
            numbers = re.findall(r'\d+', str(text))
            return ''.join(numbers) if numbers else ''
        except Exception as e:
            print(f"提取数字失败: {str(e)}")
            return str(text)  # 出错时返回原始文本

    def stop(self):
        """停止查询"""
        self.should_stop = True




class ProductSearchThread(QThread):
    """商品搜索任务"""
    search_progress = pyqtSignal(str)  # 搜索进度信号
    search_completed = pyqtSignal(list, str, int)  # 搜索完成信号 (结果, 搜索词, 搜索店铺数)
    error_occurred = pyqtSignal(str)  # 错误信号
    partial_results = pyqtSignal(list, str)  # 部分结果信号 (商品列表, 店铺名称)

    def __init__(self, shop_names, search_query, is_product_id, only_on_sale=True):
        super().__init__()
        self.shop_names = shop_names
        self.search_query = search_query
        self.is_product_id = is_product_id
        self.only_on_sale = only_on_sale  # 是否只搜索在售商品
        self._is_cancelled = False
        self.result_queue = []  # 结果队列
        self.queue_lock = threading.Lock()  # 队列锁

    def cancel(self):
        """取消搜索"""
        self._is_cancelled = True

    def run(self):
        """执行并发搜索"""
        try:
            from tool.快手cokie_api import KuaishouCookieAPI
            import concurrent.futures
            import threading

            all_results = []
            searched_shops = 0

            # 获取可用账号列表
            api = KuaishouCookieAPI()
            accounts = api.list_available_accounts()
            if not accounts:
                self.error_occurred.emit("没有找到可用的账号")
                return

            # 创建店铺名称到店铺ID的映射
            shop_name_to_id = {}
            for account in accounts:
                shop_name = account.get('店铺名称', '').strip()
                shop_id = account.get('店铺ID', '').strip()
                if shop_name and shop_id:
                    shop_name_to_id[shop_name] = shop_id

            # 准备搜索任务
            search_tasks = []
            for shop_name in self.shop_names:
                # 提取纯店铺名称（去掉商品数部分）
                if '[' in shop_name and ']' in shop_name:
                    clean_shop_name = shop_name.split('[')[0].strip()
                else:
                    clean_shop_name = shop_name.strip()

                # 查找对应的店铺ID
                shop_id = shop_name_to_id.get(clean_shop_name)
                if shop_id:
                    search_tasks.append((clean_shop_name, shop_id))
                else:
                    print(f"未找到店铺 '{clean_shop_name}' 的ID，跳过")

            if not search_tasks:
                self.error_occurred.emit("没有找到有效的店铺进行搜索")
                return

            # 使用线程锁保护共享变量
            results_lock = threading.Lock()
            progress_lock = threading.Lock()
            completed_count = 0
            found_result = False  # 用于商品ID搜索的早期退出

            def search_single_shop(shop_info):
                """搜索单个店铺的函数"""
                nonlocal completed_count, found_result, searched_shops

                clean_shop_name, shop_id = shop_info

                if self._is_cancelled or (self.is_product_id and found_result):
                    return []

                try:
                    # 为每个线程创建独立的API客户端
                    thread_api = KuaishouCookieAPI()

                    # 切换到指定店铺
                    if not thread_api.switch_account(shop_id):
                        print(f"切换到店铺 {clean_shop_name} 失败，跳过")
                        return []

                    # 验证cookies是否有效
                    if not thread_api.validate_cookies():
                        print(f"店铺 {clean_shop_name} 的Cookies无效，跳过")
                        return []

                    with progress_lock:
                        searched_shops += 1
                        completed_count += 1
                        self.search_progress.emit(f"正在搜索店铺 {completed_count}/{len(search_tasks)}: {clean_shop_name}")

                    # 执行搜索 - 根据"在售"复选框状态设置搜索范围
                    manager_tab = "ON_SALE" if self.only_on_sale else "ALL"
                    result = thread_api.search_all_products(
                        search_query=self.search_query,
                        manager_tab=manager_tab
                    )

                    if result.get('result') == 1:
                        data = result.get('data', {})
                        products = data.get('dataSource', [])

                        if products:
                            # 为每个商品添加店铺信息
                            for product in products:
                                product['_shop_name'] = clean_shop_name
                                product['_shop_id'] = shop_id

                            print(f"店铺 {clean_shop_name} 找到 {len(products)} 条匹配商品")

                            # 如果是商品ID搜索且找到结果，设置标志
                            if self.is_product_id and products:
                                with progress_lock:
                                    found_result = True
                                print(f"商品ID搜索在店铺 {clean_shop_name} 找到结果")

                            return products
                        else:
                            print(f"店铺 {clean_shop_name} 未找到匹配商品")
                    else:
                        error_msg = result.get('error_msg', '未知错误')
                        print(f"搜索店铺 {clean_shop_name} 失败: {error_msg}")

                except Exception as e:
                    print(f"搜索店铺 {clean_shop_name} 时发生异常: {str(e)}")

                return []

            # 并发搜索
            max_workers = min(len(search_tasks), 20)  # 最多20个并发线程
            print(f"开始智能搜索 {len(search_tasks)} 个店铺，使用 {max_workers} 个任务")

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有搜索任务
                future_to_shop = {executor.submit(search_single_shop, task): task for task in search_tasks}

                # 收集结果
                for future in concurrent.futures.as_completed(future_to_shop):
                    if self._is_cancelled:
                        break

                    try:
                        shop_results = future.result()
                        if shop_results:
                            shop_info = future_to_shop[future]
                            shop_name = shop_info[0]

                            with results_lock:
                                all_results.extend(shop_results)

                            # 发送部分结果信号，实时显示搜索结果
                            self.partial_results.emit(shop_results, shop_name)

                        # 如果是商品ID搜索且已找到结果，取消其他任务
                        if self.is_product_id and found_result:
                            print("商品ID搜索找到结果，取消其他搜索任务")
                            for remaining_future in future_to_shop:
                                if not remaining_future.done():
                                    remaining_future.cancel()
                            break

                    except Exception as e:
                        shop_info = future_to_shop[future]
                        print(f"搜索任务 {shop_info[0]} 执行异常: {str(e)}")

            # 发送搜索完成信号
            self.search_completed.emit(all_results, self.search_query, searched_shops)

        except Exception as e:
            print(f"搜索任务异常: {str(e)}")
            self.error_occurred.emit(f"搜索过程中发生异常: {str(e)}")

class NumericTableWidgetItem(QTableWidgetItem):
    """数值类型的表格项，支持数值排序"""
    def __init__(self, text):
        super().__init__(text)
        
    def __lt__(self, other):
        try:
            # 提取数字进行比较
            self_value = self.extract_number(self.text())
            other_value = self.extract_number(other.text())
            return self_value < other_value
        except:
            # 如果提取数字失败，按字符串排序
            return self.text() < other.text()
    
    def extract_number(self, text):
        """从文本中提取数字"""
        if not text:
            return 0
        # 移除货币符号、逗号等，提取数字
        import re
        # 处理价格范围，如"¥59.90-79.90"，取最小值
        if '-' in text and '¥' in text:
            numbers = re.findall(r'[\d.]+', text)
            if numbers:
                return float(numbers[0])
        else:
            # 提取所有数字
            numbers = re.findall(r'[\d.]+', text)
            if numbers:
                return float(numbers[0])
        return 0

class DateTableWidgetItem(QTableWidgetItem):
    """日期类型的表格项，支持日期排序"""
    def __init__(self, text):
        super().__init__(text)
        
    def __lt__(self, other):
        try:
            # 解析日期进行比较
            self_date = self.parse_date(self.text())
            other_date = self.parse_date(other.text())
            return self_date < other_date
        except:
            # 如果解析日期失败，按字符串排序
            return self.text() < other.text()
    
    def parse_date(self, text):
        """解析日期字符串 - 支持 "2025-03-21 17:18" 格式"""
        if not text:
            return datetime.min
        try:
            # 尝试解析格式："2025-03-21 17:18"
            if len(text) >= 16:
                return datetime.strptime(text[:16], '%Y-%m-%d %H:%M')
            elif len(text) >= 10:
                return datetime.strptime(text[:10], '%Y-%m-%d')
            else:
                return datetime.min
        except:
            return datetime.min

class ProductPageLoader(QThread):
    """单个分页加载任务"""
    page_loaded = pyqtSignal(list, int, str)  # 商品列表, 页码, 店铺名
    error_occurred = pyqtSignal(str, int, str)  # 错误信息, 页码, 店铺名
    
    def __init__(self, shop_name, shop_id, start_page, end_page, page_size=50, manager_tab="ON_SALE"):
        super().__init__()
        self.shop_name = shop_name
        self.shop_id = shop_id
        self.start_page = start_page
        self.end_page = end_page
        self.page_size = page_size
        self.manager_tab = manager_tab  # 新增：商品状态参数
        self.should_stop = False
        
    def run(self):
        """执行分页加载"""
        try:
            # 创建独立的API实例
            api = KuaishouCookieAPI()
            
            # 切换到对应账号
            if not api.switch_account(self.shop_id):
                self.error_occurred.emit(f"切换账号失败", 0, self.shop_name)
                return
            
            # 加载指定范围的分页
            for page in range(self.start_page, self.end_page + 1):
                if self.should_stop:
                    break
                    
                # 增加重试机制处理频率限制
                max_retries = 3
                retry_delay = 2.0  # 重试延时（秒），增加到2秒避免API限制
                success = False
                
                for retry in range(max_retries):
                    try:
                        # 添加页面间延时，避免频率限制
                        if page > self.start_page or retry > 0:
                            import time
                            time.sleep(1.2)  # 每个页面请求间隔1.2秒，避免API频率限制
                        
                        # 传递manager_tab参数
                        result = api.get_product_list(manager_tab=self.manager_tab, cur_page=page, page_size=self.page_size)
                        
                        if result.get('result') == 1:
                            data = result.get('data', {})
                            products = data.get('dataSource', [])
                            
                            if products:
                                # 添加店铺名称到每个商品 - 提取纯店铺名称，去掉商品数部分
                                clean_shop_name = self.shop_name
                                if '[' in clean_shop_name and ']' in clean_shop_name:
                                    clean_shop_name = clean_shop_name.split('[')[0].strip()

                                for product in products:
                                    product['shop_name'] = clean_shop_name
                                
                                self.page_loaded.emit(products, page, self.shop_name)
                                success = True
                                break  # 成功加载，跳出重试循环
                            else:
                                # 没有商品了，提前结束
                                success = True
                                break
                        else:
                            error_msg = result.get('error_msg', '未知错误')
                            if '操作太快' in error_msg and retry < max_retries - 1:
                                # 频率限制错误，增加延时重试
                                print(f"第{page}页频率限制，等待{retry_delay}秒后重试({retry + 1}/{max_retries})")
                                import time
                                time.sleep(retry_delay)
                                retry_delay *= 1.5  # 递增延时
                            else:
                                self.error_occurred.emit(f"第{page}页加载失败: {error_msg}", page, self.shop_name)
                                break
                            
                    except Exception as e:
                        if retry < max_retries - 1:
                            print(f"第{page}页加载异常，重试({retry + 1}/{max_retries}): {str(e)}")
                            import time
                            time.sleep(retry_delay)
                            retry_delay *= 1.5
                        else:
                            self.error_occurred.emit(f"第{page}页加载异常: {str(e)}", page, self.shop_name)
                
                # 如果所有重试都失败了
                if not success and max_retries > 0:
                    self.error_occurred.emit(f"第{page}页重试{max_retries}次后仍然失败", page, self.shop_name)
                    
                # 短暂休息，避免过于频繁
                self.msleep(100)
                
        except Exception as e:
            self.error_occurred.emit(f"加载异常: {str(e)}", 0, self.shop_name)
    
    def stop(self):
        """停止加载"""
        self.should_stop = True

class ProductLoadingThread(QThread):
    """智能商品加载管理器 - 分批并行加载优化版"""
    progress_updated = pyqtSignal(str)  # 进度更新信号
    data_loaded = pyqtSignal(list, str)  # 数据加载完成信号
    page_loaded = pyqtSignal(list, int, int, str)  # 单页数据加载完成信号 (products, current_page, total_pages, shop_name)
    total_pages_info = pyqtSignal(str, int, int)  # 店铺页面信息信号 (shop_name, total_pages, total_products)
    error_occurred = pyqtSignal(str)  # 错误信号

    def __init__(self, shop_names, manager_tab="ON_SALE"):
        super().__init__()
        self.shop_names = shop_names
        self.manager_tab = manager_tab  # 新增：商品状态参数
        self.kuaishou_api = KuaishouCookieAPI()
        self.all_products = []
        self.should_stop = False
        self.mutex = QMutex()  # 线程安全锁
        self.active_threads = []  # 活跃线程列表
        self.loaded_pages = {}  # 记录已加载的页面 {shop_name: {page: products}}
        self.total_pages_map = {}  # 记录每个店铺的总页数 {shop_name: total_pages}
        
    def calculate_batch_strategy(self, total_pages, total_count):
        """
        计算分批加载策略 - 根据商品数量动态调整并发数

        Parameters:
        -----------
        total_pages: int
            总页数
        total_count: int
            商品总数

        Returns:
        --------
        List[int]: 每批的线程数列表
        """
        # 根据商品数量动态调整并发数
        if total_count <= 2000:
            concurrent_count = 10
            print(f"🔧 [DEBUG] 商品数量 {total_count} ≤ 2000，使用 {concurrent_count} 个并发")
        else:
            concurrent_count = 6
            print(f"🔧 [DEBUG] 商品数量 {total_count} > 2000，使用 {concurrent_count} 个并发，并添加延时")

        print(f"🔧 [DEBUG] calculate_batch_strategy: total_pages={total_pages}, concurrent_count={concurrent_count}")

        # 根据总页数和并发数计算批次
        if total_pages <= concurrent_count:
            # 页数少于并发数，一次性处理
            print(f"🔧 [DEBUG] 页数少于并发数，一次性处理: [{total_pages}]")
            return [total_pages]
        else:
            # 分批处理
            batches = []
            remaining_pages = total_pages

            print(f"🔧 [DEBUG] 开始分批处理: total_pages={total_pages}, concurrent_count={concurrent_count}")

            while remaining_pages > concurrent_count:
                batches.append(concurrent_count)
                remaining_pages -= concurrent_count
                print(f"🔧 [DEBUG] 添加批次: {concurrent_count}, 剩余页数: {remaining_pages}")

            # 剩余页面一次性处理
            if remaining_pages > 0:
                batches.append(remaining_pages)
                print(f"🔧 [DEBUG] 添加最后批次: {remaining_pages}")

            print(f"🔧 [DEBUG] 最终批次策略: {batches}")
            return batches
        
    def run(self):
        """运行分批多线程加载任务"""
        try:
            total_shops = len(self.shop_names)
            
            for shop_index, shop_name in enumerate(self.shop_names):
                if self.should_stop:
                    break

                # 提取纯店铺名称，去掉商品数部分
                clean_shop_name = shop_name
                if '[' in clean_shop_name and ']' in clean_shop_name:
                    clean_shop_name = clean_shop_name.split('[')[0].strip()

                self.progress_updated.emit(f"正在分析店铺: {clean_shop_name} ({shop_index + 1}/{total_shops})")

                # 获取店铺ID - 使用纯店铺名称
                shop_id = self.get_shop_id_by_name(clean_shop_name)
                if not shop_id:
                    self.error_occurred.emit(f"未找到店铺 {clean_shop_name} 的ID")
                    continue

                # 先获取第一页来确定总数
                if not self.kuaishou_api.switch_account(shop_id):
                    self.error_occurred.emit(f"切换到店铺 {clean_shop_name} 失败")
                    continue
                
                # 获取商品总数 - 传递manager_tab参数
                first_page_result = self.kuaishou_api.get_product_list(manager_tab=self.manager_tab, cur_page=1, page_size=50)
                if first_page_result.get('result') != 1:
                    self.error_occurred.emit(f"获取店铺 {clean_shop_name} 商品信息失败")
                    continue

                total_count = first_page_result.get('data', {}).get('total', 0)
                if total_count == 0:
                    self.progress_updated.emit(f"店铺 {clean_shop_name} 没有{self.get_status_name()}商品")
                    continue
                
                page_size = 50
                total_pages = math.ceil(total_count / page_size)
                
                # 记录店铺的总页数
                self.total_pages_map[clean_shop_name] = total_pages
                
                # 发送店铺页面信息信号，让UI计算总进度
                self.total_pages_info.emit(clean_shop_name, total_pages, total_count)
                
                # 计算分批策略
                batch_sizes = self.calculate_batch_strategy(total_pages, total_count)
                
                self.progress_updated.emit(f"店铺 {clean_shop_name}: 共{total_count}个{self.get_status_name()}商品，{total_pages}页，分{len(batch_sizes)}批加载: {batch_sizes}")

                # 初始化该店铺的页面记录
                self.loaded_pages[clean_shop_name] = {}
                
                # 执行分批加载
                current_page = 1
                for batch_index, batch_size in enumerate(batch_sizes):
                    if self.should_stop:
                        break
                        
                    batch_threads = []
                    batch_start_page = current_page
                    
                    self.progress_updated.emit(f"店铺 {clean_shop_name} - 批次{batch_index + 1}/{len(batch_sizes)}: 启动{batch_size}个任务")

                    # 为当前批次分配页面 - 每个线程处理1页
                    for i in range(batch_size):
                        if current_page > total_pages:
                            break

                        start_page = current_page
                        end_page = current_page  # 每个线程只处理1页

                        if start_page <= end_page:
                            # 传递manager_tab参数到页面加载器 - 使用纯店铺名称
                            thread = ProductPageLoader(clean_shop_name, shop_id, start_page, end_page, page_size, self.manager_tab)
                            thread.page_loaded.connect(self.on_page_loaded)
                            thread.error_occurred.connect(self.on_page_error)
                            batch_threads.append(thread)
                            self.active_threads.append(thread)
                            current_page += 1  # 移动到下一页
                    
                    # 优化启动策略：错开启动时间，避免API限制
                    for i, thread in enumerate(batch_threads):
                        thread.start()
                        # 增加线程启动间隔至1.5秒，避免API频率限制
                        if i < len(batch_threads) - 1:  # 最后一个线程不需要等待
                            import time
                            time.sleep(1.5)  # 增加到1.5秒延时，避免API频率限制
                            print(f"线程 {i+1}/{len(batch_threads)} 启动完成，等待1.5秒后启动下一个")
                    
                    # 等待当前批次完成 - 添加超时保护
                    print(f"等待批次{batch_index + 1}的{len(batch_threads)}个线程完成...")
                    for thread in batch_threads:
                        thread.wait(30000)  # 30秒超时
                    
                    # 额外等待确保所有回调完成
                    import time
                    time.sleep(1.0)  # 等待1秒确保回调完成

                    # 批次间延时，避免连续批次造成API压力
                    if batch_index < len(batch_sizes) - 1:  # 不是最后一个批次
                        time.sleep(2.0)  # 批次间等待2秒
                        print(f"批次{batch_index + 1}完成，等待2秒后启动下一批次")
                    
                    # 验证数据完整性 - 重试机制
                    batch_end_page = current_page - 1
                    expected_pages = list(range(batch_start_page, batch_end_page + 1))
                    
                    # 等待数据完整性，最多重试1次（大幅减少重试次数）
                    retry_count = 0
                    max_retries = 1  # 进一步减少重试次数，避免卡死
                    while retry_count < max_retries and not self.should_stop:  # 检查停止标志
                        missing_pages = [p for p in expected_pages if p not in self.loaded_pages.get(clean_shop_name, {})]
                        if not missing_pages:
                            break  # 数据完整

                        print(f"批次{batch_index + 1}数据不完整，缺少页面: {missing_pages}，重试{retry_count + 1}/{max_retries}")

                        # 如果缺失页面过多（超过批次大小的30%），可能是频率限制，直接停止
                        if len(missing_pages) > batch_size * 0.3 and not self.should_stop:  # 降低阈值，更早停止
                            print(f"⚠️ 检测到大量页面缺失({len(missing_pages)}/{batch_size})，可能遇到API频率限制")
                            self.should_stop = True
                            self.error_occurred.emit(f"检测到API请求频繁，请稍后再试。建议等待1-2分钟后重新加载。")
                            return

                        time.sleep(0.2)  # 减少等待时间到200ms，加快响应
                        retry_count += 1
                    
                    # 从活跃线程列表中移除已完成的线程
                    for thread in batch_threads:
                        if thread in self.active_threads:
                            self.active_threads.remove(thread)
                    
                    loaded_in_batch = sum(len(self.loaded_pages[clean_shop_name].get(p, [])) for p in expected_pages if p in self.loaded_pages[clean_shop_name])
                    
                    if missing_pages:
                        self.progress_updated.emit(f"店铺 {clean_shop_name} - 批次{batch_index + 1}部分完成: 页面{batch_start_page}-{batch_end_page}，加载{loaded_in_batch}个商品 (缺少{len(missing_pages)}页)")
                    else:
                        self.progress_updated.emit(f"店铺 {clean_shop_name} - 批次{batch_index + 1}完成: 页面{batch_start_page}-{batch_end_page}，加载{loaded_in_batch}个商品")

                    # 如果商品数量大于2000，在批次之间添加1秒延时
                    if total_count > 2000 and batch_index < len(batch_sizes) - 1:  # 不是最后一个批次
                        import time
                        print(f"商品数量 > 2000，批次间延时1秒...")
                        time.sleep(1)
                
                # 收集该店铺的所有商品并按页面顺序排序 - 添加数据完整性验证
                shop_products = []
                loaded_pages_count = 0
                missing_pages = []
                
                for page in range(1, total_pages + 1):
                    if page in self.loaded_pages.get(clean_shop_name, {}):
                        page_products = self.loaded_pages[clean_shop_name][page]
                        shop_products.extend(page_products)
                        loaded_pages_count += 1
                    else:
                        missing_pages.append(page)

                self.all_products.extend(shop_products)
                
                # 显示详细的完成信息
                if missing_pages:
                    missing_ratio = len(missing_pages) / total_pages if total_pages > 0 else 0
                    self.progress_updated.emit(f"店铺 {clean_shop_name} 部分完成：加载{len(shop_products)}个{self.get_status_name()}商品 ({loaded_pages_count}/{total_pages}页，缺少页面{missing_pages})")
                    print(f"警告: 店铺 {clean_shop_name} 有{len(missing_pages)}页数据缺失: {missing_pages}")

                    # 如果缺失页面超过20%，可能是API频率限制问题，停止后续处理（降低阈值）
                    if missing_ratio > 0.2 and not self.should_stop:  # 降低阈值，更早检测和停止
                        print(f"⚠️ 店铺 {clean_shop_name} 缺失页面过多({missing_ratio:.1%})，可能遇到API频率限制")
                        self.should_stop = True
                        self.error_occurred.emit(f"店铺 {clean_shop_name} 加载失败：API请求过于频繁，请稍后再试。建议等待1-2分钟后重新加载。")
                        return
                else:
                    self.progress_updated.emit(f"店铺 {clean_shop_name} 加载完成：{len(shop_products)}个{self.get_status_name()}商品 ({loaded_pages_count}/{total_pages}页全部完成)")
                    print(f"店铺 {clean_shop_name} 数据完整性验证通过: {loaded_pages_count}/{total_pages}页，{len(shop_products)}个商品")
            
            # 所有店铺加载完成 - 添加最终验证和资源清理
            print(f"所有店铺处理完成，开始最终数据统计...")
            
            # 最终数据完整性统计
            total_expected_shops = len([shop for shop in self.shop_names if shop.strip()])
            processed_shops = len(self.loaded_pages)
            total_loaded_products = len(self.all_products)
            
            # 计算详细统计信息
            shop_stats = []
            for shop_name, pages_data in self.loaded_pages.items():
                shop_product_count = sum(len(products) for products in pages_data.values())
                shop_stats.append(f"{shop_name}({shop_product_count}个)")
            
            print(f"最终统计: 期望处理{total_expected_shops}个店铺，实际处理{processed_shops}个店铺")
            print(f"各店铺加载情况: {', '.join(shop_stats)}")
            
            if self.all_products:
                summary = f"智能加载完成：共{total_loaded_products}个{self.get_status_name()}商品，来自{processed_shops}个店铺"
                self.data_loaded.emit(self.all_products, summary)
            else:
                self.error_occurred.emit(f"没有加载到任何{self.get_status_name()}商品数据")
            
            # 执行资源清理，避免内存积累
            print("执行加载后资源清理...")
            try:
                import gc
                # 清理已完成的线程引用
                self.active_threads.clear()
                # 强制垃圾回收
                gc.collect()
                print("加载资源清理完成")
            except Exception as cleanup_error:
                print(f"加载资源清理异常: {cleanup_error}")
                
        except Exception as e:
            self.error_occurred.emit(f"智能加载异常: {str(e)}")
    
    def get_status_name(self):
        """根据manager_tab获取状态名称"""
        status_map = {
            "ON_SALE": "在售",
            "DOWN_SHELF": "已下架", 
            "AUDITING": "审核中",
            "AUDIT_WAIT_FOR_UPDATE": "待修改",
            "ALL": "全部"
        }
        return status_map.get(self.manager_tab, "商品")
    
    def on_page_loaded(self, products, page, shop_name):
        """单页加载完成回调"""
        self.mutex.lock()
        try:
            # 保存到对应店铺的页面记录中
            if shop_name not in self.loaded_pages:
                self.loaded_pages[shop_name] = {}
            self.loaded_pages[shop_name][page] = products
            
            # 计算总页数（这里需要从已知信息推算）
            total_pages_estimate = max(self.loaded_pages[shop_name].keys()) if self.loaded_pages[shop_name] else 1
            
            # 发送页面加载完成信号
            self.page_loaded.emit(products, page, total_pages_estimate, shop_name)
            
        finally:
            self.mutex.unlock()
    
    def on_page_error(self, error_msg, page, shop_name):
        """页面加载错误回调"""
        self.progress_updated.emit(f"店铺 {shop_name} 第{page}页加载失败: {error_msg}")
    
    def stop(self):
        """停止所有线程"""
        self.should_stop = True
        for thread in self.active_threads:
            thread.stop()
    
    def get_shop_id_by_name(self, shop_name):
        """根据店铺名称获取店铺ID"""
        try:
            accounts = self.kuaishou_api.list_available_accounts()
            for account in accounts:
                if account.get('店铺名称') == shop_name:
                    return account.get('店铺ID')
            return None
        except Exception as e:
            print(f"获取店铺ID失败: {str(e)}")
            return None

class ProductManager(QMainWindow):
    def __init__(self):
        super().__init__()
        # 使用setFixedSize强制设置窗口尺寸
        self.setFixedSize(1240, 700)  # 调整宽度为1240px
        
        # 初始化快手API
        self.kuaishou_api = KuaishouCookieAPI()

        # 初始化上家缓存
        self.supplier_cache = {}
        self.supplier_data = {}  # 上家信息数据字典

        # 页面级进度跟踪变量
        self.total_pages_all_shops = 0  # 所有店铺的总页数
        self.completed_pages_count = 0  # 已完成的页面数量
        self.shop_pages_info = {}  # 每个店铺的页面信息 {shop_name: {total_pages: int, completed_pages: int, total_products: int}}
        
        self.initUI()
        
    def initUI(self):
        # 主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 顶部工具栏（将在右侧区域中使用）
        top_frame = QFrame()
        top_frame.setFixedHeight(40)
        top_frame.setStyleSheet("""
            QFrame {
                background-color: #F4F4F4;
                border-bottom: 1px solid #d0d0d0;
            }
        """)

        top_layout = QHBoxLayout(top_frame)
        top_layout.setContentsMargins(10, 0, 10, 0)
        
        # 是否显示缩略图
        self.thumbnail_check = QCheckBox("是否缩略图")
        self.thumbnail_check.setChecked(True)
        # 设置尺寸策略为内容适应宽度
        self.thumbnail_check.setSizePolicy(self.thumbnail_check.sizePolicy().horizontalPolicy(), self.thumbnail_check.sizePolicy().verticalPolicy())
        self.thumbnail_check.adjustSize()
        self.thumbnail_check.setStyleSheet("""
            QCheckBox {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                padding: 0px;
                margin: 0px;
            }
        """)
        
        # 商品列表按钮
        self.product_list_btn = QPushButton("商品列表")
        self.product_list_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px 8px;
                background-color: #f5f5f5;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e5e5e5;
            }
        """)
        # 连接商品列表按钮的点击事件
        self.product_list_btn.clicked.connect(self.handle_product_list_btn_click)
        
        # 商品ID输入框（删除标签，调大宽度）
        self.product_id_input = QLineEdit()
        self.product_id_input.setFixedWidth(250)  # 从150调大到250
        self.product_id_input.setPlaceholderText("请输入商品ID或标题关键词")  # 更新占位符提示
        self.product_id_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
        """)
        # 连接回车键事件
        self.product_id_input.returnPressed.connect(self.search_products)
        
        # 搜索按钮
        search_btn = QPushButton("搜索")
        search_btn.setObjectName("search_btn")  # 设置对象名称，便于查找
        search_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px 8px;
                background-color: #f5f5f5;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e5e5e5;
            }
            QPushButton:disabled {
                background-color: #f0f0f0;
                color: #999999;
            }
        """)
        search_btn.clicked.connect(self.search_products)

        # 筛选按钮
        filter_btn = QPushButton("筛选")
        filter_btn.setObjectName("filter_btn")  # 设置对象名称，便于查找
        filter_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px 8px;
                background-color: #f5f5f5;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e5e5e5;
            }
            QPushButton:disabled {
                background-color: #f0f0f0;
                color: #999999;
            }
        """)
        filter_btn.clicked.connect(self.filter_products)

        # 状态按钮组
        status_btns = [
            QPushButton("已下架"),
            QPushButton("审核中"),
            QPushButton("审核失败"),
            QPushButton("草稿箱"),
            QPushButton("回收站"),
            QPushButton("记录"),
            QPushButton("上家"),
            QPushButton("查询")
        ]
        
        for btn in status_btns:
            btn.setStyleSheet("""
                QPushButton {
                    border: 1px solid #d0d0d0;
                    border-radius: 2px;
                    padding: 3px 8px;
                    background-color: #f5f5f5;
                    font-family: 'Microsoft YaHei';
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #e5e5e5;
                }
            """)
        
        # 为状态按钮添加点击事件 - 修复lambda闭包问题
        self.status_btn_down_shelf = status_btns[0]  # 已下架
        self.status_btn_auditing = status_btns[1]     # 审核中 
        self.status_btn_audit_failed = status_btns[2] # 审核失败
        self.status_btn_supplier = status_btns[6]     # 上家
        
        self.status_btn_down_shelf.clicked.connect(lambda: self.load_products_by_status("DOWN_SHELF", "已下架"))
        self.status_btn_auditing.clicked.connect(lambda: self.load_products_by_status("AUDITING", "审核中"))
        self.status_btn_audit_failed.clicked.connect(lambda: self.load_products_by_status("AUDIT_WAIT_FOR_UPDATE", "审核失败"))
        self.status_btn_supplier.clicked.connect(self.get_supplier_info)  # 上家按钮点击事件
        
        # 为查询按钮添加点击事件
        self.status_btn_query = status_btns[7]  # 查询按钮
        self.status_btn_query.clicked.connect(self.check_supplier_status)

        # 其他按钮暂时显示开发中提示
        for i in range(3, len(status_btns)):
            btn = status_btns[i]
            btn_text = btn.text()
            # 跳过"上家"和"查询"按钮，它们已经有专门的处理
            if btn_text != "上家" and btn_text != "查询":
                btn.clicked.connect(lambda checked=False, text=btn_text: QMessageBox.information(self, "提示", f"{text}功能开发中，敬请期待...") if not checked else None)
        

        
        # 添加顶部工具栏组件 - 全部左对齐
        top_layout.addWidget(self.thumbnail_check)
        top_layout.addWidget(self.product_list_btn)
        top_layout.addWidget(self.product_id_input)  # 删除了商品ID标签和标题复选框
        top_layout.addWidget(search_btn)
        top_layout.addWidget(filter_btn)

        # 添加状态按钮组 - 继续左对齐排列
        for btn in status_btns:
            top_layout.addWidget(btn)
        
        # 主内容区域 - 左右布局
        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # 左侧区域 - 搜索区域和分类树
        left_frame = QFrame()
        left_frame.setFixedWidth(250)
        left_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-right: 1px solid #d0d0d0;
            }
        """)

        left_layout = QVBoxLayout(left_frame)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(0)

        # 搜索区域
        search_frame = QFrame()
        search_frame.setFixedHeight(40)
        search_frame.setStyleSheet("background-color: white; border-bottom: 1px solid #d0d0d0;")

        search_layout = QHBoxLayout(search_frame)
        search_layout.setContentsMargins(5, 5, 5, 5)
        search_layout.setSpacing(5)

        # 店铺输入框 - 占大部分宽度
        self.shop_input = QLineEdit()
        self.shop_input.setPlaceholderText("输入店铺名称...")
        self.shop_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 5px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #3E5FAE;
            }
        """)

        # 搜索按钮 - 固定宽度
        shop_search_btn = QPushButton("搜索")
        shop_search_btn.setFixedWidth(50)
        shop_search_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #3E5FAE;
                border-radius: 2px;
                padding: 5px;
                background-color: #3E5FAE;
                color: white;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2E4F9E;
            }
            QPushButton:pressed {
                background-color: #1E3F8E;
            }
        """)
        shop_search_btn.clicked.connect(self.search_shops)

        # 连接搜索框事件
        self.shop_input.textChanged.connect(self.on_search_text_changed)  # 实时搜索
        self.shop_input.returnPressed.connect(self.search_shops)  # 回车搜索

        # 添加到搜索布局 - 输入框占大部分空间，搜索按钮固定宽度
        search_layout.addWidget(self.shop_input, 1)  # 比例为1，占大部分空间
        search_layout.addWidget(shop_search_btn)  # 固定宽度

        # 分类树
        self.category_tree = CategoryTree()

        # 设置分类树的尺寸策略，确保它能够占满剩余空间
        from PyQt5.QtWidgets import QSizePolicy
        self.category_tree.setSizePolicy(
            QSizePolicy.Preferred,  # 水平方向首选
            QSizePolicy.Expanding   # 垂直方向可扩展
        )

        # 打印调试信息
        print("商品管理器中的分类树已初始化")

        # 商品信息框 - 在分类树底部
        self.product_info_frame = QFrame()
        self.product_info_frame.setFixedHeight(130)  # 调整高度适应内容
        self.product_info_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-top: 1px solid #d0d0d0;
                border-right: 1px solid #d0d0d0;
            }
        """)

        # 商品信息框布局
        info_layout = QVBoxLayout(self.product_info_frame)
        info_layout.setContentsMargins(8, 8, 8, 8)  # 减小外边距
        info_layout.setSpacing(0)

        # 商品信息容器 - 带边框
        product_container = QFrame()
        product_container.setStyleSheet("""
            QFrame {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                background-color: #fafafa;
            }
        """)

        # 商品图片和基本信息区域
        product_content_layout = QHBoxLayout(product_container)
        product_content_layout.setContentsMargins(6, 6, 6, 6)  # 减小内边距
        product_content_layout.setSpacing(10)  # 减小间距

        # 商品图片
        self.product_image_label = QLabel()
        self.product_image_label.setFixedSize(100, 100)
        self.product_image_label.setStyleSheet("""
            QLabel {
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                background-color: white;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
                color: #999;
            }
        """)
        self.product_image_label.setAlignment(Qt.AlignCenter)
        self.product_image_label.setText("暂无图片")
        self.product_image_label.setScaledContents(True)

        # 商品信息文本区域
        product_text_layout = QVBoxLayout()
        product_text_layout.setSpacing(8)  # 增加标题框和价格框之间的间距
        product_text_layout.setContentsMargins(0, 0, 0, 0)

        # 商品标题
        self.product_title_label = QLabel("")  # 移除提示文字
        self.product_title_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 9px;
                font-weight: normal;
                color: #2c3e50;
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 4px;
                margin: 0px;
            }
        """)
        self.product_title_label.setWordWrap(True)
        self.product_title_label.setFixedHeight(70)  # 调整高度
        self.product_title_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)  # 顶部左对齐

        # 商品价格
        self.product_price_label = QLabel("")  # 移除提示文字
        self.product_price_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #e74c3c;
                font-weight: bold;
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 4px;
                margin: 0px;
            }
        """)
        self.product_price_label.setFixedHeight(25)  # 调整高度适应更小字体
        self.product_price_label.setAlignment(Qt.AlignCenter)  # 居中对齐

        # 添加文本信息到布局 - 标题70px + 价格30px = 100px（等于图片高度）
        product_text_layout.addWidget(self.product_title_label)
        product_text_layout.addWidget(self.product_price_label)

        # 添加图片和文本到水平布局
        product_content_layout.addWidget(self.product_image_label)
        product_content_layout.addLayout(product_text_layout, 1)

        # 添加商品容器到信息框布局
        info_layout.addWidget(product_container)

        # 添加搜索区域、分类树和商品信息框到左侧布局
        left_layout.addWidget(search_frame)  # 搜索区域固定高度
        left_layout.addWidget(self.category_tree, 1)  # 分类树占大部分空间，拉伸因子为1
        left_layout.addWidget(self.product_info_frame)  # 商品信息框固定高度200px

        # 右侧区域 - 顶部工具栏 + 表格 + 底部筛选
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background-color: white;
            }
        """)

        right_layout = QVBoxLayout(right_frame)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)

        # 添加顶部工具栏到右侧区域
        right_layout.addWidget(top_frame)

        # 表格区域
        table_frame = QFrame()
        table_frame.setStyleSheet("""
            QFrame {
                background-color: white;
            }
        """)

        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(5, 5, 5, 5)

        # 进度条和状态标签区域
        progress_frame = QFrame()
        progress_frame.setFixedHeight(30)
        progress_frame.setStyleSheet("background-color: #F4F4F4;")

        progress_layout = QHBoxLayout(progress_frame)
        progress_layout.setContentsMargins(10, 0, 10, 0)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setFixedWidth(380)  # 固定宽度380px
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                text-align: center;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                min-width: 380px;
                max-width: 380px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 1px;
            }
        """)

        # 状态标签
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("font-family: 'Microsoft YaHei'; font-size: 12px; color: #666;")

        # 类目分析按钮
        self.category_analysis_btn = QPushButton("📊 类目分析")
        self.category_analysis_btn.setStyleSheet("""
            QPushButton {
                background-color: #3E5FAC;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
            }
            QPushButton:hover {
                background-color: #354A94;
            }
            QPushButton:pressed {
                background-color: #2A3F7C;
            }
        """)
        self.category_analysis_btn.clicked.connect(self.show_category_analysis)

        # 在售商品复选框 - 状态栏右侧（使用默认样式）
        self.on_sale_check = QCheckBox("在售")
        self.on_sale_check.setChecked(True)  # 默认勾选

        # 商品数量显示标签 - 去掉边框，保持简洁
        self.product_count_label = QLabel("商品总数: 0 | 已选择: 0")
        self.product_count_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 13px;
                color: #2c3e50;
                background-color: transparent;
                padding: 6px 12px;
                margin-left: 12px;
                font-weight: 600;
                min-width: 180px;
            }
        """)

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        progress_layout.addStretch()
        progress_layout.addWidget(self.category_analysis_btn)  # 添加类目分析按钮
        progress_layout.addWidget(self.on_sale_check)
        progress_layout.addWidget(self.product_count_label)

        # 商品表格
        self.product_table = QTableWidget()
        self.product_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #d0d0d0;
                gridline-color: #e0e0e0;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 4px;
                border: 1px solid #d0d0d0;
                border-left: none;
                border-top: none;
            }

            /* 自定义滚动条样式 */
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 12px;
                margin: 0px 0px 0px 0px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #c0c0c0;
                min-height: 30px;
                border-radius: 6px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background: #a0a0a0;
            }
            QScrollBar::handle:vertical:pressed {
                background: #808080;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }

            /* 水平滚动条样式 */
            QScrollBar:horizontal {
                border: none;
                background: #f0f0f0;
                height: 12px;
                margin: 0px 0px 0px 0px;
                border-radius: 6px;
            }
            QScrollBar::handle:horizontal {
                background: #c0c0c0;
                min-width: 30px;
                border-radius: 6px;
                margin: 2px;
            }
            QScrollBar::handle:horizontal:hover {
                background: #a0a0a0;
            }
            QScrollBar::handle:horizontal:pressed {
                background: #808080;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {
                background: none;
            }
        """)

        # 设置表格列 - 添加"上家名称"列
        self.headers = ["序号", "商品标题", "商品ID", "价格", "图片", "累销", "创建时间", "库存", "操作", "店铺", "状态", "点击量", "访问量", "类目", "上家", "上家名称"]
        self.product_table.setColumnCount(len(self.headers))
        self.product_table.setHorizontalHeaderLabels(self.headers)



        # 调整列宽 - 根据新的列顺序调整，为"上家名称"列分配100px宽度
        column_widths = [50, 200, 120, 80, 150, 60, 120, 60, 60, 100, 80, 60, 60, 80, 80, 100]
        for i, width in enumerate(column_widths):
            self.product_table.setColumnWidth(i, width)

        # 不隐藏选择列，显示复选框

        # 隐藏表格自带的序号列（垂直表头）
        self.product_table.verticalHeader().setVisible(False)

        # 启用表格排序功能
        self.product_table.setSortingEnabled(True)

        # 启用表头排序指示器，让所有列都可以点击排序
        self.product_table.horizontalHeader().setSortIndicatorShown(True)
        self.product_table.horizontalHeader().setSectionsClickable(True)

        # 连接排序信号，排序后重新生成序号
        self.product_table.horizontalHeader().sectionClicked.connect(self.on_header_clicked)

        # 监听表格模型的数据变化来检测排序完成
        self.product_table.model().layoutChanged.connect(self.on_table_layout_changed)

        # 设置右键菜单
        self.product_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.product_table.customContextMenuRequested.connect(self.show_context_menu)

        # 连接表格点击事件，用于显示商品信息
        self.product_table.itemClicked.connect(self.on_product_table_clicked)

        # 连接表格双击事件，用于打开上家1688链接
        self.product_table.itemDoubleClicked.connect(self.on_product_table_double_clicked)

        # 启用工具提示，显示完整的单元格内容
        self.product_table.setMouseTracking(True)
        self.product_table.itemEntered.connect(self.on_item_entered)

        # 初始化图片加载相关变量
        self.current_image_loader = None  # 当前图片加载器
        self.click_timer = QTimer()  # 防抖定时器
        self.click_timer.setSingleShot(True)
        self.click_timer.timeout.connect(self.process_delayed_click)
        self.pending_click_item = None  # 待处理的点击项

        # 连接分类树双击事件，用于进入店铺后台
        self.category_tree.tree_widget.itemDoubleClicked.connect(self.on_category_tree_double_clicked)

        # 设置自定义滚动条
        self.setup_custom_scrollbars()

        table_layout.addWidget(progress_frame)
        table_layout.addWidget(self.product_table, 1)

        # 添加表格区域到右侧布局
        right_layout.addWidget(table_frame, 1)

        # 添加左右区域到主内容布局
        content_layout.addWidget(left_frame)
        content_layout.addWidget(right_frame, 1)
        
        # 日期选择控件
        start_label = QLabel("开始:")
        start_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                border: none;
                background-color: transparent;
            }
        """)

        self.start_date = ModernDateButton()
        # 设置开始日期为一个月前
        current_date = QDate.currentDate()
        start_date = current_date.addMonths(-1)
        self.start_date.setDate(start_date)

        end_label = QLabel("结束:")
        end_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                border: none;
                background-color: transparent;
            }
        """)

        self.end_date = ModernDateButton()
        # 设置结束日期为当前日期
        self.end_date.setDate(current_date)

        # 时间筛选按钮
        time_filter_btn = QPushButton("时间筛选")
        time_filter_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px 10px;
                background-color: #f8f9fa;
                color: #495057;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                min-width: 70px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        time_filter_btn.clicked.connect(self.filter_by_time)
        # 添加调试：测试按钮点击
        time_filter_btn.clicked.connect(lambda: print("时间筛选按钮点击事件触发"))
        
        # 销量输入框
        sales_label = QLabel("销量:")
        sales_label.setStyleSheet("font-family: 'Microsoft YaHei'; font-size: 12px;")
        
        self.sales_min = QLineEdit()
        self.sales_min.setFixedWidth(50)
        self.sales_min.setStyleSheet("""
            QLineEdit {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px;
                background-color: white;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
        """)
        
        sales_dash = QLabel("-")
        sales_dash.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                border: none;
                background-color: transparent;
            }
        """)
        
        self.sales_max = QLineEdit()
        self.sales_max.setFixedWidth(50)
        self.sales_max.setStyleSheet("""
            QLineEdit {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px;
                background-color: white;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
        """)
        
        # 销量筛选按钮
        sales_filter_btn = QPushButton("销量筛选")
        sales_filter_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px 10px;
                background-color: #f8f9fa;
                color: #495057;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                min-width: 70px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        sales_filter_btn.clicked.connect(self.filter_by_sales)
        # 添加调试：测试按钮点击
        sales_filter_btn.clicked.connect(lambda: print("销量筛选按钮点击事件触发"))
        
        # 价格输入框
        price_label = QLabel("价格筛选:")
        price_label.setStyleSheet("font-family: 'Microsoft YaHei'; font-size: 12px;")
        
        self.price_min = QLineEdit()
        self.price_min.setFixedWidth(50)
        self.price_min.setStyleSheet("""
            QLineEdit {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px;
                background-color: white;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
        """)
        
        price_dash = QLabel("-")
        price_dash.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                border: none;
                background-color: transparent;
            }
        """)
        
        self.price_max = QLineEdit()
        self.price_max.setFixedWidth(50)
        self.price_max.setStyleSheet("""
            QLineEdit {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px;
                background-color: white;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
        """)
        
        # 价格排序单选按钮
        price_sort_label = QLabel("价格排序:")
        price_sort_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                border: none;
                background-color: transparent;
            }
        """)
        
        self.price_asc_radio = QRadioButton("左边")
        self.price_asc_radio.setChecked(True)
        self.price_asc_radio.setStyleSheet("font-family: 'Microsoft YaHei'; font-size: 12px;")
        
        self.price_desc_radio = QRadioButton("右边")
        self.price_desc_radio.setStyleSheet("font-family: 'Microsoft YaHei'; font-size: 12px;")
        
        # 价格筛选按钮
        price_filter_btn = QPushButton("价格筛选")
        price_filter_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px 10px;
                background-color: #f8f9fa;
                color: #495057;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                min-width: 70px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        price_filter_btn.clicked.connect(self.filter_by_price)
        # 添加调试：测试按钮点击
        price_filter_btn.clicked.connect(lambda: print("价格筛选按钮点击事件触发"))
        
        # 重上架按钮
        reup_btn = QPushButton("重上架")
        reup_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px 8px;
                background-color: #f5f5f5;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e5e5e5;
            }
        """)
        
        # 上架链接按钮
        up_link_btn = QPushButton("上架链接")
        up_link_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px 8px;
                background-color: #f5f5f5;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e5e5e5;
            }
        """)
        
        # 近30天复选框
        self.near_30_check = QCheckBox("近30天")
        self.near_30_check.setStyleSheet("font-family: 'Microsoft YaHei'; font-size: 12px;")
        
        # 近90天复选框
        self.near_90_check = QCheckBox("近90天")
        self.near_90_check.setStyleSheet("font-family: 'Microsoft YaHei'; font-size: 12px;")
        
        # 访问量输入框
        visit_label = QLabel("天数:")
        visit_label.setStyleSheet("""
            QLabel {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                border: none;
                background-color: transparent;
            }
        """)
        
        self.visit_input = QLineEdit()
        self.visit_input.setFixedWidth(50)
        self.visit_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px;
                background-color: white;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
        """)
        
        # 载入按钮
        load_btn = QPushButton("载入")
        load_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px 10px;
                background-color: #f8f9fa;
                color: #495057;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        
        # 改标题按钮
        change_title_btn = QPushButton("改标题")
        change_title_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px 10px;
                background-color: #f8f9fa;
                color: #495057;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                min-width: 70px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        
        # 停止按钮
        stop_btn = QPushButton("停止")
        stop_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px 10px;
                background-color: #f8f9fa;
                color: #495057;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        
        # 底部控制区域 - 设置整体宽度为1240px
        bottom_frame = QFrame()
        bottom_frame.setFixedHeight(110)  # 稍微增加高度
        bottom_frame.setFixedWidth(1250)  # 设置整体宽度为1240px
        bottom_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-left: 1px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                border-bottom: 1px solid #dee2e6;
                border-radius: 0px 0px 6px 6px;
            }
        """)

        # 底部控制区域布局 - 优化内边距以充分利用1240px宽度
        bottom_layout = QVBoxLayout(bottom_frame)
        bottom_layout.setContentsMargins(10, 12, 10, 8)  # 减少左右内边距
        bottom_layout.setSpacing(8)  # 增加行间距

        # 第一行筛选控件 - 增加间距以充分利用宽度
        first_row_layout = QHBoxLayout()
        first_row_layout.setSpacing(15)  # 进一步增加控件间距

        # 第二行筛选控件 - 增加间距以充分利用宽度
        second_row_layout = QHBoxLayout()
        second_row_layout.setSpacing(15)  # 进一步增加控件间距

        # 将控件分配到两行布局中

        # 第一行：日期筛选、销量筛选、价格筛选
        first_row_layout.addWidget(start_label)
        first_row_layout.addWidget(self.start_date)
        first_row_layout.addWidget(end_label)
        first_row_layout.addWidget(self.end_date)
        first_row_layout.addWidget(time_filter_btn)

        first_row_layout.addWidget(sales_label)
        first_row_layout.addWidget(self.sales_min)
        first_row_layout.addWidget(sales_dash)
        first_row_layout.addWidget(self.sales_max)
        first_row_layout.addWidget(sales_filter_btn)

        first_row_layout.addWidget(price_label)
        first_row_layout.addWidget(self.price_min)
        first_row_layout.addWidget(price_dash)
        first_row_layout.addWidget(self.price_max)
        first_row_layout.addWidget(price_filter_btn)

        # 添加清除筛选按钮用于测试
        clear_filter_btn = QPushButton("清除筛选")
        clear_filter_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px 10px;
                background-color: #f8f9fa;
                color: #495057;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                min-width: 70px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        clear_filter_btn.clicked.connect(self.clear_filters)
        first_row_layout.addWidget(clear_filter_btn)

        first_row_layout.addSpacing(20)  # 添加固定间距
        first_row_layout.addStretch()  # 第一行末尾添加弹性空间

        # 第二行：价格排序、操作按钮、复选框等
        second_row_layout.addWidget(price_sort_label)
        second_row_layout.addWidget(self.price_asc_radio)
        second_row_layout.addWidget(self.price_desc_radio)
        second_row_layout.addSpacing(15)  # 添加间距

        second_row_layout.addWidget(reup_btn)
        second_row_layout.addWidget(up_link_btn)
        second_row_layout.addSpacing(15)  # 添加间距

        second_row_layout.addWidget(self.near_30_check)
        second_row_layout.addWidget(self.near_90_check)
        second_row_layout.addSpacing(15)  # 添加间距

        second_row_layout.addWidget(visit_label)
        second_row_layout.addWidget(self.visit_input)
        second_row_layout.addSpacing(15)  # 添加间距

        second_row_layout.addWidget(load_btn)
        second_row_layout.addWidget(change_title_btn)
        second_row_layout.addWidget(stop_btn)
        second_row_layout.addStretch()  # 第二行末尾添加弹性空间

        # 将两行布局添加到底部区域
        bottom_layout.addLayout(first_row_layout)
        bottom_layout.addLayout(second_row_layout)

        # 创建底部容器，确保1240px宽度显示
        bottom_container = QWidget()
        bottom_container.setFixedWidth(1245)
        bottom_container_layout = QHBoxLayout(bottom_container)
        bottom_container_layout.setContentsMargins(0, 0, 0, 0)
        bottom_container_layout.addWidget(bottom_frame)

        # 将底部容器添加到右侧区域 - 右对齐
        right_layout.addWidget(bottom_container, 0, Qt.AlignRight)

        # 添加所有部件到主布局
        main_layout.addWidget(content_widget, 1)  # 主内容区域（包含左侧分类树和右侧工具栏表格底部筛选）

        # 初始化加载管理器
        self.loading_thread = None
        self.estimated_total_products = 0  # 预估总商品数量

    def setup_custom_scrollbars(self):
        """设置自定义滚动条"""
        try:
            # 创建自定义垂直滚动条
            custom_v_scrollbar = CustomScrollBar(Qt.Vertical, self.product_table)
            self.product_table.setVerticalScrollBar(custom_v_scrollbar)

            # 创建自定义水平滚动条
            custom_h_scrollbar = CustomScrollBar(Qt.Horizontal, self.product_table)
            self.product_table.setHorizontalScrollBar(custom_h_scrollbar)

            print("已设置自定义滚动条")

        except Exception as e:
            print(f"设置自定义滚动条失败: {str(e)}")

    def load_products_by_status(self, manager_tab, status_name):
        """根据商品状态加载商品"""
        try:
            # 获取选中的店铺
            checked_shops = self.category_tree.get_checked_shops()
            
            if not checked_shops:
                self.status_label.setText("请先选择要加载的店铺")
                return
            
            # 清空表格
            self.product_table.setRowCount(0)
            
            # **禁用排序功能，防止在加载过程中触发排序导致数据不稳定**
            self.product_table.setSortingEnabled(False)
            print(f"已禁用表格排序功能，开始加载{status_name}商品")
            
            # 重置页面级进度跟踪变量
            self.total_pages_all_shops = 0
            self.completed_pages_count = 0
            self.shop_pages_info = {}
            self.estimated_total_products = 0
            
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 100)  # 设置为百分比范围
            self.progress_bar.setValue(0)
            # 强制刷新界面
            QApplication.processEvents()
            
            # 更新状态
            self.status_label.setText(f"开始加载 {len(checked_shops)} 个店铺的{status_name}商品...")
            
            # 修改商品列表按钮为停止按钮
            self.product_list_btn.setText("停止加载")
            self.product_list_btn.setStyleSheet("""
                QPushButton {
                    border: 1px solid #d32f2f;
                    border-radius: 2px;
                    padding: 3px 8px;
                    background-color: #ffebee;
                    color: #d32f2f;
                    font-family: 'Microsoft YaHei';
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #ffcdd2;
                }
            """)
            
            # 创建并启动加载管理器，传递商品状态参数
            self.loading_thread = ProductLoadingThread(checked_shops, manager_tab)
            self.loading_thread.progress_updated.connect(self.update_loading_progress)
            self.loading_thread.data_loaded.connect(self.on_products_loaded)
            self.loading_thread.page_loaded.connect(self.on_page_loaded)
            self.loading_thread.total_pages_info.connect(self.on_shop_pages_info)  # 连接新的页面信息信号
            self.loading_thread.error_occurred.connect(self.on_loading_error)
            self.loading_thread.start()
            
        except Exception as e:
            print(f"启动{status_name}商品加载时发生错误: {str(e)}")
            self.status_label.setText(f"启动{status_name}加载失败: {str(e)}")
            self.reset_loading_state()

    def load_products_from_selected_shops(self):
        """从选中的店铺加载商品（默认加载在售商品）"""
        # 检查是否正在加载中
        if hasattr(self, 'loading_thread') and self.loading_thread and self.loading_thread.isRunning():
            # 正在加载中，直接停止当前加载，不重新开始
            print("检测到正在加载中，停止当前加载")
            self.stop_loading()
            return

        self.load_products_by_status("ON_SALE", "在售")

    def handle_product_list_btn_click(self):
        """处理商品列表按钮点击事件"""
        try:
            # 根据按钮当前文字判断应该执行什么操作
            if self.product_list_btn.text() == "停止加载":
                # 当前是停止加载状态，执行停止操作
                self.stop_loading()
            else:
                # 当前是商品列表状态，执行加载操作
                self.load_products_from_selected_shops()

        except Exception as e:
            print(f"处理按钮点击失败: {str(e)}")

    def on_shop_pages_info(self, shop_name, total_pages, total_products):
        """处理店铺页面信息，计算总页数和总商品数"""
        try:
            print(f"🔍 [DEBUG] on_shop_pages_info 被调用: 店铺={shop_name}, 总页数={total_pages}, 总商品数={total_products}")

            # 记录店铺页面信息
            self.shop_pages_info[shop_name] = {
                'total_pages': total_pages,
                'completed_pages': 0,
                'total_products': total_products
            }

            # 累加总页数和总商品数
            self.total_pages_all_shops += total_pages
            self.estimated_total_products += total_products

            print(f"店铺 {shop_name}: {total_pages}页, {total_products}个商品")
            print(f"累计总页数: {self.total_pages_all_shops}, 累计总商品数: {self.estimated_total_products}")
            print(f"🔍 [DEBUG] 当前进度条状态: 总页数={self.total_pages_all_shops}, 已完成页数={self.completed_pages_count}")

            # 更新状态显示
            self.status_label.setText(f"收集页面信息... 总计 {self.total_pages_all_shops} 页, {self.estimated_total_products} 个商品")

        except Exception as e:
            print(f"处理店铺页面信息失败: {str(e)}")

    def update_loading_progress(self, message):
        """更新加载进度"""
        self.status_label.setText(message)
        print(f"加载进度: {message}")
    
    def on_page_loaded(self, products, current_page, total_pages, shop_name):
        """单页商品加载完成回调"""
        try:
            print(f"🔍 [DEBUG] on_page_loaded 被调用: 店铺={shop_name}, 页面={current_page}/{total_pages}, 商品数={len(products)}")

            # 增量添加商品到表格
            self.add_products_to_table(products)

            # 更新店铺的页面完成情况
            if shop_name in self.shop_pages_info:
                # 检查该页面是否已经记录（避免重复计数）
                if current_page > self.shop_pages_info[shop_name]['completed_pages']:
                    self.shop_pages_info[shop_name]['completed_pages'] = current_page
                    # 重新计算总的已完成页面数
                    self.completed_pages_count = sum(info['completed_pages'] for info in self.shop_pages_info.values())
                    print(f"🔍 [DEBUG] 更新页面完成状态: 店铺{shop_name}完成{current_page}页, 总完成页数={self.completed_pages_count}")

            print(f"🔍 [DEBUG] 进度计算前: 已完成页数={self.completed_pages_count}, 总页数={self.total_pages_all_shops}")

            # 计算基于页面的准确进度
            if self.total_pages_all_shops > 0:
                page_progress = int((self.completed_pages_count / self.total_pages_all_shops) * 100)
                # 确保进度条能达到100%，不再限制在95%
                progress_value = min(100, page_progress)
                print(f"🔍 [DEBUG] 计算进度: {page_progress}% -> {progress_value}%")

                # 平滑更新进度条，避免跳跃式更新
                current_value = self.progress_bar.value()
                print(f"🔍 [DEBUG] 进度条状态: 当前值={current_value}%, 目标值={progress_value}%")

                if progress_value > current_value:
                    print(f"🔍 [DEBUG] 开始平滑更新进度条: {current_value}% -> {progress_value}%")
                    # 如果进度增加，逐步更新到目标值
                    for i in range(current_value + 1, progress_value + 1):
                        self.progress_bar.setValue(i)
                        QApplication.processEvents()
                        # 添加小延迟让用户能看到进度变化
                        if i % 5 == 0:  # 每5%暂停一下
                            QThread.msleep(10)
                    print(f"🔍 [DEBUG] 进度条更新完成: {self.progress_bar.value()}%")
                else:
                    # 直接设置值
                    self.progress_bar.setValue(progress_value)
                    QApplication.processEvents()
                    print(f"🔍 [DEBUG] 直接设置进度条: {progress_value}%")
            else:
                print(f"🔍 [DEBUG] 总页数为0，无法计算进度")
            
            # 获取当前商品总数
            current_row_count = self.product_table.rowCount()
            
            # 更新状态标签 - 显示更详细的进度信息
            self.status_label.setText(f"加载中... 店铺: {shop_name}, 第{current_page}/{total_pages}页 | 总进度: {self.completed_pages_count}/{self.total_pages_all_shops}页 | 已加载{current_row_count}个商品")
            print(f"页面进度: 店铺{shop_name}, 第{current_page}页完成, 总进度: {self.completed_pages_count}/{self.total_pages_all_shops}页, 已加载{current_row_count}个商品")
        except Exception as e:
            print(f"页面加载回调失败: {str(e)}")

    def on_products_loaded(self, products, summary):
        """商品加载完成回调 - 添加数据完整性验证"""
        try:
            # 数据完整性验证
            expected_count = len(products) if products else 0
            table_rows = self.product_table.rowCount()
            
            print(f"加载完成数据验证: 期望{expected_count}个商品，表格显示{table_rows}行")
            
            # 如果数据不一致，给出警告但继续处理
            if expected_count != table_rows:
                print(f"警告: 数据不一致！加载线程返回{expected_count}个商品，但表格只有{table_rows}行")
                # 使用实际表格行数作为准确数据
                actual_count = table_rows
            else:
                actual_count = table_rows
                print(f"数据完整性验证通过: {actual_count}个商品")
            
            # 更新进度条到100%
            self.progress_bar.setValue(100)
            
            # **重新启用排序功能**
            self.product_table.setSortingEnabled(True)
            print("已启用表格排序功能，商品加载完成")
            
            # 更新状态标签和商品数量标签 - 使用验证后的实际数量
            if expected_count != table_rows:
                self.status_label.setText(f"加载完成！实际加载 {actual_count} 个商品 (数据不一致警告)，已按累销降序排列")
            else:
                self.status_label.setText(f"加载完成！共加载 {actual_count} 个商品，已按累销降序排列")

            self.update_product_count_display()

            # 默认按累销降序排列（第5列，降序）
            if actual_count > 0:
                self.product_table.sortItems(5, Qt.DescendingOrder)
                print(f"已按累销降序排列{actual_count}个商品")
            
            # 检查是否需要查询上家信息
            if hasattr(self, 'is_supplier_query_mode') and self.is_supplier_query_mode:
                self.is_supplier_query_mode = False  # 重置标记
                print("商品加载完成，开始上家查询模式")
                self.start_supplier_query()
            else:
                # 只有在非查询模式下才加载上家信息缓存（静默加载，不自动查询）
                print("商品加载完成，开始静默加载已有的上家信息缓存")
                self.load_supplier_info_from_cache()

                # 禁用自动上家查询，避免无限循环
                print("商品加载完成，上家信息缓存已加载，如需查询上家信息请手动触发")
            
            print(f"所有商品加载完成: {summary}")
            print(f"最终状态: 表格{actual_count}行，期望{expected_count}个商品")
            
        except Exception as e:
            print(f"商品加载完成回调失败: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            # 重置加载状态
            self.reset_loading_state()

    def add_products_to_table(self, products):
        """增量添加商品到表格"""
        try:
            current_row_count = self.product_table.rowCount()
            new_row_count = current_row_count + len(products)
            
            # 扩展表格行数
            self.product_table.setRowCount(new_row_count)
            
            for i, product in enumerate(products):
                row = current_row_count + i
                
                # 提取商品信息
                item_desc = product.get('itemDesc', {})
                title_info = item_desc.get('title', {})
                
                # 序号列：复选框 + 序号
                checkbox_number_widget = CheckBoxNumberWidget(row + 1)
                # 连接复选框变化事件
                checkbox_number_widget.checkbox.stateChanged.connect(self.update_product_count_display)
                self.product_table.setCellWidget(row, 0, checkbox_number_widget)
                
                # 商品标题 - 使用文字排序
                title = title_info.get('text', '')
                title_item = QTableWidgetItem(title)
                title_item.setToolTip(title)  # 设置工具提示显示完整标题
                self.product_table.setItem(row, 1, title_item)
                
                # 商品ID
                item_id = str(product.get('itemId', ''))
                self.product_table.setItem(row, 2, QTableWidgetItem(item_id))
                
                # 价格 (直接使用原始价格，不需要除以100) - 使用数值排序
                price_info = product.get('managerPrice', {})
                price_list = price_info.get('price', [])
                if price_list:
                    try:
                        # 确保价格数据是数字类型
                        numeric_prices = []
                        for price in price_list:
                            if isinstance(price, str):
                                # 尝试将字符串转换为数字
                                try:
                                    numeric_prices.append(float(price))
                                except ValueError:
                                    # 如果转换失败，跳过这个价格
                                    continue
                            elif isinstance(price, (int, float)):
                                numeric_prices.append(float(price))
                        
                        if numeric_prices:
                            min_price = min(numeric_prices)
                            max_price = max(numeric_prices)
                            if min_price == max_price:
                                price_text = f"¥{min_price:.2f}"
                            else:
                                price_text = f"¥{min_price:.2f}-{max_price:.2f}"
                        else:
                            price_text = "¥0.00"
                    except Exception as e:
                        print(f"处理价格数据时发生错误: {str(e)}")
                        price_text = "¥0.00"
                else:
                    price_text = "¥0.00"
                self.product_table.setItem(row, 3, NumericTableWidgetItem(price_text))
                
                # 图片（显示图片链接或缩略图）
                main_images = item_desc.get('mainImage', [])
                if main_images:
                    # 显示第一张图片的URL
                    image_url = main_images[0]
                    self.product_table.setItem(row, 4, QTableWidgetItem(image_url))
                else:
                    self.product_table.setItem(row, 4, QTableWidgetItem(""))
                
                # 累计销量 (使用正确的字段路径) - 使用数值排序
                sold_quantity_info = product.get('soldQuantityPromotionComplex', {})
                volume = str(sold_quantity_info.get('soldQuantityPromotion', '0'))
                self.product_table.setItem(row, 5, NumericTableWidgetItem(volume))
                
                # 创建时间 - 使用日期排序，格式化为 "2025-03-21 17:18"
                create_time = product.get('createTime', '')
                if create_time:
                    try:
                        # createTime是字符串格式："2024-10-02 12:00:02"
                        if isinstance(create_time, str):
                            # 解析时间字符串并重新格式化
                            from datetime import datetime
                            if len(create_time) >= 19:  # 完整格式 "YYYY-MM-DD HH:MM:SS"
                                dt = datetime.strptime(create_time[:19], '%Y-%m-%d %H:%M:%S')
                                create_time_str = dt.strftime('%Y-%m-%d %H:%M')  # "2025-03-21 17:18"
                            elif len(create_time) >= 16:  # 格式 "YYYY-MM-DD HH:MM"
                                dt = datetime.strptime(create_time[:16], '%Y-%m-%d %H:%M')
                                create_time_str = dt.strftime('%Y-%m-%d %H:%M')  # "2025-03-21 17:18"
                            else:
                                create_time_str = create_time
                        else:
                            # 如果不是字符串，转换为字符串
                            create_time_str = str(create_time)
                    except Exception as e:
                        print(f"处理创建时间时发生错误: {str(e)}")
                        # 如果解析失败，尝试直接截取
                        if isinstance(create_time, str) and len(create_time) >= 16:
                            create_time_str = create_time[:16]  # 回退到原来的逻辑
                        else:
                            create_time_str = str(create_time)
                else:
                    create_time_str = ""
                self.product_table.setItem(row, 6, DateTableWidgetItem(create_time_str))
                
                # 库存 - 使用数值排序
                stock_info = product.get('managerItemStock', {})
                stock = str(stock_info.get('quantity', '0'))
                self.product_table.setItem(row, 7, NumericTableWidgetItem(stock))
                
                # 操作 - 默认为空
                self.product_table.setItem(row, 8, QTableWidgetItem(""))
                
                # 店铺
                shop_name = product.get('shop_name', '')
                self.product_table.setItem(row, 9, QTableWidgetItem(shop_name))
                
                # 状态 - 显示详细的审核失败信息
                status_text = product.get('status', {}).get('statusText', '')

                # 如果是审核失败状态，显示详细的失败原因
                text_with_status = product.get('textWithStatus', {})
                sub_items = text_with_status.get('subItems', [])

                if sub_items and status_text in ['审核待修改', '审核不通过', '审核失败']:
                    # 获取详细的审核失败描述
                    for sub_item in sub_items:
                        description = sub_item.get('description', '')
                        if description:
                            status_text = description
                            break

                status_item = QTableWidgetItem(status_text)
                status_item.setToolTip(status_text)  # 设置工具提示显示完整状态信息
                self.product_table.setItem(row, 10, status_item)
                
                # 点击量（暂时留空）
                self.product_table.setItem(row, 11, QTableWidgetItem(""))
                
                # 访问量（新增列，暂时留空）
                self.product_table.setItem(row, 12, QTableWidgetItem(""))
                
                # 类目（新增列，暂时留空）
                self.product_table.setItem(row, 13, QTableWidgetItem(""))

                # 上家（原来的上架列，暂时留空）
                self.product_table.setItem(row, 14, QTableWidgetItem(""))

                # 上家名称（新增列，暂时留空）
                self.product_table.setItem(row, 15, QTableWidgetItem(""))
            
            print(f"增量添加 {len(products)} 个商品到表格，当前共 {new_row_count} 条记录")
            
            # 更新商品数量显示
            self.update_product_count_display()
            
        except Exception as e:
            print(f"增量添加商品到表格时发生错误: {str(e)}")
            raise

    def on_loading_error(self, error_message):
        """加载错误回调"""
        try:
            # **发生错误时也要重新启用排序**
            self.product_table.setSortingEnabled(True)
            print("加载错误时重新启用表格排序功能")
            
            # 更新状态标签而不是弹出对话框
            self.status_label.setText(f"加载失败: {error_message}")
            
            # 检查是否为登录失效错误
            if "登录失效" in error_message or "重新登录" in error_message or "Cookie是否过期" in error_message:
                # 温和提示用户重新登录
                print(f"检测到登录失效: {error_message}")
                self.status_label.setText("登录已失效，请重新登录或检查账号配置")
                
                # 使用非阻塞的信息提示
                QTimer.singleShot(100, lambda: self.show_login_expired_warning(error_message))
            else:
                # 其他错误显示在状态栏
                print(f"商品加载错误: {error_message}")
                
        except Exception as e:
            print(f"错误处理异常: {str(e)}")
        finally:
            # 确保重置加载状态
            self.reset_loading_state()
    
    def show_login_expired_warning(self, error_message):
        """显示登录失效警告（非阻塞）"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtCore import Qt
            
            msg_box = QMessageBox(self)
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setWindowTitle("登录失效提醒")
            msg_box.setText("检测到登录已失效，请重新登录。")
            msg_box.setDetailedText(f"详细错误信息: {error_message}")
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.setWindowModality(Qt.NonModal)  # 设置为非模态
            msg_box.show()  # 使用show而不是exec_，避免阻塞
            
        except Exception as e:
            print(f"显示登录失效警告失败: {str(e)}")

    def stop_loading(self):
        """停止当前的商品加载过程 - 简化版，避免界面卡顿"""
        try:
            if hasattr(self, 'loading_thread') and self.loading_thread and self.loading_thread.isRunning():
                print("用户请求停止加载...")

                # 设置停止标志
                self.loading_thread.should_stop = True

                # 立即更新UI状态，不调用reset_loading_state()避免卡顿
                self.product_list_btn.setText("商品列表")
                self.product_list_btn.setStyleSheet("""
                    QPushButton {
                        border: 1px solid #d0d0d0;
                        border-radius: 2px;
                        padding: 3px 8px;
                        background-color: #f5f5f5;
                        font-family: 'Microsoft YaHei';
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #e5e5e5;
                    }
                """)
                self.product_list_btn.setEnabled(True)

                # 立即隐藏进度条
                self.progress_bar.setVisible(False)

                # 更新状态
                self.status_label.setText("加载已停止")

                print("商品加载已停止")

        except Exception as e:
            print(f"停止加载失败: {str(e)}")
            # 不显示错误弹窗，避免阻塞

    def reset_loading_state(self):
        """重置加载状态"""
        # 恢复商品列表按钮的原始状态
        self.product_list_btn.setText("商品列表")
        self.product_list_btn.setStyleSheet("""
            QPushButton {
                border: 1px solid #d0d0d0;
                border-radius: 2px;
                padding: 3px 8px;
                background-color: #f5f5f5;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e5e5e5;
            }
        """)
        self.product_list_btn.setEnabled(True)

        # 添加最小显示时间，确保用户能看到进度条
        def hide_progress_bar():
            self.progress_bar.setVisible(False)

        # 延迟500毫秒后隐藏进度条
        QTimer.singleShot(500, hide_progress_bar)
        
        # **如果排序被禁用，确保重新启用**
        if not self.product_table.isSortingEnabled():
            self.product_table.setSortingEnabled(True)
            print("重置加载状态时重新启用表格排序功能")
        
        if self.loading_thread:
            self.loading_thread.quit()
            self.loading_thread.wait()
            self.loading_thread = None

    def populate_product_table(self, products):
        """填充商品表格数据"""
        try:
            self.product_table.setRowCount(len(products))
            
            for row, product in enumerate(products):
                # 提取商品信息
                item_desc = product.get('itemDesc', {})
                title_info = item_desc.get('title', {})
                
                # 序号列：复选框 + 序号
                checkbox_number_widget = CheckBoxNumberWidget(row + 1)
                # 连接复选框变化事件
                checkbox_number_widget.checkbox.stateChanged.connect(self.update_product_count_display)
                self.product_table.setCellWidget(row, 0, checkbox_number_widget)
                
                # 商品标题 - 使用文字排序
                title = title_info.get('text', '')
                self.product_table.setItem(row, 1, QTableWidgetItem(title))
                
                # 商品ID
                item_id = str(product.get('itemId', ''))
                self.product_table.setItem(row, 2, QTableWidgetItem(item_id))
                
                # 价格 (直接使用原始价格，不需要除以100) - 使用数值排序
                price_info = product.get('managerPrice', {})
                price_list = price_info.get('price', [])
                if price_list:
                    try:
                        # 确保价格数据是数字类型
                        numeric_prices = []
                        for price in price_list:
                            if isinstance(price, str):
                                # 尝试将字符串转换为数字
                                try:
                                    numeric_prices.append(float(price))
                                except ValueError:
                                    # 如果转换失败，跳过这个价格
                                    continue
                            elif isinstance(price, (int, float)):
                                numeric_prices.append(float(price))
                        
                        if numeric_prices:
                            min_price = min(numeric_prices)
                            max_price = max(numeric_prices)
                            if min_price == max_price:
                                price_text = f"¥{min_price:.2f}"
                            else:
                                price_text = f"¥{min_price:.2f}-{max_price:.2f}"
                        else:
                            price_text = "¥0.00"
                    except Exception as e:
                        print(f"处理价格数据时发生错误: {str(e)}")
                        price_text = "¥0.00"
                else:
                    price_text = "¥0.00"
                self.product_table.setItem(row, 3, NumericTableWidgetItem(price_text))
                
                # 图片（显示图片链接或缩略图）
                main_images = item_desc.get('mainImage', [])
                if main_images:
                    # 显示第一张图片的URL
                    image_url = main_images[0]
                    self.product_table.setItem(row, 4, QTableWidgetItem(image_url))
                else:
                    self.product_table.setItem(row, 4, QTableWidgetItem(""))
                
                # 累计销量 (使用正确的字段路径) - 使用数值排序
                sold_quantity_info = product.get('soldQuantityPromotionComplex', {})
                volume = str(sold_quantity_info.get('soldQuantityPromotion', '0'))
                self.product_table.setItem(row, 5, NumericTableWidgetItem(volume))
                
                # 创建时间 - 使用日期排序，格式化为 "2025-03-21 17:18"
                create_time = product.get('createTime', '')
                if create_time:
                    try:
                        # createTime是字符串格式："2024-10-02 12:00:02"
                        if isinstance(create_time, str):
                            # 解析时间字符串并重新格式化
                            from datetime import datetime
                            if len(create_time) >= 19:  # 完整格式 "YYYY-MM-DD HH:MM:SS"
                                dt = datetime.strptime(create_time[:19], '%Y-%m-%d %H:%M:%S')
                                create_time_str = dt.strftime('%Y-%m-%d %H:%M')  # "2025-03-21 17:18"
                            elif len(create_time) >= 16:  # 格式 "YYYY-MM-DD HH:MM"
                                dt = datetime.strptime(create_time[:16], '%Y-%m-%d %H:%M')
                                create_time_str = dt.strftime('%Y-%m-%d %H:%M')  # "2025-03-21 17:18"
                            else:
                                create_time_str = create_time
                        else:
                            # 如果不是字符串，转换为字符串
                            create_time_str = str(create_time)
                    except Exception as e:
                        print(f"处理创建时间时发生错误: {str(e)}")
                        # 如果解析失败，尝试直接截取
                        if isinstance(create_time, str) and len(create_time) >= 16:
                            create_time_str = create_time[:16]  # 回退到原来的逻辑
                        else:
                            create_time_str = str(create_time)
                else:
                    create_time_str = ""
                self.product_table.setItem(row, 6, DateTableWidgetItem(create_time_str))
                
                # 库存 - 使用数值排序
                stock_info = product.get('managerItemStock', {})
                stock = str(stock_info.get('quantity', '0'))
                self.product_table.setItem(row, 7, NumericTableWidgetItem(stock))
                
                # 操作 - 默认为空
                self.product_table.setItem(row, 8, QTableWidgetItem(""))
                
                # 店铺
                shop_name = product.get('_shop_name', product.get('shop_name', ''))
                self.product_table.setItem(row, 9, QTableWidgetItem(shop_name))
                
                # 状态 - 显示详细的审核失败信息
                status_text = product.get('status', {}).get('statusText', '')

                # 如果是审核失败状态，显示详细的失败原因
                text_with_status = product.get('textWithStatus', {})
                sub_items = text_with_status.get('subItems', [])

                if sub_items and status_text in ['审核待修改', '审核不通过', '审核失败']:
                    # 获取详细的审核失败描述
                    for sub_item in sub_items:
                        description = sub_item.get('description', '')
                        if description:
                            status_text = description
                            break

                self.product_table.setItem(row, 10, QTableWidgetItem(status_text))
                
                # 点击量（暂时留空）
                self.product_table.setItem(row, 11, QTableWidgetItem(""))
                
                # 访问量（新增列，暂时留空）
                self.product_table.setItem(row, 12, QTableWidgetItem(""))
                
                # 类目（新增列，暂时留空）
                self.product_table.setItem(row, 13, QTableWidgetItem(""))

                # 上家（原来的上架列，暂时留空）
                self.product_table.setItem(row, 14, QTableWidgetItem(""))

                # 上家名称（新增列，暂时留空）
                self.product_table.setItem(row, 15, QTableWidgetItem(""))
            
            print(f"商品表格填充完成，共 {len(products)} 条记录")
            
            # 更新商品数量显示
            self.update_product_count_display()

        except Exception as e:
            print(f"填充商品表格时发生错误: {str(e)}")
            raise

    def on_item_entered(self, item):
        """鼠标悬停在表格项上时显示完整内容的工具提示"""
        try:
            if item and item.text():
                # 设置工具提示为完整的单元格内容
                item.setToolTip(item.text())
        except Exception as e:
            print(f"设置工具提示时发生错误: {str(e)}")

    def populate_product_row(self, row, product):
        """填充单行商品数据"""
        try:
            # 提取商品信息
            item_desc = product.get('itemDesc', {})
            title_info = item_desc.get('title', {})

            # 序号列：复选框 + 序号
            checkbox_number_widget = CheckBoxNumberWidget(row + 1)
            # 连接复选框变化事件
            checkbox_number_widget.checkbox.stateChanged.connect(self.update_product_count_display)
            self.product_table.setCellWidget(row, 0, checkbox_number_widget)

            # 商品标题 - 使用文字排序
            title = title_info.get('text', '')
            self.product_table.setItem(row, 1, QTableWidgetItem(title))

            # 商品ID
            item_id = str(product.get('itemId', ''))
            self.product_table.setItem(row, 2, QTableWidgetItem(item_id))

            # 价格 (直接使用原始价格，不需要除以100) - 使用数值排序
            price_info = product.get('managerPrice', {})
            price_list = price_info.get('price', [])
            if price_list:
                try:
                    # 确保价格数据是数字类型
                    numeric_prices = []
                    for price in price_list:
                        if isinstance(price, str):
                            # 尝试将字符串转换为数字
                            try:
                                numeric_prices.append(float(price))
                            except ValueError:
                                # 如果转换失败，跳过这个价格
                                continue
                        elif isinstance(price, (int, float)):
                            numeric_prices.append(float(price))

                    if numeric_prices:
                        min_price = min(numeric_prices)
                        max_price = max(numeric_prices)
                        if min_price == max_price:
                            price_text = f"¥{min_price:.2f}"
                        else:
                            price_text = f"¥{min_price:.2f}-{max_price:.2f}"
                    else:
                        price_text = "¥0.00"
                except Exception as e:
                    print(f"处理价格数据时发生错误: {str(e)}")
                    price_text = "¥0.00"
            else:
                price_text = "¥0.00"
            self.product_table.setItem(row, 3, NumericTableWidgetItem(price_text))

            # 图片（显示图片链接或缩略图）
            main_images = item_desc.get('mainImage', [])
            if main_images:
                # 显示第一张图片的URL
                image_url = main_images[0]
                self.product_table.setItem(row, 4, QTableWidgetItem(image_url))
            else:
                self.product_table.setItem(row, 4, QTableWidgetItem(""))

            # 累计销量 (使用正确的字段路径) - 使用数值排序
            sold_quantity_info = product.get('soldQuantityPromotionComplex', {})
            volume = str(sold_quantity_info.get('soldQuantityPromotion', '0'))
            self.product_table.setItem(row, 5, NumericTableWidgetItem(volume))

            # 创建时间 - 使用日期排序，格式化为 "2025-03-21 17:18"
            create_time = product.get('createTime', '')
            if create_time:
                try:
                    # createTime是字符串格式："2024-10-02 12:00:02"
                    if isinstance(create_time, str):
                        # 解析时间字符串并重新格式化
                        from datetime import datetime
                        if len(create_time) >= 19:  # 完整格式 "YYYY-MM-DD HH:MM:SS"
                            dt = datetime.strptime(create_time[:19], '%Y-%m-%d %H:%M:%S')
                            create_time_str = dt.strftime('%Y-%m-%d %H:%M')  # "2025-03-21 17:18"
                        elif len(create_time) >= 16:  # 格式 "YYYY-MM-DD HH:MM"
                            dt = datetime.strptime(create_time[:16], '%Y-%m-%d %H:%M')
                            create_time_str = dt.strftime('%Y-%m-%d %H:%M')  # "2025-03-21 17:18"
                        else:
                            create_time_str = create_time
                    else:
                        # 如果不是字符串，转换为字符串
                        create_time_str = str(create_time)
                except Exception as e:
                    print(f"处理创建时间时发生错误: {str(e)}")
                    # 如果解析失败，尝试直接截取
                    if isinstance(create_time, str) and len(create_time) >= 16:
                        create_time_str = create_time[:16]  # 回退到原来的逻辑
                    else:
                        create_time_str = str(create_time)
            else:
                create_time_str = ""
            self.product_table.setItem(row, 6, DateTableWidgetItem(create_time_str))

            # 库存 - 使用数值排序
            stock_info = product.get('managerItemStock', {})
            stock = str(stock_info.get('quantity', '0'))
            self.product_table.setItem(row, 7, NumericTableWidgetItem(stock))

            # 操作 - 默认为空
            self.product_table.setItem(row, 8, QTableWidgetItem(""))

            # 店铺
            shop_name = product.get('_shop_name', product.get('shop_name', ''))
            self.product_table.setItem(row, 9, QTableWidgetItem(shop_name))

            # 状态 - 显示详细的审核失败信息
            status_text = product.get('status', {}).get('statusText', '')

            # 如果是审核失败状态，显示详细的失败原因
            text_with_status = product.get('textWithStatus', {})
            sub_items = text_with_status.get('subItems', [])
            if sub_items and status_text in ['审核待修改', '审核不通过', '审核失败']:
                # 获取详细的审核失败描述
                for sub_item in sub_items:
                    description = sub_item.get('description', '')
                    if description:
                        status_text = description
                        break

            self.product_table.setItem(row, 10, QTableWidgetItem(status_text))

            # 点击量（暂时留空）
            self.product_table.setItem(row, 11, QTableWidgetItem(""))

            # 访问量（新增列，暂时留空）
            self.product_table.setItem(row, 12, QTableWidgetItem(""))

            # 类目（新增列，暂时留空）
            self.product_table.setItem(row, 13, QTableWidgetItem(""))

            # 上家（原来的上架列，暂时留空）
            self.product_table.setItem(row, 14, QTableWidgetItem(""))

            # 上家名称（新增列，暂时留空）
            self.product_table.setItem(row, 15, QTableWidgetItem(""))

        except Exception as e:
            print(f"填充商品行 {row} 时发生错误: {str(e)}")

    def search_products(self):
        """搜索商品 - 支持商品ID和标题关键词搜索"""
        search_text = self.product_id_input.text().strip()
        if not search_text:
            QMessageBox.information(self, "提示", "请输入搜索关键词")
            return

        # 获取勾选的店铺
        checked_shops = self.category_tree.get_checked_shops()
        if not checked_shops:
            QMessageBox.information(self, "提示", "请先在左侧分类树中勾选要搜索的店铺")
            return

        # 清空当前表格
        self.product_table.setRowCount(0)

        # 禁用排序功能，防止在搜索过程中触发排序导致数据不稳定
        self.product_table.setSortingEnabled(False)

        # 更新状态和重置商品数量
        self.status_label.setText(f"正在搜索 '{search_text}'...")
        self.update_product_count_display()

        # 禁用搜索按钮和输入框，防止重复搜索
        self.product_id_input.setEnabled(False)
        search_btn = self.findChild(QPushButton, "search_btn")
        if search_btn:
            search_btn.setEnabled(False)

        # 判断是否为纯数字（商品ID）
        is_product_id = search_text.isdigit()

        # 获取"在售"复选框状态
        only_on_sale = self.on_sale_check.isChecked()

        # 创建并启动搜索任务
        self.search_thread = ProductSearchThread(checked_shops, search_text, is_product_id, only_on_sale)
        self.search_thread.search_progress.connect(self.update_search_progress)
        self.search_thread.search_completed.connect(self.on_search_completed)
        self.search_thread.error_occurred.connect(self.on_search_error)
        self.search_thread.partial_results.connect(self.on_partial_results)  # 连接实时结果信号
        self.search_thread.start()

        print(f"开始搜索: 关键词='{search_text}', 是否为商品ID={is_product_id}, 店铺数={len(checked_shops)}")

    def update_search_progress(self, message):
        """更新搜索进度"""
        self.status_label.setText(message)

    def on_partial_results(self, products, shop_name):
        """处理实时搜索结果"""
        try:
            if products:
                # 获取当前表格行数
                current_row_count = self.product_table.rowCount()

                # 为新商品添加行
                new_row_count = current_row_count + len(products)
                self.product_table.setRowCount(new_row_count)

                # 填充新商品数据
                for i, product in enumerate(products):
                    row = current_row_count + i
                    self.populate_product_row(row, product)

                # 更新状态显示和商品数量标签
                self.status_label.setText(f"已找到 {new_row_count} 条商品 - 来自店铺: {shop_name}")
                self.update_product_count_display()

                print(f"实时显示: 店铺 {shop_name} 找到 {len(products)} 条商品，当前总计 {new_row_count} 条")

        except Exception as e:
            print(f"处理实时搜索结果失败: {str(e)}")

    def on_search_completed(self, results, search_text, total_searched_shops):
        """搜索完成回调"""
        try:
            # 重新启用搜索功能
            self.product_id_input.setEnabled(True)
            search_btn = self.findChild(QPushButton, "search_btn")
            if search_btn:
                search_btn.setEnabled(True)

            # 获取当前表格中的商品数量（实时显示的结果）
            current_count = self.product_table.rowCount()

            if current_count > 0:
                # 重新启用排序功能
                self.product_table.setSortingEnabled(True)

                # 更新序号
                self.update_row_numbers()

                self.status_label.setText(f"搜索完成: 找到 {current_count} 条匹配商品 (搜索了 {total_searched_shops} 个店铺)")
                self.update_product_count_display()

                # 如果有结果，选中第一行
                self.product_table.selectRow(0)

            else:
                self.status_label.setText(f"未找到匹配的商品 (已搜索 {total_searched_shops} 个店铺)")
                self.update_product_count_display()

        except Exception as e:
            print(f"处理搜索结果失败: {str(e)}")
            self.status_label.setText(f"处理搜索结果失败: {str(e)}")

    def on_search_error(self, error_message):
        """搜索错误回调"""
        try:
            # 重新启用搜索功能
            self.product_id_input.setEnabled(True)
            search_btn = self.findChild(QPushButton, "search_btn")
            if search_btn:
                search_btn.setEnabled(True)

            self.status_label.setText(f"搜索失败: {error_message}")
            QMessageBox.warning(self, "搜索失败", error_message)

        except Exception as e:
            print(f"处理搜索错误失败: {str(e)}")

    def search_shops(self):
        """搜索店铺"""
        search_text = self.shop_input.text().strip()
        if not search_text:
            return
        
        # 在分类树中搜索店铺
        found_items = []
        for i in range(self.category_tree.tree_widget.topLevelItemCount()):
            top_item = self.category_tree.tree_widget.topLevelItem(i)
            for j in range(top_item.childCount()):
                child = top_item.child(j)
                if search_text.lower() in child.text(0).lower():
                    found_items.append(child)
        
        if found_items:
            # 选中第一个匹配项
            self.category_tree.tree_widget.setCurrentItem(found_items[0])
            self.category_tree.tree_widget.scrollToItem(found_items[0])
            self.status_label.setText(f"找到 {len(found_items)} 个匹配的店铺")
        else:
            self.status_label.setText("未找到匹配的店铺")

    def update_category_tree(self, categories, category_stores=None):
        """更新分类树显示
        
        Parameters:
        -----------
        categories : dict
            包含分类名称和对应数量的字典，如 {"女装": 5, "男装": 3}
        category_stores : dict, optional
            包含每个分类下的店铺名称列表，如 {"女装": ["店铺1", "店铺2"], "男装": ["店铺3"]}
        """
        # 确保分类树实例存在
        if hasattr(self, 'category_tree'):
            # 打印调试信息
            print(f"正在更新ProductManager中的分类树...")
            print(f"分类数: {len(categories)}")
            print(f"分类名称: {list(categories.keys())[:5]}")
            
            # 将分类数据传递给分类树组件进行更新
            self.category_tree.update_categories(categories, category_stores)
        else:
            print("错误: ProductManager中没有找到category_tree属性")

    def on_product_table_clicked(self, item):
        """表格点击事件处理 - 使用防抖机制延迟处理"""
        try:
            if not item:
                return

            # 取消之前的定时器
            self.click_timer.stop()

            # 保存当前点击的项
            self.pending_click_item = item

            # 启动防抖定时器，200ms后处理点击
            self.click_timer.start(200)

        except Exception as e:
            print(f"处理表格点击事件失败: {str(e)}")

    def process_delayed_click(self):
        """延迟处理点击事件 - 防抖后的实际处理"""
        try:
            if not self.pending_click_item:
                return

            item = self.pending_click_item
            self.pending_click_item = None

            # 获取点击的行号
            row = item.row()

            # 获取商品信息
            title_item = self.product_table.item(row, 1)  # 商品标题
            price_item = self.product_table.item(row, 3)  # 价格
            image_item = self.product_table.item(row, 4)  # 图片

            # 立即更新文本信息（不阻塞）
            if title_item:
                title = title_item.text()
                # 限制标题长度，避免显示过长
                if len(title) > 80:
                    title = title[:80] + "..."
                self.product_title_label.setText(title)
            else:
                self.product_title_label.setText("未知商品")

            if price_item:
                self.product_price_label.setText(price_item.text())
            else:
                self.product_price_label.setText("价格未知")

            # 异步处理商品图片
            if image_item and image_item.text():
                image_url = image_item.text()
                if image_url.startswith('http'):
                    # 显示加载提示并异步加载图片
                    self.product_image_label.setText("图片加载中...")
                    self.load_product_image_async(image_url)
                else:
                    filename = os.path.basename(image_url) if image_url else "未知文件"
                    self.product_image_label.setText(f"图片:\n{filename}")
            else:
                self.product_image_label.setText("暂无图片")

        except Exception as e:
            print(f"延迟处理点击事件失败: {str(e)}")
            self.product_title_label.setText("获取商品信息失败")
            self.product_price_label.setText("价格未知")
            self.product_image_label.setText("暂无图片")

    def load_product_image_async(self, image_url):
        """异步加载商品图片"""
        try:
            # 取消之前的图片加载
            if self.current_image_loader and self.current_image_loader.isRunning():
                self.current_image_loader.cancel()
                self.current_image_loader.wait(1000)  # 等待最多1秒

            # 创建新的图片加载器
            target_size = self.product_image_label.size()
            self.current_image_loader = ImageLoader(image_url, target_size)

            # 连接信号
            self.current_image_loader.imageLoaded.connect(self.on_image_loaded)
            self.current_image_loader.loadFailed.connect(self.on_image_load_failed)

            # 启动异步加载
            self.current_image_loader.start()

        except Exception as e:
            print(f"启动异步图片加载失败: {str(e)}")
            self.product_image_label.setText("图片加载失败")

    def on_image_loaded(self, pixmap):
        """图片加载成功回调"""
        try:
            self.product_image_label.setPixmap(pixmap)
            self.product_image_label.setText("")  # 清除文字
        except Exception as e:
            print(f"设置图片失败: {str(e)}")

    def on_image_load_failed(self, error_msg):
        """图片加载失败回调"""
        try:
            self.product_image_label.setText(f"图片加载失败:\n{error_msg}")
        except Exception as e:
            print(f"设置错误信息失败: {str(e)}")

    def load_product_image(self, image_url):
        """保留原方法名以兼容旧代码"""
        self.load_product_image_async(image_url)

    def on_search_text_changed(self, text):
        """搜索框文本变化时的实时搜索"""
        try:
            # 如果输入为空，显示所有分类
            if not text.strip():
                self.show_all_categories()
                return

            # 实时搜索分类树中的店铺名称
            self.filter_categories(text.strip())

        except Exception as e:
            print(f"实时搜索失败: {str(e)}")

    def filter_categories(self, search_text):
        """根据搜索文本过滤分类树"""
        try:
            if not hasattr(self, 'category_tree') or not self.category_tree:
                return

            # 获取分类树的树形控件
            tree_widget = self.category_tree.tree_widget

            # 遍历所有顶级分类项
            for i in range(tree_widget.topLevelItemCount()):
                top_item = tree_widget.topLevelItem(i)
                has_match = False

                # 检查顶级分类名称是否匹配
                if search_text.lower() in top_item.text(0).lower():
                    has_match = True
                    top_item.setHidden(False)
                    # 展开匹配的顶级分类
                    top_item.setExpanded(True)
                    # 显示所有子项
                    for j in range(top_item.childCount()):
                        child_item = top_item.child(j)
                        child_item.setHidden(False)
                else:
                    # 检查子分类是否有匹配
                    for j in range(top_item.childCount()):
                        child_item = top_item.child(j)
                        if search_text.lower() in child_item.text(0).lower():
                            has_match = True
                            child_item.setHidden(False)
                        else:
                            child_item.setHidden(True)

                    if has_match:
                        top_item.setHidden(False)
                        top_item.setExpanded(True)  # 展开有匹配子项的分类
                    else:
                        top_item.setHidden(True)

        except Exception as e:
            print(f"过滤分类失败: {str(e)}")

    def show_all_categories(self):
        """显示所有分类"""
        try:
            if not hasattr(self, 'category_tree') or not self.category_tree:
                return

            # 获取分类树的树形控件
            tree_widget = self.category_tree.tree_widget

            # 显示所有项目
            for i in range(tree_widget.topLevelItemCount()):
                top_item = tree_widget.topLevelItem(i)
                top_item.setHidden(False)

                # 显示所有子项
                for j in range(top_item.childCount()):
                    child_item = top_item.child(j)
                    child_item.setHidden(False)

                # 收起所有分类（恢复默认状态）
                top_item.setExpanded(False)

        except Exception as e:
            print(f"显示所有分类失败: {str(e)}")

    def on_category_tree_double_clicked(self, item, column):
        """分类树双击事件处理 - 进入店铺后台"""
        try:
            # 检查是否是店铺项（子项）
            if item.parent() is not None:
                # 获取店铺名称（包含商品数的格式）
                shop_display_name = item.text(0)

                # 提取纯店铺名称，去掉商品数部分 [1725/0]
                if '[' in shop_display_name and ']' in shop_display_name:
                    shop_name = shop_display_name.split('[')[0].strip()
                else:
                    shop_name = shop_display_name.strip()

                print(f"双击店铺: {shop_display_name} -> 提取店铺名称: {shop_name}")

                # 调用一键下单.py中的方法
                self.enter_shop_backend_by_name(shop_name)
            else:
                print("双击的是分类项，不是店铺项")

        except Exception as e:
            print(f"处理分类树双击事件失败: {str(e)}")

    def enter_shop_backend_by_name(self, shop_name):
        """通过店铺名称进入店铺后台 - 优化版本，避免UI阻塞"""
        try:
            print(f"正在进入店铺后台: {shop_name}")

            # 更新窗口标题显示状态
            if hasattr(self, 'parent') and self.parent and hasattr(self.parent, 'setWindowTitle'):
                original_title = self.parent.windowTitle()
                self.parent.setWindowTitle(f"{original_title} - 正在启动 {shop_name} 后台...")

            # 使用QTimer延迟执行，避免阻塞主界面
            QTimer.singleShot(100, lambda: self._async_enter_shop_backend(shop_name))

        except Exception as e:
            print(f"进入店铺后台失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"进入店铺后台失败: {str(e)}")

    def _async_enter_shop_backend(self, shop_name):
        """异步进入店铺后台的实际执行方法 - 简化版本，只使用一键下单模块"""
        try:
            # 直接使用一键下单模块（最稳定可靠的方法）
            self._use_one_click_order_module(shop_name)

        except Exception as e:
            print(f"异步进入店铺后台失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"进入店铺后台失败: {str(e)}")
            # 恢复窗口标题
            if hasattr(self, 'parent') and self.parent and hasattr(self.parent, 'setWindowTitle'):
                original_title = self.parent.windowTitle().split(' - ')[0]
                self.parent.setWindowTitle(original_title)

    def _use_one_click_order_module(self, shop_name):
        """使用一键下单模块打开店铺后台 - 优化版本"""
        try:
            # 查找现有的一键下单窗口
            one_click_window = None
            for widget in QApplication.instance().topLevelWidgets():
                if widget.__class__.__name__ == 'OneClickOrderWindow':
                    one_click_window = widget
                    print(f"找到现有的一键下单窗口")
                    break

            if one_click_window:
                # 使用现有窗口，延迟调用避免阻塞
                print(f"使用现有一键下单窗口打开店铺后台")
                QTimer.singleShot(200, lambda: one_click_window._direct_open_shop_backend(shop_name))
            else:
                # 创建新的一键下单窗口
                print(f"创建新的一键下单窗口")
                self._create_new_one_click_window(shop_name)

        except Exception as e:
            print(f"使用一键下单模块失败: {str(e)}")
            raise e

    def _create_new_one_click_window(self, shop_name):
        """创建新的一键下单窗口 - 简化版本"""
        try:
            # 动态导入一键下单模块
            import importlib.util
            import sys

            # 获取一键下单.py的路径
            one_click_order_path = os.path.join(os.path.dirname(__file__), '一键下单.py')

            if os.path.exists(one_click_order_path):
                # 动态导入一键下单模块
                spec = importlib.util.spec_from_file_location("one_click_order", one_click_order_path)
                one_click_order_module = importlib.util.module_from_spec(spec)

                # 将模块添加到sys.modules中，避免重复导入
                sys.modules["one_click_order"] = one_click_order_module
                spec.loader.exec_module(one_click_order_module)

                # 创建一键下单窗口实例
                one_click_window = one_click_order_module.OneClickOrderWindow()

                # 使用QTimer延迟调用，避免阻塞
                QTimer.singleShot(300, lambda: one_click_window._direct_open_shop_backend(shop_name))

                print(f"成功创建一键下单窗口并调用方法进入店铺后台: {shop_name}")

            else:
                print(f"一键下单.py文件不存在: {one_click_order_path}")
                QMessageBox.warning(self, "错误", "一键下单.py文件不存在")

        except Exception as e:
            print(f"创建一键下单窗口失败: {str(e)}")
            raise e

    def on_header_clicked(self, logical_index):
        """表头点击事件 - 排序后重新生成序号"""
        try:
            # 检查排序是否启用
            if not self.product_table.isSortingEnabled():
                print("🔧 [DEBUG] 表头点击时发现排序被禁用，正在重新启用...")
                self.product_table.setSortingEnabled(True)
                self.product_table.horizontalHeader().setSortIndicatorShown(True)
                self.product_table.horizontalHeader().setSectionsClickable(True)
                print("🔧 [DEBUG] 排序功能已重新启用")
            else:
                print(f"🔧 [DEBUG] 表头点击事件: 列{logical_index}, 排序已启用")

            # 使用QTimer延迟执行，确保排序完成后再更新序号
            QTimer.singleShot(100, self.update_row_numbers)
        except Exception as e:
            print(f"处理表头点击事件失败: {str(e)}")

    def on_table_layout_changed(self):
        """表格布局变化事件 - 排序完成后触发"""
        try:
            # 延迟更新序号，确保排序完全完成
            QTimer.singleShot(50, self.update_row_numbers)
        except Exception as e:
            print(f"处理表格布局变化事件失败: {str(e)}")

    def on_product_table_double_clicked(self, item):
        """商品表格双击事件 - 打开上家1688链接"""
        try:
            if not item:
                return

            row = item.row()
            print(f"🔧 [DEBUG] 双击表格行: {row}")

            # 获取商品数据
            product_data = self.get_product_data(row)
            if not product_data:
                print("🔧 [DEBUG] 未找到商品数据")
                return

            # 获取上家ID
            supplier_id = product_data.get('supplier', '').strip()
            if not supplier_id:
                print("🔧 [DEBUG] 该商品没有上家信息")
                QMessageBox.information(self, "提示", "该商品没有上家信息")
                return

            # 构造1688商品链接
            url = f"https://detail.1688.com/offer/{supplier_id}.html"
            print(f"🔧 [DEBUG] 准备打开1688链接: {url}")

            try:
                import webbrowser
                webbrowser.open(url)
                print(f"✅ 成功打开1688商品链接: {url}")
            except Exception as browser_error:
                # 如果打开浏览器失败，复制链接到剪贴板作为备选方案
                clipboard = QApplication.clipboard()
                clipboard.setText(url)
                print(f"⚠️ 浏览器打开失败，已复制链接到剪贴板: {url}")
                QMessageBox.information(self, "提示", f"浏览器打开失败，已复制链接到剪贴板:\n{url}")

        except Exception as e:
            print(f"处理表格双击事件失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"处理双击事件失败: {str(e)}")

    def update_row_numbers(self):
        """更新行序号"""
        try:
            for row in range(self.product_table.rowCount()):
                widget = self.product_table.cellWidget(row, 0)
                if isinstance(widget, CheckBoxNumberWidget):
                    # 保持复选框状态，只更新序号
                    checked_state = widget.isChecked()
                    new_widget = CheckBoxNumberWidget(row + 1)
                    new_widget.setChecked(checked_state)
                    # 连接复选框变化事件
                    new_widget.checkbox.stateChanged.connect(self.update_product_count_display)
                    self.product_table.setCellWidget(row, 0, new_widget)
            # 更新商品数量显示
            self.update_product_count_display()
        except Exception as e:
            print(f"更新行序号失败: {str(e)}")

    def get_selected_products_count(self):
        """获取选中的商品数量"""
        try:
            selected_count = 0
            for row in range(self.product_table.rowCount()):
                widget = self.product_table.cellWidget(row, 0)
                if isinstance(widget, CheckBoxNumberWidget) and widget.isChecked():
                    selected_count += 1
            return selected_count
        except Exception as e:
            print(f"获取选中商品数量失败: {str(e)}")
            return 0

    def get_visible_products_count(self):
        """获取可见商品数量（未被筛选隐藏的商品）"""
        try:
            visible_count = 0
            for row in range(self.product_table.rowCount()):
                if not self.product_table.isRowHidden(row):
                    visible_count += 1
            return visible_count
        except Exception as e:
            print(f"获取可见商品数量失败: {str(e)}")
            return 0

    def update_product_count_display(self):
        """更新商品数量显示"""
        try:
            total_count = self.product_table.rowCount()
            visible_count = self.get_visible_products_count()
            selected_count = self.get_selected_products_count()

            # 构建显示文本
            if visible_count < total_count:
                # 有筛选时显示：总数 | 筛选后 | 已选择
                display_text = f"商品总数: {total_count} | 筛选后: {visible_count} | 已选择: {selected_count}"
            else:
                # 无筛选时显示：总数 | 已选择
                display_text = f"商品总数: {total_count} | 已选择: {selected_count}"

            # 根据选中状态设置不同的颜色和样式
            if selected_count > 0:
                self.product_count_label.setText(display_text)
                self.product_count_label.setStyleSheet("""
                    QLabel {
                        font-family: 'Microsoft YaHei';
                        font-size: 12px;
                        color: #27ae60;
                        background-color: #d5f4e6;
                        border: 1px solid #27ae60;
                        border-radius: 4px;
                        padding: 4px 8px;
                        margin-left: 10px;
                        font-weight: 500;
                    }
                """)
            else:
                self.product_count_label.setText(display_text)
                self.product_count_label.setStyleSheet("""
                    QLabel {
                        font-family: 'Microsoft YaHei';
                        font-size: 12px;
                        color: #2c3e50;
                        background-color: #ecf0f1;
                        border: 1px solid #bdc3c7;
                        border-radius: 4px;
                        padding: 4px 8px;
                        margin-left: 10px;
                        font-weight: 500;
                    }
                """)
        except Exception as e:
            print(f"更新商品数量显示失败: {str(e)}")
            self.product_count_label.setText("商品总数: 0 | 已选择: 0")

    def show_context_menu(self, position):
        """显示右键菜单"""
        try:
            # 获取点击的项
            item = self.product_table.itemAt(position)
            if item is None:
                return

            # 创建右键菜单
            context_menu = QMenu(self)

            # 移除阴影效果，修复右下角圆角背景问题
            context_menu.setWindowFlags(context_menu.windowFlags() | Qt.NoDropShadowWindowHint)

            # 设置菜单样式
            context_menu.setStyleSheet("""
                QMenu {
                    background-color: white;
                    border: 1px solid #d0d0d0;
                    border-radius: 6px;
                    padding: 4px;
                    font-family: 'Microsoft YaHei';
                    font-size: 12px;
                    outline: none;
                }
                QMenu::item {
                    background-color: transparent;
                    padding: 6px 20px 6px 30px;
                    border: none;
                    border-radius: 3px;
                    margin: 1px 2px;
                    outline: none;
                }
                QMenu::item:selected {
                    background-color: #3E5FAE;
                    color: white;
                    border-radius: 3px;
                }
                QMenu::item:disabled {
                    color: #999999;
                }
                QMenu::separator {
                    height: 1px;
                    background-color: #e0e0e0;
                    margin: 3px 10px;
                    border: none;
                }
                QMenu::right-arrow {
                    width: 0px;
                    height: 0px;
                }
            """)

            # 添加菜单项（使用Unicode图标）
            context_menu.addAction("🔗 生成链接", lambda: QMessageBox.information(self, "提示", "生成链接功能开发中，敬请期待..."))
            context_menu.addAction("☑️ 全选", self.select_all_products)
            context_menu.addAction("☐ 取消选中", self.deselect_all_products)

            context_menu.addSeparator()

            context_menu.addAction("🆔 复制商品ID", self.copy_product_id)
            context_menu.addAction("📝 复制商品标题", self.copy_product_title)
            context_menu.addAction("🔗 复制商品链接", self.copy_product_link)
            context_menu.addAction("🏪 查看1688商品", self.view_1688_product)
            context_menu.addAction("🔗 复制上家链接 (支持批量)", self.copy_supplier_link)

            context_menu.addSeparator()

            context_menu.addAction("⬆️ 商品上架", self.product_on_shelf)
            context_menu.addAction("✏️ 编辑商品", self.edit_product)
            context_menu.addAction("⬇️ 商品下架", self.product_off_shelf)
            context_menu.addAction("🗑️ 商品删除", self.delete_product)

            # 显示菜单
            context_menu.exec_(self.product_table.mapToGlobal(position))

        except Exception as e:
            print(f"显示右键菜单失败: {str(e)}")

    def product_on_shelf(self):
        """商品上架"""
        print("=== 商品上架功能被调用 ===")
        try:
            self.list_product()
        except Exception as e:
            print(f"商品上架异常: {str(e)}")
            QMessageBox.critical(self, "错误", f"商品上架失败: {str(e)}")

    def product_off_shelf(self):
        """商品下架"""
        print("=== 商品下架功能被调用 ===")
        try:
            self.delist_product()
        except Exception as e:
            print(f"商品下架异常: {str(e)}")
            QMessageBox.critical(self, "错误", f"商品下架失败: {str(e)}")

    def delete_product(self):
        """商品删除"""
        print("=== 商品删除功能被调用 ===")
        try:
            self.delete_selected_products()
        except Exception as e:
            print(f"商品删除异常: {str(e)}")
            QMessageBox.critical(self, "错误", f"商品删除失败: {str(e)}")

    def get_effective_selected_rows(self):
        """获取有效选中的行（智能选择）

        逻辑：
        1. 优先使用复选框选中的行
        2. 如果没有复选框选中，则使用表格选中的行
        """
        # 先检查复选框选中的行
        checkbox_selected_rows = self.get_checkbox_selected_rows()

        if checkbox_selected_rows:
            # 如果有复选框选中，就使用复选框选择的行
            return checkbox_selected_rows, "复选框"
        else:
            # 如果没有复选框选中，就使用表格选中的行
            table_selected_rows = self.get_table_selected_rows()
            return table_selected_rows, "表格选择"

    def get_checkbox_selected_rows(self):
        """获取复选框选中的行（只包括可见行）"""
        selected_rows = []
        for row in range(self.product_table.rowCount()):
            # 只处理可见的行，跳过被筛选隐藏的行
            if self.product_table.isRowHidden(row):
                continue

            checkbox_widget = self.product_table.cellWidget(row, 0)
            if checkbox_widget and isinstance(checkbox_widget, CheckBoxNumberWidget):
                if checkbox_widget.isChecked():
                    selected_rows.append(row)
        return selected_rows

    def get_table_selected_rows(self):
        """获取表格选中的行（只包括可见行）"""
        selected_rows = []
        for item in self.product_table.selectedItems():
            row = item.row()
            # 只处理可见的行，跳过被筛选隐藏的行
            if not self.product_table.isRowHidden(row) and row not in selected_rows:
                selected_rows.append(row)
        return sorted(selected_rows)

    def update_operation_status(self, row, status):
        """更新指定行的操作状态"""
        try:
            # 操作状态列索引为8
            status_item = QTableWidgetItem(status)
            self.product_table.setItem(row, 8, status_item)

            # 调试信息：记录状态更新
            product_id_item = self.product_table.item(row, 2)  # 商品ID列
            product_id = product_id_item.text() if product_id_item else "未知"
            print(f"状态更新: 第{row+1}行 商品{product_id} -> {status}")

            # 立即刷新界面，确保状态更新显示（特别是第一个商品）
            QApplication.processEvents()

        except Exception as e:
            print(f"更新操作状态时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def clear_operation_status_for_rows(self, rows):
        """清空指定行的操作状态列"""
        try:
            for row in rows:
                # 操作状态列索引为8
                status_item = QTableWidgetItem("")
                self.product_table.setItem(row, 8, status_item)
                print(f"清空第{row+1}行的操作状态")

            # 立即刷新界面，确保清空操作生效
            QApplication.processEvents()

        except Exception as e:
            print(f"清空操作状态时出错: {str(e)}")

    def delist_product(self):
        """下架商品 - 复用详情统计中的完善实现"""
        # 获取表格选中的行（智能选择）
        selected_rows, selection_type = self.get_effective_selected_rows()
        if not selected_rows:
            print("没有选中任何行")
            QMessageBox.warning(self, "提示", "请先选择要下架的商品（可以勾选复选框或选择表格行）")
            return

        print(f"使用{selection_type}模式选中了 {len(selected_rows)} 行商品")

        # 获取商品ID列和店铺名称列的索引
        product_id_col_index = -1
        shop_name_col_index = -1

        for col in range(self.product_table.columnCount()):
            header_item = self.product_table.horizontalHeaderItem(col)
            if header_item:
                if header_item.text() == "商品ID":
                    product_id_col_index = col
                elif header_item.text() == "店铺":
                    shop_name_col_index = col

        if product_id_col_index == -1:
            print("未找到商品ID列")
            QMessageBox.warning(self, "错误", "未找到商品ID列")
            return

        if shop_name_col_index == -1:
            print("未找到店铺列")
            QMessageBox.warning(self, "错误", "未找到店铺列")
            return

        # 收集选中行的商品信息
        product_operations = []
        for row in selected_rows:
            product_id_item = self.product_table.item(row, product_id_col_index)
            shop_name_item = self.product_table.item(row, shop_name_col_index)

            if product_id_item and product_id_item.text() and shop_name_item and shop_name_item.text():
                product_id = product_id_item.text()
                shop_name_full = shop_name_item.text()

                # 处理店铺名称，去掉括号和分数（如 "佰俪品质女鞋(4.71)" -> "佰俪品质女鞋"）
                clean_shop_name = shop_name_full
                if '(' in shop_name_full and shop_name_full.endswith(')'):
                    clean_shop_name = shop_name_full.split('(')[0].strip()
                elif '（' in shop_name_full and shop_name_full.endswith('）'):
                    clean_shop_name = shop_name_full.split('（')[0].strip()

                product_operations.append({
                    'product_id': product_id,
                    'shop_name': clean_shop_name,
                    'row': row
                })

        if not product_operations:
            print("没有有效的商品信息")
            QMessageBox.warning(self, "错误", "没有找到有效的商品信息")
            return

        # 确认下架操作
        reply = QMessageBox.question(self, "确认下架",
                                   f"确定要下架 {len(product_operations)} 个商品吗？\n选择方式：{selection_type}",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply != QMessageBox.Yes:
            return

        # 禁用表格排序功能，防止下架过程中排序导致行号变化
        self.product_table.setSortingEnabled(False)
        print("已禁用表格排序功能，开始下架操作")

        # 清空选中行的操作状态列
        self.clear_operation_status_for_rows([op['row'] for op in product_operations])

        # 执行下架操作 - 直接调用详情统计中的完善实现
        self.perform_delist_operation(product_operations)

        # 重新启用表格排序功能
        self.product_table.setSortingEnabled(True)
        print("已重新启用表格排序功能，下架操作完成")

    def list_product(self):
        """上架商品"""
        try:
            # 获取表格选中的行（智能选择）
            selected_rows, selection_type = self.get_effective_selected_rows()
            if not selected_rows:
                QMessageBox.information(self, "提示", "请先选择要上架的商品（可以勾选复选框或选择表格行）")
                return

            print(f"使用{selection_type}模式选中了 {len(selected_rows)} 行商品")

            # 获取商品ID列和店铺名称列的索引
            product_id_col_index = -1
            shop_name_col_index = -1

            for col in range(self.product_table.columnCount()):
                header_item = self.product_table.horizontalHeaderItem(col)
                if header_item:
                    header_text = header_item.text()
                    if header_text == "商品ID":
                        product_id_col_index = col
                    elif header_text == "店铺":
                        shop_name_col_index = col

            if product_id_col_index == -1:
                QMessageBox.warning(self, "错误", "未找到商品ID列")
                return

            if shop_name_col_index == -1:
                QMessageBox.warning(self, "错误", "未找到店铺列")
                return

            # 构建商品操作列表
            product_operations = []

            for row in selected_rows:
                # 获取商品ID
                product_id_item = self.product_table.item(row, product_id_col_index)
                if not product_id_item or not product_id_item.text():
                    continue

                product_id = product_id_item.text().strip()

                # 获取店铺名称
                shop_name_item = self.product_table.item(row, shop_name_col_index)
                if not shop_name_item or not shop_name_item.text():
                    continue

                shop_name = shop_name_item.text().strip()

                # 清理店铺名称（去除评分括号）
                if '(' in shop_name and shop_name.endswith(')'):
                    shop_name = shop_name.split('(')[0].strip()

                # 添加商品操作信息
                product_operations.append({
                    'product_id': product_id,
                    'shop_name': shop_name,
                    'row': row
                })

            if not product_operations:
                QMessageBox.warning(self, "错误", "没有找到有效的商品信息")
                return

            # 确认操作
            reply = QMessageBox.question(
                self,
                "确认上架",
                f"将要上架 {len(product_operations)} 个商品\n选择方式：{selection_type}\n\n确定要继续吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 禁用表格排序功能，防止上架过程中排序导致行号变化
            self.product_table.setSortingEnabled(False)
            print("已禁用表格排序功能，开始上架操作")

            # 清空选中行的操作状态列
            self.clear_operation_status_for_rows(selected_rows)

            # 执行上架操作
            self.perform_list_operation(product_operations)

            # 重新启用表格排序功能
            self.product_table.setSortingEnabled(True)
            print("已重新启用表格排序功能，上架操作完成")

        except Exception as e:
            print(f"上架商品时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"上架操作失败: {str(e)}")

    def delete_selected_products(self):
        """删除选中的商品"""
        # 获取表格选中的行（智能选择）
        selected_rows, selection_type = self.get_effective_selected_rows()
        if not selected_rows:
            print("没有选中任何行")
            QMessageBox.warning(self, "提示", "请先选择要删除的商品（可以勾选复选框或选择表格行）")
            return

        print(f"使用{selection_type}模式选中了 {len(selected_rows)} 行商品")

        # 获取商品ID列和店铺名称列的索引
        product_id_col_index = -1
        shop_name_col_index = -1

        for col in range(self.product_table.columnCount()):
            header_item = self.product_table.horizontalHeaderItem(col)
            if header_item:
                if header_item.text() == "商品ID":
                    product_id_col_index = col
                elif header_item.text() == "店铺":
                    shop_name_col_index = col

        if product_id_col_index == -1:
            print("未找到商品ID列")
            QMessageBox.warning(self, "错误", "未找到商品ID列")
            return

        if shop_name_col_index == -1:
            print("未找到店铺列")
            QMessageBox.warning(self, "错误", "未找到店铺列")
            return

        # 收集选中行的商品信息
        product_operations = []
        for row in selected_rows:
            product_id_item = self.product_table.item(row, product_id_col_index)
            shop_name_item = self.product_table.item(row, shop_name_col_index)

            if product_id_item and product_id_item.text() and shop_name_item and shop_name_item.text():
                product_id = product_id_item.text()
                shop_name_full = shop_name_item.text()

                # 处理店铺名称，去掉括号和分数（如 "佰俪品质女鞋(4.71)" -> "佰俪品质女鞋"）
                clean_shop_name = shop_name_full
                if '(' in shop_name_full and shop_name_full.endswith(')'):
                    clean_shop_name = shop_name_full.split('(')[0].strip()
                elif '（' in shop_name_full and shop_name_full.endswith('）'):
                    clean_shop_name = shop_name_full.split('（')[0].strip()

                product_operations.append({
                    'product_id': product_id,
                    'shop_name': clean_shop_name,
                    'row': row
                })

        if not product_operations:
            print("没有有效的商品信息")
            QMessageBox.warning(self, "错误", "没有找到有效的商品信息")
            return

        # 确认删除操作（删除是危险操作，需要特别确认）
        reply = QMessageBox.question(self, "⚠️ 确认删除",
                                   f"⚠️ 警告：删除操作不可恢复！\n\n确定要删除 {len(product_operations)} 个商品吗？\n选择方式：{selection_type}\n\n删除后商品将无法恢复，请谨慎操作！",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)  # 默认选择"否"
        if reply != QMessageBox.Yes:
            return

        # 禁用表格排序功能，防止删除过程中排序导致行号变化
        self.product_table.setSortingEnabled(False)
        print("已禁用表格排序功能，开始删除操作")

        # 清空选中行的操作状态列
        self.clear_operation_status_for_rows([op['row'] for op in product_operations])

        # 执行删除操作
        self.perform_delete_operation(product_operations)

        # 重新启用表格排序功能
        self.product_table.setSortingEnabled(True)
        print("已重新启用表格排序功能，删除操作完成")

    def get_selected_row(self):
        """获取当前选中的行"""
        current_row = self.product_table.currentRow()
        if current_row >= 0:
            return current_row
        return None

    def get_product_data(self, row):
        """获取指定行的商品数据"""
        try:
            if row is None or row < 0:
                return None

            # 从表格中提取商品信息
            product_data = {
                'title': self.product_table.item(row, 1).text() if self.product_table.item(row, 1) else '',
                'id': self.product_table.item(row, 2).text() if self.product_table.item(row, 2) else '',
                'price': self.product_table.item(row, 3).text() if self.product_table.item(row, 3) else '',
                'shop_name': self.product_table.item(row, 9).text() if self.product_table.item(row, 9) else '',
                'status': self.product_table.item(row, 10).text() if self.product_table.item(row, 10) else '',
                'category': self.product_table.item(row, 13).text() if self.product_table.item(row, 13) else '',
                'supplier': self.product_table.item(row, 14).text() if self.product_table.item(row, 14) else ''
            }
            return product_data
        except Exception as e:
            print(f"获取商品数据失败: {str(e)}")
            return None

    # 选择功能
    def select_all_products(self):
        """全选所有可见商品（基于筛选后的内容）"""
        try:
            selected_count = 0
            for row in range(self.product_table.rowCount()):
                # 只操作可见的行，跳过被筛选隐藏的行
                if self.product_table.isRowHidden(row):
                    continue

                widget = self.product_table.cellWidget(row, 0)
                if isinstance(widget, CheckBoxNumberWidget):
                    widget.setChecked(True)
                    selected_count += 1
            # 更新商品数量显示
            self.update_product_count_display()
            print(f"已全选 {selected_count} 个可见商品（基于筛选后的内容）")
        except Exception as e:
            print(f"全选商品失败: {str(e)}")

    def deselect_all_products(self):
        """取消选中所有可见商品（基于筛选后的内容）"""
        try:
            deselected_count = 0
            for row in range(self.product_table.rowCount()):
                # 只操作可见的行，跳过被筛选隐藏的行
                if self.product_table.isRowHidden(row):
                    continue

                widget = self.product_table.cellWidget(row, 0)
                if isinstance(widget, CheckBoxNumberWidget):
                    widget.setChecked(False)
                    deselected_count += 1
            # 更新商品数量显示
            self.update_product_count_display()
            print(f"已取消选中 {deselected_count} 个可见商品（基于筛选后的内容）")
        except Exception as e:
            print(f"取消选中商品失败: {str(e)}")

    # 复制功能
    def copy_product_id(self):
        """复制商品ID"""
        try:
            row = self.get_selected_row()
            if row is not None:
                product_data = self.get_product_data(row)
                if product_data and product_data['id']:
                    clipboard = QApplication.clipboard()
                    clipboard.setText(product_data['id'])
                    # 无感提示：只在控制台输出，不弹窗
                    print(f"✅ 已复制商品ID: {product_data['id']}")
                else:
                    print("⚠️ 无法获取商品ID")
            else:
                print("⚠️ 请先选择一个商品")
        except Exception as e:
            print(f"❌ 复制商品ID失败: {str(e)}")

    def copy_product_title(self):
        """复制商品标题"""
        try:
            row = self.get_selected_row()
            if row is not None:
                product_data = self.get_product_data(row)
                if product_data and product_data['title']:
                    clipboard = QApplication.clipboard()
                    clipboard.setText(product_data['title'])
                    # 无感提示：只在控制台输出，不弹窗
                    print(f"✅ 已复制商品标题: {product_data['title'][:30]}...")
                else:
                    print("⚠️ 无法获取商品标题")
            else:
                print("⚠️ 请先选择一个商品")
        except Exception as e:
            print(f"❌ 复制商品标题失败: {str(e)}")

    def copy_product_link(self):
        """复制商品链接"""
        try:
            row = self.get_selected_row()
            if row is not None:
                product_data = self.get_product_data(row)
                if product_data and product_data['id']:
                    # 生成快手商品链接
                    product_link = f"https://www.kuaishou.com/product/{product_data['id']}"
                    clipboard = QApplication.clipboard()
                    clipboard.setText(product_link)
                    # 无感提示：只在控制台输出，不弹窗
                    print(f"✅ 已复制商品链接: {product_link}")
                else:
                    print("⚠️ 无法获取商品ID")
            else:
                print("⚠️ 请先选择一个商品")
        except Exception as e:
            print(f"❌ 复制商品链接失败: {str(e)}")

    def view_1688_product(self):
        """查看1688商品 - 用默认浏览器打开1688商品页面"""
        try:
            row = self.get_selected_row()
            if row is not None:
                product_data = self.get_product_data(row)
                if product_data and product_data['supplier']:
                    # 获取上家ID（去除可能的空格）
                    supplier_id = product_data['supplier'].strip()
                    if supplier_id:
                        # 构造1688商品链接
                        url = f"https://detail.1688.com/offer/{supplier_id}.html"
                        try:
                            webbrowser.open(url)
                            print(f"打开1688商品链接: {url}")
                        except Exception as browser_error:
                            # 如果打开浏览器失败，复制链接到剪贴板作为备选方案
                            clipboard = QApplication.clipboard()
                            clipboard.setText(url)
                            QMessageBox.warning(self, "错误", f"无法打开浏览器，已将链接复制到剪贴板:\n{url}")
                    else:
                        QMessageBox.warning(self, "警告", "该商品的上家信息为空")
                else:
                    QMessageBox.warning(self, "警告", "该商品没有上家信息")
            else:
                QMessageBox.warning(self, "警告", "请先选择一个商品")
        except Exception as e:
            print(f"查看1688商品失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"查看1688商品失败: {str(e)}")

    def copy_supplier_link(self):
        """复制上家链接 - 支持批量复制多个勾选商品的上家链接"""
        try:
            # 优先获取勾选的商品
            checked_rows = self.get_checkbox_selected_rows()

            if checked_rows:
                # 批量复制勾选商品的上家链接
                supplier_links = []
                valid_count = 0

                for row in checked_rows:
                    product_data = self.get_product_data(row)
                    if product_data and product_data['supplier']:
                        # 获取上家ID（去除可能的空格）
                        supplier_id = product_data['supplier'].strip()
                        if supplier_id:
                            # 构造完整的1688商品链接
                            url = f"https://detail.1688.com/offer/{supplier_id}.html"
                            # 添加商品标题作为注释（便于识别）
                            title = product_data.get('title', '未知商品')[:30]
                            supplier_links.append(f"{url}  # {title}")
                            valid_count += 1

                if supplier_links:
                    # 将所有链接用换行符连接
                    all_links = '\n'.join(supplier_links)
                    clipboard = QApplication.clipboard()
                    clipboard.setText(all_links)

                    # 无感提示：只在控制台输出，不弹窗
                    print(f"✅ 批量复制上家链接成功: {valid_count} 个商品")
                    print(f"📊 总勾选商品: {len(checked_rows)} 个，有效上家链接: {valid_count} 个")
                else:
                    print("⚠️ 勾选的商品中没有有效的上家链接信息")
            else:
                # 如果没有勾选商品，则复制当前选中行的上家链接
                row = self.get_selected_row()
                if row is not None:
                    product_data = self.get_product_data(row)
                    if product_data and product_data['supplier']:
                        # 获取上家ID（去除可能的空格）
                        supplier_id = product_data['supplier'].strip()
                        if supplier_id:
                            # 构造完整的1688商品链接
                            url = f"https://detail.1688.com/offer/{supplier_id}.html"
                            clipboard = QApplication.clipboard()
                            clipboard.setText(url)
                            # 无感提示：只在控制台输出，不弹窗
                            print(f"✅ 已复制上家链接: {url}")
                        else:
                            print("⚠️ 该商品的上家信息为空")
                    else:
                        print("⚠️ 该商品没有上家链接信息")
                else:
                    print("⚠️ 请先勾选商品或选择一个商品")

        except Exception as e:
            print(f"❌ 复制上家链接失败: {str(e)}")

    # 商品操作功能
    def edit_product(self):
        """编辑商品"""
        try:
            row = self.get_selected_row()
            if row is not None:
                product_data = self.get_product_data(row)
                if product_data:
                    # 这里可以根据实际需求实现商品编辑逻辑
                    QMessageBox.information(self, "提示", f"商品编辑功能开发中，敬请期待...\n商品: {product_data['title'][:30]}...")
                else:
                    QMessageBox.warning(self, "警告", "无法获取商品信息")
            else:
                QMessageBox.warning(self, "警告", "请先选择一个商品")
        except Exception as e:
            print(f"编辑商品失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"编辑商品失败: {str(e)}")

    def perform_delist_operation(self, product_operations):
        """执行商品下架操作 - 完全复用详情统计的实现"""
        try:
            # 读取账号管理配置
            config_path = get_config_path('账号管理.json')
            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}")
                QMessageBox.warning(self, "错误", "账号管理配置文件不存在")
                return

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 按店铺分组商品
            shop_products = {}
            for operation in product_operations:
                shop_name = operation['shop_name']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(operation)

            # 执行下架操作
            for shop_name, products in shop_products.items():
                print(f"开始下架店铺 {shop_name} 的商品...")

                # 查找店铺的accesstoken
                shop_info = self.find_shop_info(config_data, shop_name)
                if not shop_info:
                    print(f"未找到店铺 {shop_name} 的配置信息")
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 未找到店铺配置")
                    continue

                access_token = shop_info.get('accesstoken')
                if not access_token:
                    print(f"店铺 {shop_name} 没有accesstoken")
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 缺少accesstoken")
                    continue

                # 创建快手API实例
                try:
                    # 从配置文件或环境变量获取API密钥
                    app_key = shop_info.get('app_key') or self.get_kuaishou_app_key()
                    app_secret = shop_info.get('app_secret') or self.get_kuaishou_app_secret()
                    sign_secret = shop_info.get('signSecret') or self.get_kuaishou_sign_secret()

                    api = KuaishouAPI(
                        app_key=app_key,
                        app_secret=app_secret,
                        sign_secret=sign_secret,
                        access_token=access_token
                    )

                    # 为每个商品执行下架操作
                    for product in products:
                        if not isinstance(product, dict) or 'product_id' not in product:
                            continue

                        product_id = product['product_id']
                        row = product.get('row', -1)
                        print(f"正在下架商品: {product_id}")

                        # 更新状态为处理中
                        if row >= 0:
                            self.update_operation_status(row, "下架中...")
                            # 立即刷新界面，确保状态更新显示
                            QApplication.processEvents()

                        try:
                            # 调用快手API下架商品
                            result = api.unshelf_item(int(product_id))

                            if result.get('success'):
                                print(f"商品 {product_id} 下架成功")
                                if row >= 0:
                                    self.update_operation_status(row, "下架成功")
                            else:
                                error_msg = result.get('message', '下架失败')
                                # 检查是否是"重复操作"错误，这种情况应该视为成功
                                if "重复操作" in error_msg and "上下架状态已生效" in error_msg:
                                    print(f"商品 {product_id} 已经是下架状态，无需重复下架")
                                    if row >= 0:
                                        self.update_operation_status(row, "已下架")
                                else:
                                    print(f"商品 {product_id} 下架失败: {error_msg}")
                                    if row >= 0:
                                        self.update_operation_status(row, f"下架失败: {error_msg}")
                        except Exception as e:
                            error_msg = f"下架异常: {str(e)}"
                            print(f"商品 {product_id} 下架异常: {error_msg}")
                            if row >= 0:
                                self.update_operation_status(row, f"下架异常: {error_msg}")

                except Exception as e:
                    print(f"创建快手API实例失败: {str(e)}")
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"API初始化失败: {str(e)}")

            print("下架操作完成，状态已更新到表格中")

        except Exception as e:
            print(f"执行下架操作时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"下架操作异常: {str(e)}")
    
    def perform_delist_operation_with_progress(self, product_operations):
        """执行商品下架操作（带进度条更新）"""
        success_count = 0  # 统计成功下架的商品数量
        try:
            # 读取账号管理配置
            config_path = get_config_path('账号管理.json')
            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}")
                QMessageBox.warning(self, "错误", "账号管理配置文件不存在")
                return success_count

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 按店铺分组商品
            shop_products = {}
            for operation in product_operations:
                shop_name = operation['shop_name']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(operation)

            # 执行下架操作
            completed_count = 0
            total_count = len(product_operations)
            
            for shop_name, products in shop_products.items():
                print(f"开始下架店铺 {shop_name} 的商品...")

                # 查找店铺的accesstoken
                shop_info = self.find_shop_info(config_data, shop_name)
                if not shop_info:
                    print(f"未找到店铺 {shop_name} 的配置信息")
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 未找到店铺配置")
                            completed_count += 1
                            self.progress_bar.setValue(completed_count)
                            self.status_label.setText(f"自动下架进度: {completed_count}/{total_count} - 配置错误")
                            QApplication.processEvents()  # 刷新界面
                    continue

                access_token = shop_info.get('accesstoken')
                if not access_token:
                    print(f"店铺 {shop_name} 没有accesstoken")
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 缺少accesstoken")
                            completed_count += 1
                            self.progress_bar.setValue(completed_count)
                            self.status_label.setText(f"自动下架进度: {completed_count}/{total_count} - 配置错误")
                            QApplication.processEvents()  # 刷新界面
                    continue

                # 创建快手API实例
                try:
                    # 从配置文件或环境变量获取API密钥
                    app_key = shop_info.get('app_key') or self.get_kuaishou_app_key()
                    app_secret = shop_info.get('app_secret') or self.get_kuaishou_app_secret()
                    sign_secret = shop_info.get('signSecret') or self.get_kuaishou_sign_secret()

                    api = KuaishouAPI(
                        app_key=app_key,
                        app_secret=app_secret,
                        sign_secret=sign_secret,
                        access_token=access_token
                    )

                    # 为每个商品执行下架操作
                    for product in products:
                        if not isinstance(product, dict) or 'product_id' not in product:
                            completed_count += 1
                            self.progress_bar.setValue(completed_count)
                            QApplication.processEvents()
                            continue

                        product_id = product['product_id']
                        row = product.get('row', -1)
                        print(f"正在下架商品: {product_id}")

                        # 更新状态为处理中
                        if row >= 0:
                            self.update_operation_status(row, "下架中...")
                            QApplication.processEvents()  # 立即刷新界面

                        try:
                            # 调用快手API下架商品
                            result = api.unshelf_item(int(product_id))

                            if result.get('success'):
                                print(f"商品 {product_id} 下架成功")
                                success_count += 1  # 增加成功计数
                                if row >= 0:
                                    self.update_operation_status(row, "下架成功")
                            else:
                                error_msg = result.get('message', '下架失败')
                                # 检查是否是"重复操作"错误，这种情况应该视为成功
                                if "重复操作" in error_msg and "上下架状态已生效" in error_msg:
                                    print(f"商品 {product_id} 已经是下架状态，无需重复下架")
                                    success_count += 1  # 重复操作也视为成功
                                    if row >= 0:
                                        self.update_operation_status(row, "已下架")
                                else:
                                    print(f"商品 {product_id} 下架失败: {error_msg}")
                                    if row >= 0:
                                        self.update_operation_status(row, f"下架失败: {error_msg}")
                        except Exception as e:
                            error_msg = f"下架异常: {str(e)}"
                            print(f"商品 {product_id} 下架异常: {error_msg}")
                            if row >= 0:
                                self.update_operation_status(row, f"下架异常: {error_msg}")

                        # 更新进度
                        completed_count += 1
                        self.progress_bar.setValue(completed_count)
                        self.status_label.setText(f"自动下架进度: {completed_count}/{total_count} - 已处理 {product_id}")
                        QApplication.processEvents()  # 刷新界面

                except Exception as e:
                    print(f"创建快手API实例失败: {str(e)}")
                    # 为所有商品更新状态为API初始化失败
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            product_id = product.get('product_id', '未知')
                            row = product.get('row', -1)
                            if row >= 0:
                                self.update_operation_status(row, f"API初始化失败: {str(e)}")
                                QApplication.processEvents()  # 立即刷新界面
                            completed_count += 1
                            self.progress_bar.setValue(completed_count)
                            self.status_label.setText(f"自动下架进度: {completed_count}/{total_count} - API错误: {product_id}")
                            QApplication.processEvents()  # 刷新界面

            # 最终检查：确保所有商品的状态都已更新，修复仍显示"下架中..."的商品
            print("正在进行最终状态检查...")
            for operation in product_operations:
                row = operation.get('row', -1)
                if row >= 0:
                    current_status_item = self.product_table.item(row, 8)  # 操作状态列
                    if current_status_item:
                        current_status = current_status_item.text()
                        # 如果状态仍然是"下架中..."或"自动下架中..."，更新为处理完成
                        if "下架中" in current_status:
                            product_id = operation.get('product_id', '未知')
                            print(f"修复商品 {product_id} (第{row+1}行) 的状态: {current_status} -> 处理完成")
                            self.update_operation_status(row, "处理完成")

            # 强制刷新界面
            QApplication.processEvents()
            print(f"带进度的下架操作完成，成功下架 {success_count} 个商品，状态已更新到表格中")
            return success_count

        except Exception as e:
            print(f"执行带进度的下架操作时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"下架操作异常: {str(e)}")
            return success_count

    def perform_list_operation(self, product_operations):
        """执行上架操作 - 完全复用详情统计的实现"""
        try:
            # 读取账号配置
            config_path = get_config_path("账号管理.json")
            if not os.path.exists(config_path):
                QMessageBox.critical(self, "错误", "未找到账号配置文件")
                return

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 按店铺分组商品
            shop_products = {}
            for operation in product_operations:
                shop_name = operation['shop_name']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(operation)

            # 执行上架操作
            for shop_name, products in shop_products.items():
                print(f"开始上架店铺 {shop_name} 的商品...")

                # 查找店铺的accesstoken
                shop_info = self.find_shop_info(config_data, shop_name)
                if not shop_info:
                    print(f"未找到店铺 {shop_name} 的配置信息")
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 未找到店铺配置")
                    continue

                access_token = shop_info.get('accesstoken')
                if not access_token:
                    print(f"店铺 {shop_name} 没有accesstoken")
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 缺少accesstoken")
                    continue

                # 创建快手API实例
                try:
                    # 从配置文件或环境变量获取API密钥
                    app_key = shop_info.get('app_key') or self.get_kuaishou_app_key()
                    app_secret = shop_info.get('app_secret') or self.get_kuaishou_app_secret()
                    sign_secret = shop_info.get('signSecret') or self.get_kuaishou_sign_secret()

                    api = KuaishouAPI(
                        app_key=app_key,
                        app_secret=app_secret,
                        sign_secret=sign_secret,
                        access_token=access_token
                    )

                    # 为每个商品执行上架操作
                    for product in products:
                        if not isinstance(product, dict) or 'product_id' not in product:
                            continue

                        product_id = product['product_id']
                        row = product.get('row', -1)
                        print(f"正在上架商品: {product_id}")

                        # 更新状态为处理中
                        if row >= 0:
                            self.update_operation_status(row, "上架中...")

                        try:
                            # 调用快手API上架商品
                            result = api.shelf_item(int(product_id))

                            if result.get('success'):
                                print(f"商品 {product_id} 上架成功")
                                if row >= 0:
                                    self.update_operation_status(row, "上架成功")
                            else:
                                error_msg = result.get('message', '上架失败')
                                print(f"商品 {product_id} 上架失败: {error_msg}")
                                if row >= 0:
                                    self.update_operation_status(row, f"上架失败: {error_msg}")
                        except Exception as e:
                            error_msg = f"上架异常: {str(e)}"
                            print(f"商品 {product_id} 上架异常: {error_msg}")
                            if row >= 0:
                                self.update_operation_status(row, f"上架异常: {error_msg}")

                except Exception as e:
                    print(f"创建快手API实例失败: {str(e)}")
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"API初始化失败: {str(e)}")

            print("上架操作完成，状态已更新到表格中")

        except Exception as e:
            print(f"执行上架操作时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"上架操作异常: {str(e)}")

    def find_shop_info(self, config_data, shop_name):
        """从配置数据中查找店铺信息"""
        try:
            if 'data' in config_data:
                for shop_data in config_data['data']:
                    if shop_data.get('店铺名称') == shop_name:
                        return shop_data
            return None
        except Exception as e:
            print(f"查找店铺信息时出错: {str(e)}")
            return None

    def get_kuaishou_app_key(self):
        """获取快手API的App Key"""
        try:
            # 首先尝试从配置文件读取
            config_path = get_config_path('config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    kuaishou_config = config_data.get('kuaishou_api_config', {})
                    app_key = kuaishou_config.get('app_key')
                    if app_key:
                        return app_key

            # 如果配置文件中没有，返回默认值或环境变量
            return os.environ.get('KUAISHOU_APP_KEY', 'your_app_key_here')

        except Exception as e:
            print(f"获取快手App Key失败: {str(e)}")
            return 'your_app_key_here'

    def get_kuaishou_app_secret(self):
        """获取快手API的App Secret"""
        try:
            # 首先尝试从配置文件读取
            config_path = get_config_path('config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    kuaishou_config = config_data.get('kuaishou_api_config', {})
                    app_secret = kuaishou_config.get('app_secret')
                    if app_secret:
                        return app_secret

            # 如果配置文件中没有，返回默认值或环境变量
            return os.environ.get('KUAISHOU_APP_SECRET', 'your_app_secret_here')

        except Exception as e:
            print(f"获取快手App Secret失败: {str(e)}")
            return 'your_app_secret_here'

    def get_kuaishou_sign_secret(self):
        """获取快手API的Sign Secret"""
        try:
            # 首先尝试从配置文件读取
            config_path = get_config_path('config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    kuaishou_config = config_data.get('kuaishou_api_config', {})
                    sign_secret = kuaishou_config.get('signSecret')
                    if sign_secret:
                        return sign_secret

            # 如果配置文件中没有，返回默认值或环境变量
            return os.environ.get('KUAISHOU_SIGN_SECRET', 'your_sign_secret_here')

        except Exception as e:
            print(f"获取快手Sign Secret失败: {str(e)}")
            return 'your_sign_secret_here'

    def perform_delete_operation(self, product_operations):
        """执行商品删除操作 - 完全复用详情统计的实现"""
        try:
            # 读取账号管理配置
            config_path = get_config_path('账号管理.json')
            if not os.path.exists(config_path):
                print(f"配置文件不存在: {config_path}")
                QMessageBox.warning(self, "错误", "账号管理配置文件不存在")
                return

            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 按店铺分组商品
            shop_products = {}
            for operation in product_operations:
                shop_name = operation['shop_name']
                if shop_name not in shop_products:
                    shop_products[shop_name] = []
                shop_products[shop_name].append(operation)

            # 执行删除操作
            for shop_name, products in shop_products.items():
                print(f"开始删除店铺 {shop_name} 的商品...")

                # 查找店铺的accesstoken
                shop_info = self.find_shop_info(config_data, shop_name)
                if not shop_info:
                    print(f"未找到店铺 {shop_name} 的配置信息")
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 未找到店铺配置")
                    continue

                access_token = shop_info.get('accesstoken')
                if not access_token:
                    print(f"店铺 {shop_name} 没有accesstoken")
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"配置错误: 缺少accesstoken")
                    continue

                # 创建快手API实例
                try:
                    # 从配置文件或环境变量获取API密钥
                    app_key = shop_info.get('app_key') or self.get_kuaishou_app_key()
                    app_secret = shop_info.get('app_secret') or self.get_kuaishou_app_secret()
                    sign_secret = shop_info.get('signSecret') or self.get_kuaishou_sign_secret()

                    api = KuaishouAPI(
                        app_key=app_key,
                        app_secret=app_secret,
                        sign_secret=sign_secret,
                        access_token=access_token
                    )

                    # 为每个商品执行删除操作
                    for product in products:
                        if not isinstance(product, dict) or 'product_id' not in product:
                            continue

                        product_id = product['product_id']
                        row = product.get('row', -1)
                        print(f"正在删除商品: {product_id}")

                        # 更新状态为处理中
                        if row >= 0:
                            self.update_operation_status(row, "删除中...")

                        try:
                            # 调用快手API删除商品
                            result = api.delete_item(int(product_id))

                            if result.get('success'):
                                print(f"商品 {product_id} 删除成功")
                                if row >= 0:
                                    self.update_operation_status(row, "删除成功")
                                    # 删除成功后，可以考虑从表格中移除该行
                                    # self.product_table.removeRow(row)  # 可选：删除表格行
                            else:
                                error_msg = result.get('message', '删除失败')
                                print(f"商品 {product_id} 删除失败: {error_msg}")
                                if row >= 0:
                                    self.update_operation_status(row, f"删除失败: {error_msg}")
                        except Exception as e:
                            error_msg = f"删除异常: {str(e)}"
                            print(f"商品 {product_id} 删除异常: {error_msg}")
                            if row >= 0:
                                self.update_operation_status(row, f"删除异常: {error_msg}")

                except Exception as e:
                    print(f"创建快手API实例失败: {str(e)}")
                    for product in products:
                        if isinstance(product, dict) and 'product_id' in product:
                            if 'row' in product:
                                self.update_operation_status(product['row'], f"API初始化失败: {str(e)}")

            print("删除操作完成，状态已更新到表格中")

        except Exception as e:
            print(f"执行删除操作时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"删除操作异常: {str(e)}")

    def filter_by_time(self):
        """时间筛选功能"""
        print("=== 时间筛选按钮被点击 ===")
        try:
            # 获取开始和结束日期时间
            start_datetime = self.start_date.dateTime()
            end_datetime = self.end_date.dateTime()

            # 验证日期范围
            if start_datetime > end_datetime:
                QMessageBox.warning(self, "警告", "开始时间不能晚于结束时间！")
                return

            # 获取表格中的所有行
            row_count = self.product_table.rowCount()
            if row_count == 0:
                QMessageBox.information(self, "提示", "表格中没有数据可以筛选")
                return

            filtered_count = 0
            hidden_count = 0

            # 遍历所有行进行筛选
            for row in range(row_count):
                # 获取创建时间列的数据（第6列）
                time_item = self.product_table.item(row, 6)
                if time_item:
                    time_text = time_item.text()
                    try:
                        # 解析时间字符串
                        if time_text and len(time_text) >= 16:
                            product_datetime = QDateTime.fromString(time_text[:16], "yyyy-MM-dd hh:mm")

                            # 检查是否在时间范围内
                            if start_datetime <= product_datetime <= end_datetime:
                                self.product_table.setRowHidden(row, False)
                                filtered_count += 1
                            else:
                                self.product_table.setRowHidden(row, True)
                                hidden_count += 1
                        else:
                            # 时间格式不正确，隐藏该行
                            self.product_table.setRowHidden(row, True)
                            hidden_count += 1
                    except Exception as e:
                        print(f"解析时间失败: {time_text}, 错误: {str(e)}")
                        self.product_table.setRowHidden(row, True)
                        hidden_count += 1
                else:
                    # 没有时间数据，隐藏该行
                    self.product_table.setRowHidden(row, True)
                    hidden_count += 1

            # 更新状态信息
            start_str = start_datetime.toString("yyyy-MM-dd hh:mm")
            end_str = end_datetime.toString("yyyy-MM-dd hh:mm")
            self.status_label.setText(f"时间筛选完成: {start_str} 至 {end_str}, 显示 {filtered_count} 条，隐藏 {hidden_count} 条")

            print(f"时间筛选完成: 显示 {filtered_count} 条，隐藏 {hidden_count} 条")

        except Exception as e:
            print(f"时间筛选失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"时间筛选失败: {str(e)}")

    def filter_by_sales(self):
        """销量筛选功能"""
        print("=== 销量筛选按钮被点击 ===")
        try:
            # 获取销量范围
            min_text = self.sales_min.text().strip()
            max_text = self.sales_max.text().strip()

            # 验证输入
            min_sales = 0
            max_sales = float('inf')

            if min_text:
                try:
                    min_sales = int(min_text)
                    if min_sales < 0:
                        QMessageBox.warning(self, "警告", "最小销量不能为负数！")
                        return
                except ValueError:
                    QMessageBox.warning(self, "警告", "最小销量必须是数字！")
                    return

            if max_text:
                try:
                    max_sales = int(max_text)
                    if max_sales < 0:
                        QMessageBox.warning(self, "警告", "最大销量不能为负数！")
                        return
                except ValueError:
                    QMessageBox.warning(self, "警告", "最大销量必须是数字！")
                    return

            if min_sales > max_sales:
                QMessageBox.warning(self, "警告", "最小销量不能大于最大销量！")
                return

            # 获取表格中的所有行
            row_count = self.product_table.rowCount()
            if row_count == 0:
                QMessageBox.information(self, "提示", "表格中没有数据可以筛选")
                return

            filtered_count = 0
            hidden_count = 0

            # 遍历所有行进行筛选
            for row in range(row_count):
                # 获取累销列的数据（第5列）
                sales_item = self.product_table.item(row, 5)
                if sales_item:
                    sales_text = sales_item.text()
                    try:
                        # 提取销量数字
                        import re
                        numbers = re.findall(r'\d+', sales_text)
                        if numbers:
                            sales_value = int(numbers[0])

                            # 检查是否在销量范围内
                            if min_sales <= sales_value <= max_sales:
                                self.product_table.setRowHidden(row, False)
                                filtered_count += 1
                            else:
                                self.product_table.setRowHidden(row, True)
                                hidden_count += 1
                        else:
                            # 没有找到数字，隐藏该行
                            self.product_table.setRowHidden(row, True)
                            hidden_count += 1
                    except Exception as e:
                        print(f"解析销量失败: {sales_text}, 错误: {str(e)}")
                        self.product_table.setRowHidden(row, True)
                        hidden_count += 1
                else:
                    # 没有销量数据，隐藏该行
                    self.product_table.setRowHidden(row, True)
                    hidden_count += 1

            # 更新状态信息
            range_str = f"{min_sales} - {max_sales if max_sales != float('inf') else '∞'}"
            self.status_label.setText(f"销量筛选完成: {range_str}, 显示 {filtered_count} 条，隐藏 {hidden_count} 条")

            print(f"销量筛选完成: 显示 {filtered_count} 条，隐藏 {hidden_count} 条")

        except Exception as e:
            print(f"销量筛选失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"销量筛选失败: {str(e)}")

    def filter_by_price(self):
        """价格筛选功能"""
        print("=== 价格筛选按钮被点击 ===")
        try:
            # 获取价格范围
            min_text = self.price_min.text().strip()
            max_text = self.price_max.text().strip()

            # 验证输入
            min_price = 0.0
            max_price = float('inf')

            if min_text:
                try:
                    min_price = float(min_text)
                    if min_price < 0:
                        QMessageBox.warning(self, "警告", "最小价格不能为负数！")
                        return
                except ValueError:
                    QMessageBox.warning(self, "警告", "最小价格必须是数字！")
                    return

            if max_text:
                try:
                    max_price = float(max_text)
                    if max_price < 0:
                        QMessageBox.warning(self, "警告", "最大价格不能为负数！")
                        return
                except ValueError:
                    QMessageBox.warning(self, "警告", "最大价格必须是数字！")
                    return

            if min_price > max_price:
                QMessageBox.warning(self, "警告", "最小价格不能大于最大价格！")
                return

            # 获取表格中的所有行
            row_count = self.product_table.rowCount()
            if row_count == 0:
                QMessageBox.information(self, "提示", "表格中没有数据可以筛选")
                return

            filtered_count = 0
            hidden_count = 0

            # 遍历所有行进行筛选
            for row in range(row_count):
                # 获取价格列的数据（第3列）
                price_item = self.product_table.item(row, 3)
                if price_item:
                    price_text = price_item.text()
                    try:
                        # 提取价格数字
                        import re
                        # 处理价格范围，如"¥59.90-79.90"，取最小值
                        if '-' in price_text and '¥' in price_text:
                            numbers = re.findall(r'[\d.]+', price_text)
                            if numbers:
                                price_value = float(numbers[0])
                            else:
                                price_value = 0.0
                        else:
                            # 提取所有数字
                            numbers = re.findall(r'[\d.]+', price_text)
                            if numbers:
                                price_value = float(numbers[0])
                            else:
                                price_value = 0.0

                        # 检查是否在价格范围内
                        if min_price <= price_value <= max_price:
                            self.product_table.setRowHidden(row, False)
                            filtered_count += 1
                        else:
                            self.product_table.setRowHidden(row, True)
                            hidden_count += 1

                    except Exception as e:
                        print(f"解析价格失败: {price_text}, 错误: {str(e)}")
                        self.product_table.setRowHidden(row, True)
                        hidden_count += 1
                else:
                    # 没有价格数据，隐藏该行
                    self.product_table.setRowHidden(row, True)
                    hidden_count += 1

            # 更新状态信息
            range_str = f"{min_price} - {max_price if max_price != float('inf') else '∞'}"
            self.status_label.setText(f"价格筛选完成: ¥{range_str}, 显示 {filtered_count} 条，隐藏 {hidden_count} 条")

            print(f"价格筛选完成: 显示 {filtered_count} 条，隐藏 {hidden_count} 条")

        except Exception as e:
            print(f"价格筛选失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"价格筛选失败: {str(e)}")

    def filter_products(self):
        """根据搜索输入框的关键词筛选表格中的商品"""
        print("=== 筛选按钮被点击 ===")
        try:
            # 获取搜索关键词
            filter_text = self.product_id_input.text().strip()
            if not filter_text:
                QMessageBox.information(self, "提示", "请输入筛选关键词")
                return

            # 获取表格中的所有行
            row_count = self.product_table.rowCount()
            if row_count == 0:
                QMessageBox.information(self, "提示", "表格中没有数据可以筛选")
                return

            # 定义要筛选的列索引：标题(1)、商品ID(2)、操作(8)、上家(14)、上家名称(15)
            filter_columns = [1, 2, 8, 14, 15]

            filtered_count = 0
            hidden_count = 0

            # 遍历所有行进行筛选
            for row in range(row_count):
                row_matches = False

                # 检查指定列是否包含关键词
                for col in filter_columns:
                    item = self.product_table.item(row, col)
                    if item and item.text():
                        # 包含匹配，不区分大小写
                        if filter_text.lower() in item.text().lower():
                            row_matches = True
                            break

                # 根据匹配结果显示或隐藏行
                if row_matches:
                    self.product_table.setRowHidden(row, False)
                    filtered_count += 1
                else:
                    self.product_table.setRowHidden(row, True)
                    hidden_count += 1

            # 更新状态显示
            self.status_label.setText(f"筛选完成: 找到 {filtered_count} 条匹配商品，隐藏 {hidden_count} 条")
            print(f"筛选完成: 关键词='{filter_text}', 显示={filtered_count}条, 隐藏={hidden_count}条")

            # 更新商品数量显示
            self.update_product_count_display()

        except Exception as e:
            print(f"筛选失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"筛选失败: {str(e)}")

    def clear_filters(self):
        """清除所有筛选，显示所有行"""
        print("=== 清除筛选按钮被点击 ===")
        try:
            row_count = self.product_table.rowCount()
            if row_count == 0:
                QMessageBox.information(self, "提示", "表格中没有数据")
                return

            # 显示所有行
            for row in range(row_count):
                self.product_table.setRowHidden(row, False)

            # 清空输入框
            self.sales_min.clear()
            self.sales_max.clear()
            self.price_min.clear()
            self.price_max.clear()
            # 不清空搜索输入框，因为用户可能还需要使用它进行搜索
            # self.product_id_input.clear()

            # 重置日期为默认值（一个月前到当前日期）
            current_date = QDate.currentDate()
            start_date = current_date.addMonths(-1)
            self.start_date.setDate(start_date)
            self.end_date.setDate(current_date)

            # 更新状态
            self.status_label.setText(f"已清除筛选，显示全部 {row_count} 条数据")
            print(f"清除筛选完成，显示全部 {row_count} 条数据")

            # 更新商品数量显示
            self.update_product_count_display()

        except Exception as e:
            print(f"清除筛选失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"清除筛选失败: {str(e)}")

    def get_supplier_info(self):
        """获取上家信息 - 优化版本：查完上家ID后自动查询上家商品在线状态和上家名称"""
        try:
            # 获取勾选的店铺
            checked_shops = self.category_tree.get_checked_shops()
            if not checked_shops:
                QMessageBox.warning(self, "提示", "请先在左侧分类树中勾选店铺")
                return

            # 获取店铺数量信息
            shop_count = len(checked_shops)

            # 检查商品表格是否为空
            current_row_count = self.product_table.rowCount()

            if current_row_count == 0:
                # 表格为空，需要先加载商品数据
                self.status_label.setText("商品表格为空，正在加载商品数据...")

                # 确认操作
                reply = QMessageBox.question(self, "确认查询上家信息",
                                           f"商品表格为空，需要先加载商品数据。\n"
                                           f"将按以下流程操作：\n"
                                           f"1. 加载 {shop_count} 个店铺的在售商品\n"
                                           f"2. 查询上家信息（仅查询上家列为空的商品）\n"
                                           f"3. 查询上家商品在线状态和上家名称\n"
                                           f"4. 保存所有信息\n"
                                           f"确定要继续吗？",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply != QMessageBox.Yes:
                    return

                # 标记正在进行上家查询模式
                self.is_supplier_query_mode = True

                # 加载商品数据（加载完成后会自动调用start_supplier_query）
                self.load_products_by_status("ON_SALE", "在售")

            else:
                # 表格不为空，直接开始查询上家信息
                self.status_label.setText(f"检测到表格已有 {current_row_count} 个商品，直接开始查询上家信息...")

                # 统计需要查询的商品数量（上家列为空的）
                empty_supplier_count = 0
                for row in range(current_row_count):
                    supplier_item = self.product_table.item(row, 14)  # 上家列
                    if not supplier_item or not supplier_item.text().strip():
                        empty_supplier_count += 1

                if empty_supplier_count == 0:
                    self.status_label.setText("所有商品都已有上家信息，无需查询")
                    QMessageBox.information(self, "提示", "当前表格中的所有商品都已有上家信息，无需重复查询。")
                    return

                # 确认直接查询
                reply = QMessageBox.question(self, "确认查询上家信息",
                                           f"当前表格共有 {current_row_count} 个商品\n"
                                           f"其中 {empty_supplier_count} 个商品需要查询上家信息\n"
                                           f"将执行以下操作：\n"
                                           f"1. 查询上家ID\n"
                                           f"2. 查询上家商品在线状态和上家名称\n"
                                           f"3. 保存所有信息\n"
                                           f"是否开始查询？\n\n"
                                           f"提示：如需重新加载商品，请先点击'商品列表'按钮",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply != QMessageBox.Yes:
                    return

                # 直接开始查询上家信息
                self.start_supplier_query()

        except Exception as e:
            print(f"获取上家信息失败: {str(e)}")
            # 使用状态标签显示错误，避免阻塞对话框
            self.status_label.setText(f"获取上家信息失败: {str(e)}")

            # 如果是登录相关错误，给出特别提示
            error_str = str(e)
            if "登录失效" in error_str or "重新登录" in error_str or "Cookie" in error_str:
                self.status_label.setText("上家信息查询失败：登录已失效，请重新登录")
                QTimer.singleShot(100, lambda: self.show_login_expired_warning(error_str))
    
    def on_shop_products_loaded(self, shop_name, product_count):
        """单个店铺商品加载完成回调"""
        print(f"店铺 {shop_name} 商品加载完成，共 {product_count} 个商品")
    
    def start_supplier_query(self):
        """开始查询上家信息 - 添加防重复查询机制"""
        try:
            # 防重复查询检查
            if hasattr(self, 'supplier_thread') and self.supplier_thread and self.supplier_thread.isRunning():
                print("上家查询线程正在运行，跳过重复查询")
                return

            # 获取表格中的商品数据
            row_count = self.product_table.rowCount()
            if row_count == 0:
                self.status_label.setText("商品列表为空，无法查询上家信息")
                return

            # 统计需要查询的商品数量（上家列为空的）
            empty_supplier_count = 0
            for row in range(row_count):
                supplier_item = self.product_table.item(row, 14)  # 上家列
                if not supplier_item or not supplier_item.text().strip():
                    empty_supplier_count += 1

            if empty_supplier_count == 0:
                self.status_label.setText("所有商品都已有上家信息，无需查询")
                return

            print(f"开始上家查询：找到 {empty_supplier_count} 个需要查询的商品")
            self.status_label.setText(f"商品加载完成，找到 {empty_supplier_count} 个需要查询上家信息的商品，开始查询...")

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, empty_supplier_count)  # 设置为需要查询的商品数量
            self.progress_bar.setValue(0)

            # 创建上家查询线程（使用原有的SupplierQueryThread）
            checked_shops = self.category_tree.get_checked_shops()
            self.supplier_thread = SupplierQueryThread(checked_shops, self.product_table, self)
            self.supplier_thread.progress_updated.connect(self.update_supplier_progress)
            self.supplier_thread.row_updated.connect(self.update_supplier_row)
            self.supplier_thread.query_completed.connect(self.on_supplier_query_completed)
            self.supplier_thread.start()

        except Exception as e:
            print(f"开始上家查询失败: {str(e)}")
            self.status_label.setText(f"开始上家查询失败: {str(e)}")
    
    def update_supplier_progress(self, message):
        """更新上家查询进度"""
        self.status_label.setText(message)
        
        # 从进度消息中提取当前进度数字，更新进度条
        try:
            import re
            # 匹配格式：总进度: 25/100 或者 店铺 XXX 并发查询: 5/10，总进度: 25/100
            match = re.search(r'总进度:\s*(\d+)/(\d+)', message)
            if match:
                current = int(match.group(1))
                total = int(match.group(2))
                self.progress_bar.setValue(current)
                print(f"上家查询进度: {current}/{total} ({current/total*100:.1f}%)")
            else:
                # 兼容其他格式的进度信息
                simple_match = re.search(r'(\d+)/(\d+)', message)
                if simple_match:
                    current = int(simple_match.group(1))
                    total = int(simple_match.group(2))
                    # 只有当这个比例看起来合理时才更新进度条
                    if current <= total and total > 0:
                        self.progress_bar.setValue(current)
                        print(f"上家查询进度（简单格式）: {current}/{total}")
        except Exception as e:
            print(f"解析上家查询进度失败: {str(e)}")
    
    def update_supplier_status_progress(self, message):
        """更新上家状态查询进度"""
        self.status_label.setText(message)
        
        # 从进度消息中提取当前进度数字
        try:
            import re
            # 匹配格式：上家状态查询: 25/100
            match = re.search(r'上家状态查询:\s*(\d+)/(\d+)', message)
            if match:
                current = int(match.group(1))
                total = int(match.group(2))
                self.progress_bar.setValue(current)
                print(f"上家状态查询进度: {current}/{total} ({current/total*100:.1f}%)")
        except Exception as e:
            print(f"解析上家状态查询进度失败: {str(e)}")
    
    def update_supplier_row(self, row, supplier_info, category_info=""):
        """更新单行的上家信息和类目信息"""
        try:
            # 更新表格第14列（上家列）
            supplier_item = QTableWidgetItem(supplier_info)
            self.product_table.setItem(row, 14, supplier_item)

            # 更新表格第13列（类目列）
            if category_info:
                category_item = QTableWidgetItem(category_info)
                self.product_table.setItem(row, 13, category_item)
        except Exception as e:
            print(f"更新上家信息和类目信息失败: {str(e)}")

    def show_category_analysis(self):
        """显示类目分析窗口"""
        try:
            # 创建类目分析窗口
            analysis_window = CategoryAnalysisWindow(self, self)

            # 先显示窗口，然后再居中（确保窗口几何信息正确）
            analysis_window.show()

            # 延迟居中，确保窗口完全显示后再调整位置
            QTimer.singleShot(50, analysis_window.center_on_parent)

        except Exception as e:
            print(f"显示类目分析窗口失败: {str(e)}")
            QMessageBox.warning(self, "错误", f"显示类目分析窗口失败: {str(e)}")
    
    def on_supplier_query_completed(self, success_count, total_count):
        """上家查询完成回调 - 优化版本：自动启动上家商品在线状态查询"""
        try:
            if success_count > 0:
                self.status_label.setText(f"上家ID查询完成：成功 {success_count}/{total_count} 个商品，开始查询上家商品在线状态...")

                # 清理上家查询线程
                if hasattr(self, 'supplier_thread'):
                    self.supplier_thread.quit()
                    self.supplier_thread.wait()
                    self.supplier_thread = None

                if hasattr(self, 'supplier_by_shop_thread'):
                    self.supplier_by_shop_thread.quit()
                    self.supplier_by_shop_thread.wait()
                    self.supplier_by_shop_thread = None

                # 自动启动上家商品在线状态查询
                self.start_supplier_status_query_after_id_query()

            else:
                # 没有找到上家信息，直接结束
                self.progress_bar.setVisible(False)
                self.status_label.setText(f"上家查询完成：未找到任何有效的上家信息")

                # 清理线程
                if hasattr(self, 'supplier_thread'):
                    self.supplier_thread.quit()
                    self.supplier_thread.wait()
                    self.supplier_thread = None

                if hasattr(self, 'supplier_by_shop_thread'):
                    self.supplier_by_shop_thread.quit()
                    self.supplier_by_shop_thread.wait()
                    self.supplier_by_shop_thread = None

        except Exception as e:
            print(f"上家查询完成回调处理失败: {str(e)}")
            self.progress_bar.setVisible(False)
            self.status_label.setText(f"上家查询完成回调处理失败: {str(e)}")

            # 清理线程
            if hasattr(self, 'supplier_thread'):
                self.supplier_thread.quit()
                self.supplier_thread.wait()
                self.supplier_thread = None

            if hasattr(self, 'supplier_by_shop_thread'):
                self.supplier_by_shop_thread.quit()
                self.supplier_by_shop_thread.wait()
                self.supplier_by_shop_thread = None

    def start_supplier_status_query_after_id_query(self):
        """在上家ID查询完成后启动上家商品在线状态查询"""
        try:
            # 获取表格中有上家ID的商品数据
            products_with_supplier = []
            row_count = self.product_table.rowCount()

            # 通过表头名找到相关列
            product_id_col = self.find_column_by_header_name("商品ID")
            supplier_col = self.find_column_by_header_name("上家")
            shop_col = self.find_column_by_header_name("店铺")
            title_col = self.find_column_by_header_name("商品标题")

            if product_id_col == -1 or supplier_col == -1:
                print("未找到必要的列（商品ID或上家列），无法启动上家状态查询")
                self.progress_bar.setVisible(False)
                self.status_label.setText("未找到必要的列，无法查询上家状态")
                return

            # 收集有上家ID的商品
            for row in range(row_count):
                product_id_item = self.product_table.item(row, product_id_col)
                supplier_item = self.product_table.item(row, supplier_col)

                if product_id_item and supplier_item:
                    product_id = product_id_item.text().strip()
                    supplier_id = supplier_item.text().strip()

                    if product_id and supplier_id and supplier_id != "未找到上家信息":
                        # 获取其他信息
                        shop_name = ""
                        title = ""

                        if shop_col != -1:
                            shop_item = self.product_table.item(row, shop_col)
                            if shop_item:
                                shop_name = shop_item.text().strip()

                        if title_col != -1:
                            title_item = self.product_table.item(row, title_col)
                            if title_item:
                                title = title_item.text().strip()

                        products_with_supplier.append({
                            'row': row,
                            'product_id': product_id,
                            'supplier_id': supplier_id,
                            'shop_name': shop_name,
                            'title': title
                        })

            if not products_with_supplier:
                self.progress_bar.setVisible(False)
                self.status_label.setText("没有找到有上家ID的商品，无法查询上家状态")
                return

            print(f"找到 {len(products_with_supplier)} 个有上家ID的商品，开始查询上家状态")

            # 更新进度条
            self.progress_bar.setRange(0, len(products_with_supplier))
            self.progress_bar.setValue(0)
            self.progress_bar.setVisible(True)

            # 启动上家状态查询线程（禁用自动下架功能）
            self.supplier_status_thread = SupplierStatusThread(products_with_supplier, self, enable_auto_delist=False)
            self.supplier_status_thread.progress_updated.connect(self.update_supplier_status_progress)
            self.supplier_status_thread.row_updated.connect(self.update_supplier_status_row)
            self.supplier_status_thread.query_completed.connect(self.on_supplier_status_query_completed)
            self.supplier_status_thread.supplier_name_updated.connect(self.on_supplier_name_updated)  # 连接上家名称更新信号
            self.supplier_status_thread.start()

        except Exception as e:
            print(f"启动上家状态查询失败: {str(e)}")
            self.progress_bar.setVisible(False)
            self.status_label.setText(f"启动上家状态查询失败: {str(e)}")

    def on_supplier_status_query_completed(self, success_count, total_count):
        """上家状态查询完成回调"""
        try:
            # 隐藏进度条
            self.progress_bar.setVisible(False)

            if success_count > 0:
                self.status_label.setText(f"上家信息查询全部完成：上家ID查询成功，上家状态查询成功 {success_count}/{total_count} 个商品，所有信息已保存")
            else:
                self.status_label.setText(f"上家信息查询完成：上家ID查询成功，但上家状态查询未找到有效信息")

            # 清理线程
            if hasattr(self, 'supplier_status_thread'):
                self.supplier_status_thread.quit()
                self.supplier_status_thread.wait()
                self.supplier_status_thread = None

        except Exception as e:
            print(f"上家状态查询完成回调处理失败: {str(e)}")
            self.progress_bar.setVisible(False)
            self.status_label.setText(f"上家状态查询完成回调处理失败: {str(e)}")

            # 清理线程
            if hasattr(self, 'supplier_status_thread'):
                self.supplier_status_thread.quit()
                self.supplier_status_thread.wait()
                self.supplier_status_thread = None
    
    def load_supplier_info_from_cache(self):
        """异步从缓存（SQLite数据库）加载上家信息并显示在表格中（按店铺优化加载）"""
        # 获取表格中的店铺名称
        shop_names = self.get_shop_names_from_table()

        if shop_names:
            # 如果只有一个店铺，按店铺加载
            if len(shop_names) == 1:
                shop_name = list(shop_names)[0]
                print(f"检测到单店铺 '{shop_name}'，使用优化加载")
                self.start_supplier_cache_loading(shop_name)
            else:
                # 多店铺情况，使用统一文件加载
                print(f"检测到多店铺 ({len(shop_names)} 个)，使用统一文件加载")
                self.start_supplier_cache_loading()
        else:
            # 没有店铺信息，使用默认加载
            self.start_supplier_cache_loading()

    def start_supplier_cache_loading(self, shop_name=None):
        """启动上家缓存加载线程（支持按店铺加载）"""
        try:
            # 如果已有线程在运行，先停止
            if hasattr(self, 'supplier_cache_thread') and self.supplier_cache_thread and self.supplier_cache_thread.isRunning():
                self.supplier_cache_thread.quit()
                self.supplier_cache_thread.wait()

            # 创建并启动上家缓存加载线程（传入店铺名称）
            self.supplier_cache_thread = SupplierCacheLoadThread(shop_name)
            self.supplier_cache_thread.cache_loaded.connect(self.on_supplier_cache_loaded)
            self.supplier_cache_thread.loading_failed.connect(self.on_supplier_cache_failed)
            self.supplier_cache_thread.start()

        except Exception as e:
            print(f"启动上家缓存加载线程失败: {str(e)}")

    def find_column_by_header_name(self, header_name):
        """通过表头名查找列索引"""
        for col in range(self.product_table.columnCount()):
            header_item = self.product_table.horizontalHeaderItem(col)
            if header_item and header_item.text() == header_name:
                return col
        return -1

    def on_supplier_cache_loaded(self, supplier_data):
        """上家缓存加载完成回调"""
        try:
            # 遍历表格中的每一行，匹配上家信息
            row_count = self.product_table.rowCount()
            matched_count = 0
            missing_products = []  # 记录缺失上家信息的商品

            # 通过表头名找到商品ID列
            product_id_col = self.find_column_by_header_name("商品ID")
            if product_id_col == -1:
                print("未找到商品ID列，无法匹配上家信息")
                return

            for row in range(row_count):
                # 获取商品ID
                product_id_item = self.product_table.item(row, product_id_col)
                if product_id_item:
                    product_id = product_id_item.text().strip()

                    # 在数据中查找对应的上家信息
                    if supplier_data and product_id in supplier_data:
                        supplier_info_data = supplier_data[product_id]

                        # 兼容旧格式（只有supplier_id）和新格式（包含supplier_name和category_name）
                        if isinstance(supplier_info_data, dict):
                            supplier_id = supplier_info_data.get('supplier_id', '')
                            supplier_name = supplier_info_data.get('supplier_name', '')
                            category_name = supplier_info_data.get('category_name', '')
                        else:
                            # 旧格式，只有supplier_id
                            supplier_id = str(supplier_info_data)
                            supplier_name = ''
                            category_name = ''

                        # 确保上家ID为纯数字格式
                        processed_supplier_id = self.extract_numbers_from_text(supplier_id)

                        # 通过表头名找到上家列、上家名称列和类目列
                        supplier_col = self.find_column_by_header_name("上家")
                        supplier_name_col = self.find_column_by_header_name("上家名称")
                        category_col = self.find_column_by_header_name("类目")

                        # 设置上家列
                        if supplier_col != -1:
                            supplier_item = QTableWidgetItem(processed_supplier_id)
                            supplier_item.setTextAlignment(Qt.AlignCenter)
                            self.product_table.setItem(row, supplier_col, supplier_item)

                        # 设置上家名称列
                        if supplier_name_col != -1:
                            supplier_name_item = QTableWidgetItem(supplier_name)
                            supplier_name_item.setTextAlignment(Qt.AlignCenter)
                            self.product_table.setItem(row, supplier_name_col, supplier_name_item)

                        # 设置类目列
                        if category_col != -1 and category_name:
                            category_item = QTableWidgetItem(category_name)
                            category_item.setTextAlignment(Qt.AlignCenter)
                            self.product_table.setItem(row, category_col, category_item)

                        matched_count += 1
                    else:
                        # 记录缺失上家信息的商品
                        missing_products.append({
                            'row': row,
                            'product_id': product_id
                        })

            print(f"上家信息缓存匹配完成: 共匹配 {matched_count}/{row_count} 个商品")

            # 如果有缺失的上家信息，只记录但不自动查询
            if missing_products:
                print(f"发现 {len(missing_products)} 个商品缺失上家信息，等待用户手动触发查询")
                # 不自动启动查询，让用户手动决定是否查询
                # self.query_missing_supplier_info(missing_products)
            else:
                # 更新状态标签
                if matched_count > 0:
                    current_status = self.status_label.text()
                    self.status_label.setText(f"{current_status}，已加载 {matched_count} 个商品的上家信息")

                # 暂时禁用自动查询，避免无限循环
                print("上家信息加载完成，但暂时禁用自动查询以避免无限循环")
                # TODO: 修复线程属性问题后再启用自动查询
                # if not hasattr(self, 'auto_status_check_started') or not self.auto_status_check_started:
                #     self.auto_status_check_started = True
                #     QTimer.singleShot(1000, self.check_supplier_status)

        except Exception as e:
            print(f"处理上家缓存数据失败: {str(e)}")
            self.status_label.setText(f"处理上家缓存数据失败: {str(e)}")

    def on_supplier_cache_failed(self, error_msg):
        """上家缓存加载失败回调"""
        print(f"加载上家信息失败: {error_msg}")
        self.status_label.setText(f"加载上家信息失败: {error_msg}")



    def query_missing_supplier_info(self, missing_products):
        """查询缺失的上家信息"""
        try:
            if not missing_products:
                return

            # 获取当前店铺名称
            current_shop_name = None
            if hasattr(self, 'current_shop_name') and self.current_shop_name:
                current_shop_name = self.current_shop_name
            else:
                # 尝试从表格中获取店铺名称
                if self.product_table.rowCount() > 0:
                    shop_item = self.product_table.item(0, 9)  # 第9列是店铺名称
                    if shop_item:
                        current_shop_name = shop_item.text().strip()

            if not current_shop_name:
                print("无法确定当前店铺名称，跳过上家信息查询")
                return

            print(f"开始查询店铺 {current_shop_name} 的 {len(missing_products)} 个商品的上家信息")

            # 更新状态
            self.status_label.setText(f"正在查询 {len(missing_products)} 个商品的上家信息...")

            # 启动查询线程
            self.start_missing_supplier_query(current_shop_name, missing_products)

        except Exception as e:
            print(f"启动上家信息查询失败: {str(e)}")
            self.status_label.setText(f"启动上家信息查询失败: {str(e)}")

    def start_missing_supplier_query(self, shop_name, missing_products):
        """启动缺失上家信息的查询线程"""
        try:
            # 如果已有查询线程在运行，先停止
            if hasattr(self, 'missing_supplier_thread') and self.missing_supplier_thread and self.missing_supplier_thread.isRunning():
                self.missing_supplier_thread.quit()
                self.missing_supplier_thread.wait()

            # 创建并启动查询线程
            self.missing_supplier_thread = MissingSupplierQueryThread(shop_name, missing_products, self)
            self.missing_supplier_thread.query_progress.connect(self.on_missing_supplier_progress)
            self.missing_supplier_thread.query_completed.connect(self.on_missing_supplier_completed)
            self.missing_supplier_thread.query_failed.connect(self.on_missing_supplier_failed)
            self.missing_supplier_thread.row_updated.connect(self.update_supplier_row)  # 连接行更新信号
            self.missing_supplier_thread.start()

        except Exception as e:
            print(f"启动缺失上家信息查询线程失败: {str(e)}")

    def on_missing_supplier_progress(self, progress_msg):
        """缺失上家信息查询进度回调"""
        self.status_label.setText(progress_msg)

    def on_missing_supplier_completed(self, queried_count, total_count):
        """缺失上家信息查询完成回调"""
        print(f"上家信息查询完成: 成功查询 {queried_count}/{total_count} 个商品")
        self.status_label.setText(f"上家信息查询完成: 成功查询 {queried_count}/{total_count} 个商品")

        # 不再重新加载上家信息，避免无限循环
        # 因为查询线程已经通过 row_updated 信号实时更新了表格
        print("缺失上家信息查询完成，已通过实时更新完成表格填充")

        # 暂时禁用自动查询，避免无限循环
        print("缺失上家信息查询完成，但暂时禁用自动查询以避免无限循环")
        # TODO: 修复线程属性问题后再启用自动查询
        # if not hasattr(self, 'auto_status_check_started') or not self.auto_status_check_started:
        #     self.auto_status_check_started = True
        #     QTimer.singleShot(2000, self.check_supplier_status)

    def on_missing_supplier_failed(self, error_msg):
        """缺失上家信息查询失败回调"""
        print(f"上家信息查询失败: {error_msg}")
        self.status_label.setText(f"上家信息查询失败: {error_msg}")
    
    def check_supplier_status(self):
        """查询上家商品状态 - 逐店铺处理模式"""
        try:
            # 重置自动查询标志
            self.auto_status_check_started = False

            # 获取勾选的店铺
            checked_shops = self.category_tree.get_checked_shops()
            if not checked_shops:
                QMessageBox.warning(self, "提示", "请先在左侧分类树中勾选店铺")
                return

            # 清空当前表格
            self.product_table.setRowCount(0)

            # 禁用排序功能，防止在查询过程中触发排序导致数据不稳定
            self.product_table.setSortingEnabled(False)

            # 更新商品数量显示
            self.update_product_count_display()

            # 确认逐店铺查询操作
            reply = QMessageBox.question(self, "确认逐店铺查询",
                                       f"将逐个处理 {len(checked_shops)} 个勾选的店铺：\n\n"
                                       f"处理流程：\n"
                                       f"• 每个店铺单独加载商品\n"
                                       f"• 每个店铺单独查询上家状态\n"
                                       f"• 处理完一个店铺清空表格再显示下一个\n"
                                       f"• 如果某店铺失败会继续处理下一个\n\n"
                                       f"勾选的店铺：\n" +
                                       "\n".join([f"  • {shop}" for shop in checked_shops[:10]]) +
                                       (f"\n  • ... 还有 {len(checked_shops)-10} 个店铺" if len(checked_shops) > 10 else "") +
                                       f"\n\n预计需要较长时间，确定继续吗？",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply != QMessageBox.Yes:
                return

            # 启动逐店铺查询
            self.start_shop_by_shop_query(checked_shops)

        except Exception as e:
            print(f"查询上家状态失败: {str(e)}")
            self.status_label.setText(f"查询上家状态失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"查询上家状态失败: {str(e)}")

    def start_shop_by_shop_query(self, checked_shops):
        """启动逐店铺查询"""
        try:
            # 显示进度条 - 设置为百分比模式（0-100）
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)

            # 初始化统计变量
            self.shop_query_stats = {
                'total_shops': len(checked_shops),
                'completed_shops': 0,
                'success_shops': 0,
                'total_products': 0,
                'total_success': 0,
                'current_shop': '',
                'failed_shops': []
            }

            # 创建并启动逐店铺查询线程
            self.shop_by_shop_thread = ShopByShopQueryThread(checked_shops, self)
            self.shop_by_shop_thread.progress_updated.connect(self.update_shop_query_progress)
            self.shop_by_shop_thread.shop_started.connect(self.on_shop_query_started)
            self.shop_by_shop_thread.shop_products_loaded.connect(self.on_shop_products_loaded)
            self.shop_by_shop_thread.page_products_loaded.connect(self.on_page_products_loaded)  # 新增实时加载信号连接
            self.shop_by_shop_thread.shop_query_completed.connect(self.on_shop_query_completed)
            self.shop_by_shop_thread.shop_failed.connect(self.on_shop_query_failed)
            self.shop_by_shop_thread.all_completed.connect(self.on_all_shops_query_completed)
            self.shop_by_shop_thread.all_completed_with_auto_delist.connect(self.on_all_shops_query_completed_with_auto_delist)  # 新增自动下架完成信号
            self.shop_by_shop_thread.auto_delist_required.connect(self.on_auto_delist_required)  # 新增自动下架需求信号
            self.shop_by_shop_thread.shop_auto_delist_required.connect(self.on_shop_auto_delist_required)  # 新增单店铺自动下架需求信号
            self.shop_by_shop_thread.row_status_updated.connect(self.on_row_status_updated)
            self.shop_by_shop_thread.supplier_name_updated.connect(self.on_supplier_name_updated)  # 新增上家名称更新信号
            self.shop_by_shop_thread.start()

            self.status_label.setText(f"开始逐店铺查询 {len(checked_shops)} 个店铺...")

        except Exception as e:
            print(f"启动逐店铺查询失败: {str(e)}")
            self.status_label.setText(f"启动逐店铺查询失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"启动逐店铺查询失败: {str(e)}")

    def update_shop_query_progress(self, message):
        """更新店铺查询进度"""
        # 检查消息是否包含进度百分比信息
        if '|' in message:
            # 分离消息和进度百分比
            parts = message.split('|')
            if len(parts) == 2:
                status_msg = parts[0]
                try:
                    progress_percent = int(parts[1])
                    # 更新进度条
                    self.progress_bar.setValue(progress_percent)
                    print(f"🔍 [DEBUG] 商品加载进度更新: {progress_percent}% - {status_msg}")
                except ValueError:
                    status_msg = message
                    print(f"🔍 [DEBUG] 进度解析失败，使用原消息: {message}")
            else:
                status_msg = message
        else:
            status_msg = message
            print(f"🔍 [DEBUG] 查询进度更新: {message}")

        self.status_label.setText(status_msg)

    def on_shop_query_started(self, shop_name, current_index, total_count):
        """店铺查询开始回调"""
        self.shop_query_stats['current_shop'] = shop_name

        # 清空表格，准备加载新店铺的商品
        self.product_table.setRowCount(0)
        print(f"🔍 [DEBUG] 店铺开始 - 清空表格，准备加载店铺 {shop_name} 的商品")

        # 重置进度条为0，准备显示商品加载进度
        self.progress_bar.setValue(0)
        print(f"🔍 [DEBUG] 店铺开始 - 重置进度条为0，准备加载商品")

        self.status_label.setText(f"正在处理店铺 {current_index}/{total_count}: {shop_name}")
        print(f"开始处理店铺 {current_index}/{total_count}: {shop_name}")

        # 强制刷新界面
        QApplication.processEvents()

    def on_shop_products_loaded(self, shop_name, products):
        """店铺商品加载完成回调 - 支持实时显示"""
        try:
            # 清空表格（仅在开始新店铺时）
            if not hasattr(self, '_current_shop_loading') or self._current_shop_loading != shop_name:
                self.product_table.setRowCount(0)
                self._current_shop_loading = shop_name

            if not products:
                self.status_label.setText(f"店铺 {shop_name} 没有商品")
                return

            # 实时显示商品到表格
            self.add_products_to_table(products)
            self.status_label.setText(f"店铺 {shop_name} 已加载 {self.product_table.rowCount()} 个商品，正在加载上家缓存...")

            # 更新商品数量显示
            self.update_product_count_display()

            # 加载上家缓存信息
            self.load_supplier_info_from_cache()
            self.status_label.setText(f"店铺 {shop_name} 已加载 {self.product_table.rowCount()} 个商品，上家缓存已加载，正在查询上家状态...")

        except Exception as e:
            print(f"处理店铺商品加载回调失败: {str(e)}")

    def on_page_products_loaded(self, shop_name, products):
        """单页商品实时加载回调"""
        try:
            if not products:
                return

            # 实时添加商品到表格
            self.add_products_to_table(products)

            # 更新状态显示
            current_count = self.product_table.rowCount()
            self.status_label.setText(f"店铺 {shop_name} 已实时加载 {current_count} 个商品...")

            # 更新商品数量显示
            self.update_product_count_display()

            print(f"实时显示: 店铺 {shop_name} 新增 {len(products)} 个商品，当前总计 {current_count} 个")

        except Exception as e:
            print(f"处理单页商品实时加载失败: {str(e)}")

    def on_shop_query_completed(self, shop_name, success_count, total_count):
        """单个店铺查询完成回调"""
        try:
            # 更新统计
            self.shop_query_stats['completed_shops'] += 1
            self.shop_query_stats['total_products'] += total_count
            self.shop_query_stats['total_success'] += success_count

            if success_count > 0 or total_count > 0:
                self.shop_query_stats['success_shops'] += 1

            # 更新进度条为100%（当前店铺完成）
            self.progress_bar.setValue(100)
            print(f"🔍 [DEBUG] 店铺完成 - 进度条设置为100%")

            # 显示当前店铺结果
            result_msg = f"店铺 {shop_name} 完成: 成功查询 {success_count}/{total_count} 个商品"
            self.status_label.setText(result_msg)
            print(result_msg)

        except Exception as e:
            print(f"处理店铺查询完成回调失败: {str(e)}")

    def on_shop_query_failed(self, shop_name, error_message):
        """店铺查询失败回调"""
        try:
            # 记录失败店铺
            self.shop_query_stats['failed_shops'].append({
                'shop_name': shop_name,
                'error': error_message
            })
            self.shop_query_stats['completed_shops'] += 1

            # 更新进度条
            self.progress_bar.setValue(self.shop_query_stats['completed_shops'])

            # 显示失败信息
            error_msg = f"店铺 {shop_name} 处理失败: {error_message}"
            self.status_label.setText(error_msg)
            print(error_msg)

            # 清空表格，准备下一个店铺
            self.product_table.setRowCount(0)

        except Exception as e:
            print(f"处理店铺查询失败回调失败: {str(e)}")

    def on_all_shops_query_completed(self, total_success, total_products, success_shops, total_shops):
        """所有店铺查询完成回调"""
        try:
            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 生成完成报告
            failed_count = len(self.shop_query_stats['failed_shops'])
            success_rate = (success_shops / total_shops * 100) if total_shops > 0 else 0

            report = f"逐店铺查询完成！\n\n"
            report += f"店铺统计：\n"
            report += f"  • 总店铺数: {total_shops}\n"
            report += f"  • 成功处理: {success_shops} ({success_rate:.1f}%)\n"
            report += f"  • 处理失败: {failed_count}\n\n"
            report += f"商品统计：\n"
            report += f"  • 总商品数: {total_products}\n"
            report += f"  • 成功查询: {total_success}\n"

            if self.shop_query_stats['failed_shops']:
                report += f"\n失败店铺：\n"
                for failed in self.shop_query_stats['failed_shops'][:5]:  # 只显示前5个
                    report += f"  • {failed['shop_name']}: {failed['error']}\n"
                if failed_count > 5:
                    report += f"  • ... 还有 {failed_count - 5} 个失败店铺\n"

            # 显示完成状态
            self.status_label.setText(f"逐店铺查询完成: {success_shops}/{total_shops} 个店铺成功，共查询 {total_success}/{total_products} 个商品")

            # 显示详细报告
            QMessageBox.information(self, "逐店铺查询完成", report)

        except Exception as e:
            print(f"处理所有店铺查询完成回调失败: {str(e)}")
            self.status_label.setText(f"查询完成，但处理结果时出错: {str(e)}")

    def on_all_shops_query_completed_with_auto_delist(self, total_success, total_products, success_shops, total_shops, auto_delist_success):
        """所有店铺查询完成且有自动下架需求的回调"""
        try:
            print(f"逐店铺查询完成，成功{total_success}/{total_products}，自动下架成功{auto_delist_success}个商品")

            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 直接显示最终结果，包含下架统计
            auto_delist_found = auto_delist_success
            if hasattr(self, 'shop_by_shop_thread') and self.shop_by_shop_thread:
                # 尝试从线程对象获取下架商品数量
                if hasattr(self.shop_by_shop_thread, 'all_products_to_auto_delist'):
                    auto_delist_found = len(self.shop_by_shop_thread.all_products_to_auto_delist)
                elif auto_delist_found == 0:
                    # 如果没有找到需要下架的商品，使用传入的成功数量作为发现数量
                    auto_delist_found = auto_delist_success

            self.show_final_query_result(
                total_success, total_products,
                success_shops, total_shops,
                auto_delist_found, auto_delist_success
            )

            # 清理查询线程
            if hasattr(self, 'shop_by_shop_thread'):
                self.shop_by_shop_thread.quit()
                self.shop_by_shop_thread.wait()
                self.shop_by_shop_thread = None

        except Exception as e:
            print(f"处理逐店铺查询完成且有自动下架需求回调失败: {str(e)}")

    def on_shop_auto_delist_required(self, shop_name, products_to_delist):
        """处理单个店铺的自动下架需求"""
        try:
            print(f"收到店铺 {shop_name} 的自动下架需求，需要下架 {len(products_to_delist)} 个商品")

            # 构建详细的商品信息列表用于日志记录
            product_details = []
            for product in products_to_delist:
                detail = f"商品ID: {product['product_id']} (第{product['row']+1}行) - {product['status']} - {product['reason']}"
                product_details.append(detail)

            print(f"店铺 {shop_name} 需要自动下架的商品详情:")
            for detail in product_details[:5]:  # 日志中显示前5个
                print(f"  {detail}")
            if len(products_to_delist) > 5:
                print(f"  ... 还有 {len(products_to_delist) - 5} 个商品")

            # 根据勾选店铺数量决定是否需要弹窗确认
            total_shops = getattr(self.shop_by_shop_thread, 'checked_shops', [])
            total_shop_count = len(total_shops) if total_shops else 1

            if total_shop_count == 1:
                # 单店铺操作：需要弹窗确认
                print(f"单店铺操作，需要用户确认下架")
                self.show_single_shop_auto_delist_confirmation(shop_name, products_to_delist)
            else:
                # 多店铺操作：静默自动下架
                print(f"多店铺操作（{total_shop_count}个店铺），静默自动下架（无弹窗）")
                self.start_auto_delist_for_products_silent_with_callback(products_to_delist, shop_name)

        except Exception as e:
            print(f"处理店铺 {shop_name} 自动下架需求失败: {str(e)}")
            # 即使出错也要通知等待的线程继续
            self.notify_shop_auto_delist_completed(shop_name)

    def show_single_shop_auto_delist_confirmation(self, shop_name, products_to_delist):
        """显示单店铺自动下架确认对话框"""
        try:
            if not products_to_delist:
                # 没有商品需要下架，直接通知完成
                self.notify_shop_auto_delist_completed(shop_name)
                return

            # 构建详细的商品信息列表
            product_details = []
            for product in products_to_delist:
                detail = f"商品ID: {product['product_id']} (第{product['row']+1}行) - {product['status']} - {product['reason']}"
                product_details.append(detail)

            # 显示确认对话框
            detail_text = "\n".join(product_details[:10])  # 最多显示前10个
            if len(products_to_delist) > 10:
                detail_text += f"\n... 还有 {len(products_to_delist) - 10} 个商品"

            reply = QMessageBox.question(
                self,
                f"店铺 {shop_name} 自动下架确认",
                f"店铺 {shop_name} 发现 {len(products_to_delist)} 个商品的供应商状态异常，建议自动下架：\n\n"
                f"{detail_text}\n\n"
                f"是否确认下架这些商品？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 用户确认，执行静默下架
                print(f"用户确认下架店铺 {shop_name} 的 {len(products_to_delist)} 个商品")
                self.start_auto_delist_for_products_silent_with_callback(products_to_delist, shop_name)
            else:
                # 用户取消，直接通知完成
                print(f"用户取消下架店铺 {shop_name} 的商品")
                self.notify_shop_auto_delist_completed(shop_name)

        except Exception as e:
            print(f"显示单店铺自动下架确认对话框时出错: {str(e)}")
            # 出错时也要通知完成，避免线程等待
            self.notify_shop_auto_delist_completed(shop_name)

    def start_auto_delist_for_products_silent_with_callback(self, products_to_delist, shop_name):
        """启动自动下架功能处理指定商品（无确认弹窗，带完成回调）"""
        try:
            print(f"开始静默自动下架 {len(products_to_delist)} 个商品（无弹窗，带回调）")

            # 更新状态
            self.status_label.setText(f"正在自动下架店铺 {shop_name} 的 {len(products_to_delist)} 个异常商品...")

            # 转换商品格式为现有自动下架功能需要的格式
            product_operations = []
            for item in products_to_delist:
                operation = {
                    'product_id': item['product_id'],
                    'row': item['row'],
                    'shop_name': item['shop_name'],
                    'title': item.get('title', ''),
                    'reason': item['reason']
                }
                product_operations.append(operation)

            # 直接执行自动下架操作，不显示确认弹窗
            self.perform_auto_delist_silent_with_callback(product_operations, shop_name)

        except Exception as e:
            print(f"静默自动下架失败: {str(e)}")
            # 即使失败也要通知完成，避免阻塞
            self.notify_shop_auto_delist_completed(shop_name)

    def perform_auto_delist_silent_with_callback(self, products_to_delist, shop_name):
        """执行自动下架操作（无弹窗版本，带完成回调）"""
        try:
            print(f"开始执行静默自动下架操作，共 {len(products_to_delist)} 个商品")

            # 获取商品ID列、店铺名称列和累销列的索引
            product_id_col_index = -1
            shop_name_col_index = -1
            sales_col_index = -1

            for col in range(self.product_table.columnCount()):
                header_item = self.product_table.horizontalHeaderItem(col)
                if header_item:
                    if header_item.text() == "商品ID":
                        product_id_col_index = col
                    elif header_item.text() == "店铺":
                        shop_name_col_index = col
                    elif header_item.text() == "累销":
                        sales_col_index = col

            if product_id_col_index == -1 or shop_name_col_index == -1:
                print("错误: 无法找到商品ID列或店铺列，无法执行自动下架")
                self.notify_shop_auto_delist_completed(shop_name)
                return

            if sales_col_index == -1:
                print("错误: 无法找到累销列，无法执行自动下架")
                self.notify_shop_auto_delist_completed(shop_name)
                return

            # 构建下架操作列表，跳过累销大于0的商品
            product_operations = []
            skipped_count = 0

            for item in products_to_delist:
                row = item.get('row', -1)
                if row >= 0 and row < self.product_table.rowCount():
                    # 检查累销值
                    sales_item = self.product_table.item(row, sales_col_index)
                    sales_value = 0
                    if sales_item:
                        try:
                            sales_text = sales_item.text().strip()
                            if sales_text and sales_text != '':
                                sales_value = int(sales_text)
                        except (ValueError, AttributeError):
                            sales_value = 0

                    if sales_value > 0:
                        print(f"跳过商品 {item['product_id']} (第{row+1}行)：累销 {sales_value} > 0")
                        skipped_count += 1
                        continue

                    # 获取商品ID和店铺名称
                    product_id_item = self.product_table.item(row, product_id_col_index)
                    shop_name_item = self.product_table.item(row, shop_name_col_index)

                    if product_id_item and shop_name_item:
                        operation = {
                            'product_id': product_id_item.text(),
                            'shop_name': shop_name_item.text(),
                            'row': row,
                            'title': item.get('title', ''),
                            'reason': item.get('reason', '上家商品异常')
                        }
                        product_operations.append(operation)

            if skipped_count > 0:
                print(f"跳过了 {skipped_count} 个已有销量的商品")

            if not product_operations:
                print("没有需要下架的商品（所有商品都有销量或数据异常）")
                self.notify_shop_auto_delist_completed(shop_name)
                return

            print(f"实际需要下架 {len(product_operations)} 个商品")

            # 调用下架操作方法，获取成功数量
            success_count = self.perform_delist_operation_with_progress(product_operations)

            # 下架操作完成后通知，传递成功数量
            print(f"店铺 {shop_name} 的自动下架操作已完成，成功下架 {success_count} 个商品")
            self.notify_shop_auto_delist_completed(shop_name, success_count)

        except Exception as e:
            print(f"执行静默自动下架操作失败: {str(e)}")
            self.notify_shop_auto_delist_completed(shop_name)

    def notify_shop_auto_delist_completed(self, shop_name, success_count=0):
        """通知店铺自动下架操作完成"""
        try:
            print(f"店铺 {shop_name} 自动下架操作完成，成功 {success_count} 个，通知等待的线程")
            # 通知逐店铺查询线程可以继续下一个店铺，传递成功数量
            if hasattr(self, 'shop_by_shop_thread') and self.shop_by_shop_thread:
                self.shop_by_shop_thread.notify_auto_delist_completed(success_count)
        except Exception as e:
            print(f"通知店铺自动下架完成失败: {str(e)}")

    def start_auto_delist_for_products_silent(self, products_to_delist):
        """启动自动下架功能处理指定商品（无确认弹窗，用于逐店铺查询）"""
        try:
            print(f"开始静默自动下架 {len(products_to_delist)} 个商品（无弹窗）")

            # 更新状态
            self.status_label.setText(f"正在自动下架 {len(products_to_delist)} 个异常商品...")

            # 转换商品格式为现有自动下架功能需要的格式
            product_operations = []
            for item in products_to_delist:
                operation = {
                    'product_id': item['product_id'],
                    'row': item['row'],
                    'shop_name': item['shop_name'],
                    'title': item.get('title', ''),
                    'reason': item['reason']
                }
                product_operations.append(operation)

            # 直接执行自动下架操作，不显示确认弹窗
            self.perform_auto_delist_silent(product_operations)

        except Exception as e:
            print(f"静默自动下架失败: {str(e)}")

    def perform_auto_delist_silent(self, products_to_delist):
        """执行自动下架操作（无弹窗版本）"""
        try:
            print(f"开始执行静默自动下架操作，共 {len(products_to_delist)} 个商品")

            # 获取商品ID列、店铺名称列和累销列的索引
            product_id_col_index = -1
            shop_name_col_index = -1
            sales_col_index = -1

            for col in range(self.product_table.columnCount()):
                header_item = self.product_table.horizontalHeaderItem(col)
                if header_item:
                    if header_item.text() == "商品ID":
                        product_id_col_index = col
                    elif header_item.text() == "店铺":
                        shop_name_col_index = col
                    elif header_item.text() == "累销":
                        sales_col_index = col

            if product_id_col_index == -1 or shop_name_col_index == -1:
                print("错误: 无法找到商品ID列或店铺列，无法执行自动下架")
                return

            if sales_col_index == -1:
                print("错误: 无法找到累销列，无法执行自动下架")
                return

            # 构建下架操作列表，跳过累销大于0的商品
            product_operations = []
            skipped_count = 0

            for item in products_to_delist:
                row = item.get('row', -1)
                if row >= 0 and row < self.product_table.rowCount():
                    # 检查累销值
                    sales_item = self.product_table.item(row, sales_col_index)
                    sales_value = 0
                    if sales_item:
                        try:
                            sales_text = sales_item.text().strip()
                            if sales_text and sales_text != '':
                                sales_value = int(sales_text)
                        except (ValueError, AttributeError):
                            sales_value = 0

                    if sales_value > 0:
                        print(f"跳过商品 {item['product_id']} (第{row+1}行)：累销 {sales_value} > 0")
                        skipped_count += 1
                        continue

                    # 获取商品ID和店铺名称
                    product_id_item = self.product_table.item(row, product_id_col_index)
                    shop_name_item = self.product_table.item(row, shop_name_col_index)

                    if product_id_item and shop_name_item:
                        operation = {
                            'product_id': product_id_item.text(),
                            'shop_name': shop_name_item.text(),
                            'row': row,
                            'title': item.get('title', ''),
                            'reason': item.get('reason', '上家商品异常')
                        }
                        product_operations.append(operation)

            if skipped_count > 0:
                print(f"跳过了 {skipped_count} 个已有销量的商品")

            if not product_operations:
                print("没有需要下架的商品（所有商品都有销量或数据异常）")
                return

            print(f"实际需要下架 {len(product_operations)} 个商品")

            # 调用下架操作方法
            self.perform_delist_operation_with_progress(product_operations)

            print(f"静默自动下架操作完成")

        except Exception as e:
            print(f"执行静默自动下架操作失败: {str(e)}")

    def on_auto_delist_required(self, products_to_delist):
        """处理自动下架需求（逐店铺查询专用）"""
        try:
            print(f"收到逐店铺查询的自动下架需求，需要下架 {len(products_to_delist)} 个商品")

            # 构建详细的商品信息列表用于日志记录
            product_details = []
            for product in products_to_delist:
                detail = f"商品ID: {product['product_id']} (第{product['row']+1}行) - {product['status']} - {product['reason']}"
                product_details.append(detail)

            print(f"需要自动下架的商品详情:")
            for detail in product_details[:10]:  # 日志中显示前10个
                print(f"  {detail}")
            if len(products_to_delist) > 10:
                print(f"  ... 还有 {len(products_to_delist) - 10} 个商品")

            # 逐店铺查询完成后自动执行下架，不显示确认弹窗
            print("逐店铺查询完成，自动执行下架操作（无弹窗）")
            self.start_auto_delist_for_products_silent(products_to_delist)

        except Exception as e:
            print(f"处理自动下架需求失败: {str(e)}")
            # 出错时也要显示查询完成结果
            if hasattr(self, 'pending_query_result'):
                result = self.pending_query_result
                self.show_final_query_result(result['success_count'], result['total_count'],
                                           result['success_shops'], result['total_shops'],
                                           result['auto_delist_count'], 0)

    def start_auto_delist_for_products(self, products_to_delist):
        """启动自动下架功能处理指定商品"""
        try:
            print(f"开始自动下架 {len(products_to_delist)} 个商品")

            # 更新状态
            self.status_label.setText(f"正在自动下架 {len(products_to_delist)} 个异常商品...")

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, len(products_to_delist))
            self.progress_bar.setValue(0)

            # 转换商品格式为现有自动下架功能需要的格式
            product_operations = []
            for item in products_to_delist:
                operation = {
                    'product_id': item['product_id'],
                    'row': item['row'],
                    'shop_name': item['shop_name'],
                    'title': item.get('title', ''),
                    'reason': item['reason']
                }
                product_operations.append(operation)

            # 使用现有的自动下架功能
            self.perform_auto_delist_with_progress(product_operations)

        except Exception as e:
            print(f"启动自动下架功能失败: {str(e)}")
            # 如果自动下架启动失败，直接显示查询结果
            if hasattr(self, 'pending_query_result'):
                result = self.pending_query_result
                self.show_final_query_result(result['success_count'], result['total_count'],
                                           result['success_shops'], result['total_shops'],
                                           result['auto_delist_count'], 0)

    def perform_auto_delist_with_progress(self, product_operations):
        """执行自动下架操作并显示进度"""
        try:
            print(f"开始执行自动下架操作，共 {len(product_operations)} 个商品")

            success_count = 0
            total_count = len(product_operations)

            # 调用现有的下架操作方法
            self.perform_delist_operation_with_progress(product_operations)

            # 统计成功数量（通过检查操作列状态）
            for operation in product_operations:
                row = operation.get('row', -1)
                if row >= 0:
                    # 通过表头索引找到操作列
                    operation_col = -1
                    for col in range(self.product_table.columnCount()):
                        header_item = self.product_table.horizontalHeaderItem(col)
                        if header_item and header_item.text() == "操作":
                            operation_col = col
                            break

                    if operation_col >= 0:
                        status_item = self.product_table.item(row, operation_col)
                        if status_item and "成功" in status_item.text():
                            success_count += 1

            # 隐藏进度条
            self.progress_bar.setVisible(False)

            print(f"自动下架完成: {success_count}/{total_count}")

            # 显示最终结果
            if hasattr(self, 'pending_query_result'):
                result = self.pending_query_result
                self.show_final_query_result(result['success_count'], result['total_count'],
                                           result['success_shops'], result['total_shops'],
                                           result['auto_delist_count'], success_count)

        except Exception as e:
            print(f"执行自动下架操作失败: {str(e)}")
            # 隐藏进度条
            self.progress_bar.setVisible(False)
            # 显示失败结果
            if hasattr(self, 'pending_query_result'):
                result = self.pending_query_result
                self.show_final_query_result(result['success_count'], result['total_count'],
                                           result['success_shops'], result['total_shops'],
                                           result['auto_delist_count'], 0)

    def show_final_query_result(self, query_success, query_total, success_shops, total_shops, auto_delist_found, auto_delist_success):
        """显示最终的查询和自动下架结果"""
        try:
            # 生成完成报告
            failed_count = len(self.shop_query_stats.get('failed_shops', []))
            success_rate = (success_shops / total_shops * 100) if total_shops > 0 else 0

            report = f"逐店铺查询和自动下架完成！\n\n"
            report += f"店铺统计：\n"
            report += f"  • 总店铺数: {total_shops}\n"
            report += f"  • 成功处理: {success_shops} ({success_rate:.1f}%)\n"
            report += f"  • 处理失败: {failed_count}\n\n"
            report += f"商品查询统计：\n"
            report += f"  • 总商品数: {query_total}\n"
            report += f"  • 成功查询: {query_success}\n\n"

            if auto_delist_found > 0:
                auto_delist_rate = (auto_delist_success / auto_delist_found * 100) if auto_delist_found > 0 else 0
                report += f"自动下架统计（已自动执行）：\n"
                report += f"  • 发现异常商品: {auto_delist_found} 个\n"
                report += f"  • 自动下架成功: {auto_delist_success} 个 ({auto_delist_rate:.1f}%)\n"
                report += f"  • 下架失败: {auto_delist_found - auto_delist_success} 个\n"
                if auto_delist_success > 0:
                    report += f"\n✅ 系统已自动下架异常商品，无需手动操作\n"
            else:
                report += f"自动下架统计：\n"
                report += f"  • 未发现需要下架的异常商品\n"

            if self.shop_query_stats.get('failed_shops'):
                report += f"\n失败店铺：\n"
                for failed in self.shop_query_stats['failed_shops'][:5]:  # 只显示前5个
                    report += f"  • {failed['shop_name']}: {failed['error']}\n"
                if failed_count > 5:
                    report += f"  • ... 还有 {failed_count - 5} 个失败店铺\n"

            # 显示完成状态
            if auto_delist_found > 0:
                self.status_label.setText(f"全部完成: 查询 {query_success}/{query_total}，自动下架 {auto_delist_success}/{auto_delist_found}")
            else:
                self.status_label.setText(f"查询完成: {query_success}/{query_total}，无需自动下架")

            # 显示详细报告
            QMessageBox.information(self, "逐店铺查询和自动下架完成", report)

        except Exception as e:
            print(f"显示最终查询结果失败: {str(e)}")

    def on_row_status_updated(self, row, status_text, status_color):
        """实时更新表格行的状态 - 更新到操作列"""
        try:
            # 检查行号是否有效
            if row < 0 or row >= self.product_table.rowCount():
                print(f"无效的行号: {row}, 表格总行数: {self.product_table.rowCount()}")
                return

            # 通过表头索引找到操作列
            operation_col = -1
            for col in range(self.product_table.columnCount()):
                header_item = self.product_table.horizontalHeaderItem(col)
                if header_item and header_item.text() == "操作":
                    operation_col = col
                    break

            if operation_col == -1:
                print("未找到操作列")
                return

            # 创建状态项
            status_item = QTableWidgetItem(status_text)
            status_item.setBackground(QColor(status_color))
            status_item.setForeground(QColor("#FFFFFF"))  # 白色文字
            status_item.setTextAlignment(Qt.AlignCenter)

            # 设置到表格中
            self.product_table.setItem(row, operation_col, status_item)

            # 刷新表格显示
            self.product_table.viewport().update()

            print(f"已更新第 {row} 行操作列状态: {status_text}")

        except Exception as e:
            print(f"更新表格行状态失败: {str(e)}")

    def on_supplier_name_updated(self, row, supplier_name):
        """处理上家名称更新"""
        try:
            # 检查行号是否有效
            if row < 0 or row >= self.product_table.rowCount():
                print(f"无效的行号: {row}, 表格总行数: {self.product_table.rowCount()}")
                return

            # 通过表头索引找到上家名称列
            supplier_name_col = -1
            for col in range(self.product_table.columnCount()):
                header_item = self.product_table.horizontalHeaderItem(col)
                if header_item and header_item.text() == "上家名称":
                    supplier_name_col = col
                    break

            if supplier_name_col == -1:
                print("未找到上家名称列")
                return

            # 创建上家名称项，确保一对一显示
            # 如果supplier_name为空，显示空内容，确保清空旧数据
            display_name = supplier_name.strip() if supplier_name else ""
            supplier_name_item = QTableWidgetItem(display_name)
            supplier_name_item.setTextAlignment(Qt.AlignCenter)

            # 设置到表格中
            self.product_table.setItem(row, supplier_name_col, supplier_name_item)

            # 刷新表格显示
            self.product_table.viewport().update()

            if display_name:
                print(f"已更新第 {row} 行上家名称: {display_name}")
            else:
                print(f"已清空第 {row} 行上家名称（无上家信息）")

        except Exception as e:
            print(f"更新上家名称失败: {str(e)}")

    def update_supplier_status_row(self, row, status_text, status_color="#000000"):
        """更新单行的上家状态信息 - 添加诊断功能"""
        try:
            # 验证行号有效性
            current_row_count = self.product_table.rowCount()
            if row < 0 or row >= current_row_count:
                print(f"警告: 无效行号 {row}，当前表格行数: {current_row_count}")
                return
            
            # 获取商品信息用于诊断
            product_id_item = self.product_table.item(row, 2)
            product_id = product_id_item.text().strip() if product_id_item else "未知"
            
            # 检查现有状态，避免重复更新
            existing_status_item = self.product_table.item(row, 8)
            existing_status = existing_status_item.text() if existing_status_item else ""
            
            if existing_status == status_text:
                print(f"行{row}(商品{product_id}) 状态相同，跳过更新: {status_text}")
                return
            
            # 更新表格第8列（操作列）
            status_item = QTableWidgetItem(status_text)
            status_item.setForeground(QColor(status_color))
            self.product_table.setItem(row, 8, status_item)
            
            # 确保更新成功
            updated_item = self.product_table.item(row, 8)
            if updated_item and updated_item.text() == status_text:
                print(f"✓ 行{row}(商品{product_id}) 状态更新成功: {existing_status} -> {status_text}")
            else:
                print(f"✗ 行{row}(商品{product_id}) 状态更新失败: 期望{status_text}, 实际{updated_item.text() if updated_item else '空'}")
                
        except Exception as e:
            print(f"更新第{row}行上家状态信息失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def on_supplier_status_query_completed(self, success_count, total_count):
        """上家状态查询完成回调 - 添加状态验证"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 验证实际更新的状态数量
        updated_count = 0
        empty_count = 0
        status_distribution = {}
        
        current_row_count = self.product_table.rowCount()
        
        # 通过表头索引找到操作列
        operation_col = -1
        for col in range(self.product_table.columnCount()):
            header_item = self.product_table.horizontalHeaderItem(col)
            if header_item and header_item.text() == "操作":
                operation_col = col
                break

        if operation_col == -1:
            print("未找到操作列，无法验证状态")
            return

        for row in range(current_row_count):
            try:
                # 状态现在显示在操作列
                operation_item = self.product_table.item(row, operation_col)  # 操作列

                # 检查是否有状态信息
                if operation_item and operation_item.text().strip():
                    status_text = operation_item.text().strip()
                    # 如果是状态信息（包含特定关键词），则统计
                    if any(keyword in status_text for keyword in ['在线', '下架', '不存在', '过期', '删除', '审核', '失败', '异常']):
                        updated_count += 1
                        status_distribution[status_text] = status_distribution.get(status_text, 0) + 1
                    else:
                        # 如果是其他操作信息，不统计为状态
                        empty_count += 1
                else:
                    empty_count += 1
            except Exception as e:
                print(f"验证第{row}行状态时出错: {str(e)}")
        
        # 详细统计报告
        print(f"\n=== 查询完成验证报告 ===")
        print(f"查询统计: 成功{success_count}/{total_count}")
        print(f"状态更新: 已更新{updated_count}个, 状态为空{empty_count}个")
        
        if status_distribution:
            print(f"状态分布:")
            for status, count in status_distribution.items():
                print(f"  - {status}: {count}个")
        
        # 检查是否有状态丢失
        if updated_count < success_count:
            missing_count = success_count - updated_count
            print(f"⚠️ 警告: 有{missing_count}个商品查询成功但状态未显示！")
            
            # 显示警告给用户
            self.status_label.setText(f"查询完成：成功 {success_count}/{total_count}，状态显示 {updated_count}/{success_count} (可能有{missing_count}个状态丢失)")
            
            QMessageBox.warning(self, "状态显示警告", 
                               f"查询完成：成功 {success_count}/{total_count}\n"
                               f"状态显示：{updated_count}/{success_count}\n"
                               f"可能有 {missing_count} 个商品状态丢失！\n\n"
                               f"建议：检查控制台日志以获取详细信息")
        else:
            self.status_label.setText(f"查询完成：成功 {success_count}/{total_count}，状态已全部显示")
            QMessageBox.information(self, "查询完成", 
                                   f"上家状态查询完成\n"
                                   f"成功: {success_count}/{total_count}\n"
                                   f"状态已全部显示: {updated_count}个")

        # 清理线程
        if hasattr(self, 'supplier_status_thread'):
            self.supplier_status_thread.quit()
            self.supplier_status_thread.wait()
            self.supplier_status_thread = None

    def on_supplier_status_query_completed_with_auto_delist(self, success_count, total_count, auto_delist_count):
        """上家状态查询完成且有自动下架需求的回调"""
        print(f"查询完成，成功{success_count}/{total_count}，发现{auto_delist_count}个商品需要自动下架")
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 更新状态显示，但不显示完成对话框（等待自动下架处理完成）
        self.status_label.setText(f"查询完成，发现 {auto_delist_count} 个异常商品需要处理...")
        
        # 保存查询结果，等待自动下架完成后使用
        self.pending_query_result = {
            'success_count': success_count,
            'total_count': total_count,
            'auto_delist_count': auto_delist_count
        }
        
        # 清理查询线程
        if hasattr(self, 'supplier_status_thread'):
            self.supplier_status_thread.quit()
            self.supplier_status_thread.wait()
            self.supplier_status_thread = None

    def on_auto_delist_required(self, products_to_delist):
        """处理自动下架需求信号"""
        try:
            if not products_to_delist:
                return

            print(f"收到自动下架需求，共 {len(products_to_delist)} 个商品")
            
            # 构建详细的商品信息列表
            product_details = []
            for product in products_to_delist:
                detail = f"商品ID: {product['product_id']} (第{product['row']+1}行) - {product['status']} - {product['reason']}"
                product_details.append(detail)
            
            # 显示确认对话框
            detail_text = "\n".join(product_details[:10])  # 最多显示前10个
            if len(products_to_delist) > 10:
                detail_text += f"\n... 还有 {len(products_to_delist) - 10} 个商品"
            
            reply = QMessageBox.question(
                self, 
                "自动下架确认",
                f"发现 {len(products_to_delist)} 个商品的供应商状态异常，建议自动下架：\n\n"
                f"{detail_text}\n\n"
                f"是否确认批量下架这些商品？",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # 用户确认下架，执行自动下架操作
                self.perform_auto_delist(products_to_delist)
            else:
                # 用户取消自动下架，显示查询完成消息
                self.show_final_query_completion()
                
        except Exception as e:
            print(f"处理自动下架需求时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"处理自动下架需求失败: {str(e)}")
            # 即使出错也要显示最终完成
            self.show_final_query_completion()

    def show_final_query_completion(self):
        """显示最终的查询完成消息"""
        try:
            if hasattr(self, 'pending_query_result'):
                result = self.pending_query_result
                success_count = result['success_count']
                total_count = result['total_count']
                auto_delist_count = result['auto_delist_count']
                
                # 更新最终状态
                self.status_label.setText(f"查询完成：成功 {success_count}/{total_count} 个商品，处理了 {auto_delist_count} 个异常商品")
                
                # 显示完成对话框
                QMessageBox.information(
                    self, 
                    "查询完成", 
                    f"上家状态查询已全部完成\n"
                    f"查询成功: {success_count}/{total_count}\n"
                    f"处理异常商品: {auto_delist_count} 个"
                )
                
                # 清理临时数据
                delattr(self, 'pending_query_result')
            else:
                # 没有pending数据，显示默认完成消息
                QMessageBox.information(self, "查询完成", "上家状态查询已完成")
                
        except Exception as e:
            print(f"显示最终完成消息时出错: {str(e)}")

    def perform_auto_delist(self, products_to_delist):
        """执行自动下架操作"""
        try:
            print(f"开始执行自动下架操作，共 {len(products_to_delist)} 个商品")

            # 获取商品ID列、店铺名称列和累销列的索引
            product_id_col_index = -1
            shop_name_col_index = -1
            sales_col_index = -1

            for col in range(self.product_table.columnCount()):
                header_item = self.product_table.horizontalHeaderItem(col)
                if header_item:
                    if header_item.text() == "商品ID":
                        product_id_col_index = col
                    elif header_item.text() == "店铺":
                        shop_name_col_index = col
                    elif header_item.text() == "累销":
                        sales_col_index = col

            if product_id_col_index == -1 or shop_name_col_index == -1:
                QMessageBox.warning(self, "错误", "无法找到商品ID列或店铺列，无法执行自动下架")
                return

            if sales_col_index == -1:
                QMessageBox.warning(self, "错误", "无法找到累销列，无法执行自动下架")
                return

            # 转换为perform_delist_operation期望的格式，跳过累销大于0的商品
            product_operations = []
            skipped_products = []  # 记录跳过的商品

            for product in products_to_delist:
                row = product['row']

                # 从表格获取商品ID、店铺名称和累销信息
                product_id_item = self.product_table.item(row, product_id_col_index)
                shop_name_item = self.product_table.item(row, shop_name_col_index)
                sales_item = self.product_table.item(row, sales_col_index)

                if product_id_item and product_id_item.text() and shop_name_item and shop_name_item.text():
                    product_id = product_id_item.text()
                    shop_name_full = shop_name_item.text()

                    # 检查累销是否大于0
                    should_skip = False
                    if sales_item:
                        sales_text = sales_item.text()
                        try:
                            # 提取销量数字
                            import re
                            numbers = re.findall(r'\d+', sales_text)
                            if numbers:
                                sales_value = int(numbers[0])
                                if sales_value > 0:
                                    should_skip = True
                                    skipped_products.append({
                                        'product_id': product_id,
                                        'sales_value': sales_value,
                                        'row': row + 1,  # 显示行号从1开始
                                        'reason': product['reason']
                                    })
                                    print(f"跳过商品 {product_id} (第{row+1}行)，累销: {sales_value} > 0")
                        except Exception as e:
                            print(f"解析累销失败: {sales_text}, 错误: {str(e)}")
                            # 解析失败时不跳过，继续下架

                    if not should_skip:
                        # 处理店铺名称，去掉括号和分数（如 "佰俪品质女鞋(4.71)" -> "佰俪品质女鞋"）
                        clean_shop_name = shop_name_full
                        if '(' in shop_name_full and shop_name_full.endswith(')'):
                            clean_shop_name = shop_name_full.split('(')[0].strip()
                        elif '（' in shop_name_full and shop_name_full.endswith('）'):
                            clean_shop_name = shop_name_full.split('（')[0].strip()

                        product_operations.append({
                            'product_id': product_id,
                            'shop_name': clean_shop_name,
                            'row': row,
                            'auto_delist_reason': product['reason']  # 添加自动下架原因
                        })

            # 显示跳过商品的统计信息
            if skipped_products:
                skipped_count = len(skipped_products)
                print(f"跳过 {skipped_count} 个累销大于0的商品")

                # 构建跳过商品的详细信息
                skipped_details = []
                for skipped in skipped_products[:5]:  # 最多显示前5个
                    detail = f"商品ID: {skipped['product_id']} (第{skipped['row']}行) - 累销: {skipped['sales_value']}"
                    skipped_details.append(detail)

                skipped_text = "\n".join(skipped_details)
                if skipped_count > 5:
                    skipped_text += f"\n... 还有 {skipped_count - 5} 个商品"

                # 显示跳过信息
                QMessageBox.information(
                    self,
                    "跳过商品提示",
                    f"已跳过 {skipped_count} 个累销大于0的商品：\n\n"
                    f"{skipped_text}\n\n"
                    f"这些商品不会被自动下架。"
                )

            if not product_operations:
                if skipped_products:
                    QMessageBox.information(self, "提示", "所有商品都因累销大于0而被跳过，没有商品需要下架")
                else:
                    QMessageBox.warning(self, "错误", "没有找到有效的商品信息，无法执行自动下架")
                return

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, len(product_operations))  # 设置为下架商品数量范围
            self.progress_bar.setValue(0)
            
            # 更新状态显示
            self.status_label.setText(f"正在自动下架 {len(product_operations)} 个商品...")
            
            # 清空选中行的操作状态列，并标记为自动下架中
            for operation in product_operations:
                row = operation['row']
                self.update_operation_status(row, "自动下架中...")

            # 调用增强版的下架操作方法，带进度更新
            self.perform_delist_operation_with_progress(product_operations)

            # 隐藏进度条
            self.progress_bar.setVisible(False)

            # 额外的状态修复检查：确保没有商品仍显示"下架中"状态
            print("执行自动下架后的状态修复检查...")
            for operation in product_operations:
                row = operation.get('row', -1)
                if row >= 0:
                    current_status_item = self.product_table.item(row, 8)  # 操作状态列
                    if current_status_item:
                        current_status = current_status_item.text()
                        # 如果状态仍然包含"中"字（如"下架中"、"自动下架中"等），说明可能没有正确更新
                        if "中" in current_status and ("下架" in current_status or "处理" in current_status):
                            product_id = operation.get('product_id', '未知')
                            print(f"自动下架后修复商品 {product_id} (第{row+1}行) 的状态: {current_status} -> 状态异常")
                            self.update_operation_status(row, "状态异常")

            # 强制刷新界面
            QApplication.processEvents()
            
            # 显示自动下架完成提示
            total_original = len(products_to_delist)
            processed_count = len(product_operations)
            skipped_count = len(skipped_products)

            self.status_label.setText(f"自动下架操作完成，共处理 {processed_count} 个商品，跳过 {skipped_count} 个商品")

            # 弹出结果统计对话框
            result_message = f"自动下架操作已完成\n"
            result_message += f"原始商品数量: {total_original}\n"
            result_message += f"实际下架数量: {processed_count}\n"
            if skipped_count > 0:
                result_message += f"跳过商品数量: {skipped_count} (累销>0)\n"
            result_message += f"详细结果请查看表格中的操作状态列"

            QMessageBox.information(
                self,
                "自动下架完成",
                result_message
            )
            
            # 显示最终查询完成消息（包含自动下架结果）
            self.show_final_query_completion()
            
        except Exception as e:
            print(f"执行自动下架操作时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            
            # 隐藏进度条
            self.progress_bar.setVisible(False)
            
            QMessageBox.critical(self, "错误", f"自动下架操作失败: {str(e)}")
            # 即使自动下架失败也要显示最终完成
            self.show_final_query_completion()

    def extract_numbers_from_text(self, text):
        """提取文本中的纯数字"""
        try:
            import re
            # 使用正则表达式提取所有数字
            numbers = re.findall(r'\d+', str(text))
            if numbers:
                # 将所有数字连接成一个完整的数字字符串
                result = ''.join(numbers)
                return result
            else:
                return text  # 如果没有数字，返回原始文本
        except Exception as e:
            print(f"提取数字时出错: {str(e)}")
            return text  # 出错时返回原始文本

    def filter_products_with_supplier(self, products, force_reload_cache=False):
        """筛选有上家信息的商品"""
        products_with_supplier = []

        # 如果需要强制重新加载缓存
        if force_reload_cache:
            self.reload_supplier_cache()

        missing_count = 0
        for i, product in enumerate(products):
            # 检查商品是否有上家信息
            product_id = product.get('itemId', '') if isinstance(product, dict) else ''
            supplier_id = self.extract_supplier_id_from_product(product)
            if supplier_id:
                products_with_supplier.append(product)
            else:
                missing_count += 1
                if missing_count <= 5:  # 只显示前5个缺失的商品信息
                    print(f"⚠️ 商品 {product_id} 缺失上家信息 - 标题: {product.get('title', '无标题')[:30]}...")
            # 不再跳过无上家信息的商品，而是在上层逻辑中处理查询

        print(f"筛选结果: {len(products_with_supplier)}/{len(products)} 个商品有上家信息")
        if missing_count > 5:
            print(f"⚠️ 还有 {missing_count - 5} 个商品缺失上家信息（未显示）")
        return products_with_supplier

    def reload_supplier_cache(self):
        """重新加载上家缓存（从SQLite数据库）"""
        try:
            import sqlite3

            self.supplier_cache = {}

            # 从所有店铺的SQLite数据库加载上家信息
            supplier_dir = get_config_path("上家信息")
            if os.path.exists(supplier_dir):
                for file_name in os.listdir(supplier_dir):
                    if file_name.endswith('.db'):
                        shop_name = file_name[:-3]  # 去掉.db后缀
                        db_file_path = os.path.join(supplier_dir, file_name)

                        try:
                            conn = sqlite3.connect(db_file_path)
                            cursor = conn.cursor()
                            cursor.execute('SELECT product_id, supplier_id FROM supplier_info')
                            rows = cursor.fetchall()

                            for product_id, supplier_id in rows:
                                self.supplier_cache[product_id] = supplier_id

                            conn.close()
                            print(f"从SQLite数据库加载店铺 {shop_name} 的上家信息: {len(rows)} 条记录")

                        except Exception as e:
                            print(f"加载店铺 {shop_name} SQLite数据库失败: {str(e)}")

                print(f"重新加载上家缓存完成，共 {len(self.supplier_cache)} 条记录")
            else:
                print("上家信息目录不存在")

        except Exception as e:
            print(f"重新加载上家缓存失败: {str(e)}")
            self.supplier_cache = {}





    def query_shop_supplier_info(self, shop_name, config_data, start_row, product_count):
        """查询单个店铺的上家信息（并发50线程）"""
        try:
            from concurrent.futures import ThreadPoolExecutor, as_completed
            import threading
            import re

            # 清理店铺名称，去掉商品数量信息 [数字/数字]
            clean_shop_name = re.sub(r'\[\d+/\d+\]', '', shop_name).strip()
            print(f"查询上家信息 - 清理后的店铺名称: '{clean_shop_name}'")

            # 查找店铺配置
            account_info = self.find_shop_info(config_data, clean_shop_name)
            if not account_info:
                return 0

            access_token = account_info.get('accesstoken')
            if not access_token:
                return 0

            # 收集需要查询的商品（上家列为空的）
            products_to_query = []
            for i in range(product_count):
                row = start_row + i

                # 检查上家列是否为空
                supplier_item = self.product_table.item(row, 14)
                if supplier_item and supplier_item.text().strip():
                    continue  # 已有上家信息，跳过

                # 获取商品ID
                product_id_item = self.product_table.item(row, 2)
                if product_id_item:
                    product_id = product_id_item.text().strip()
                    products_to_query.append({
                        'row': row,
                        'product_id': product_id
                    })

            if not products_to_query:
                return 0

            # 并发查询上家信息
            success_count = 0
            processed_count = 0
            count_lock = threading.Lock()

            def query_single_product(product):
                """查询单个商品的上家信息"""
                try:
                    row = product['row']
                    product_id = product['product_id']

                    # 创建API实例
                    api = KuaishouAPI(access_token=access_token)

                    # 调用商品详情接口
                    result = api.get_item_detail(int(product_id))

                    if result.get('success'):
                        data = result.get('data', {})
                        sku_infos = data.get('skuInfos', [])

                        if sku_infos:
                            sku_nick = sku_infos[0].get('skuNick', '')
                            if sku_nick:
                                # 处理上家信息：提取纯数字
                                processed_sku_nick = self.extract_numbers_only(sku_nick)

                                # 线程安全地更新数据
                                with count_lock:
                                    self.supplier_data[product_id] = processed_sku_nick

                                return (row, processed_sku_nick, True)
                            else:
                                return (row, '未找到上家信息', False)
                        else:
                            return (row, '无SKU信息', False)
                    else:
                        error_msg = result.get('message', '未知错误')
                        return (row, f'查询失败: {error_msg}', False)

                except Exception as e:
                    print(f"查询商品 {product_id} 详情失败: {str(e)}")
                    return (row, f'查询异常', False)

            # 使用线程池并发查询
            with ThreadPoolExecutor(max_workers=50) as executor:
                future_to_product = {executor.submit(query_single_product, product): product for product in products_to_query}

                for future in as_completed(future_to_product):
                    if self.should_stop:
                        break

                    try:
                        row, result_text, success = future.result()

                        # 发送行更新信号
                        self.row_updated.emit(row, result_text)

                        # 更新计数
                        with count_lock:
                            processed_count += 1
                            if success:
                                success_count += 1

                        # 更新进度
                        progress_text = f"店铺 {shop_name} 上家查询: {processed_count}/{len(products_to_query)}"
                        self.progress_updated.emit(progress_text)

                    except Exception as e:
                        print(f"处理查询结果时出错: {str(e)}")
                        with count_lock:
                            processed_count += 1

            # 保存查询到的上家信息到SQLite数据库（按店铺保存）
            self.save_supplier_data_to_db(shop_name)

            return success_count

        except Exception as e:
            print(f"查询店铺 {shop_name} 上家信息失败: {str(e)}")
            return 0

    def extract_numbers_only(self, text):
        """提取字符串中的纯数字"""
        try:
            import re
            numbers = re.findall(r'\d+', str(text))
            if numbers:
                result = ''.join(numbers)
                print(f"上家信息处理: '{text}' -> '{result}'")
                return result
            else:
                print(f"上家信息中未找到数字: '{text}'")
                return text
        except Exception as e:
            print(f"处理上家信息时出错: {str(e)}")
            return text

    def query_shop_supplier_info_for_status_check(self, shop_name, missing_products):
        """为上家状态查询专门的上家信息查询方法 - 使用线程查询"""
        try:
            print(f"🔍 [DEBUG] 开始为上家状态查询，查询店铺 {shop_name} 的 {len(missing_products)} 个商品的上家信息...")

            # 创建并启动查询线程
            self.missing_supplier_thread = MissingSupplierQueryThread(shop_name, missing_products, self)

            # 连接信号
            self.missing_supplier_thread.query_progress.connect(lambda msg: print(f"🔍 [DEBUG] 查询进度: {msg}"))
            self.missing_supplier_thread.query_completed.connect(lambda success, total: print(f"🔍 [DEBUG] 查询完成: 成功 {success}/{total}"))
            self.missing_supplier_thread.query_failed.connect(lambda error: print(f"🔍 [DEBUG] 查询失败: {error}"))
            self.missing_supplier_thread.row_updated.connect(self.update_supplier_row)  # 连接行更新信号

            # 启动线程并等待完成
            self.missing_supplier_thread.start()
            self.missing_supplier_thread.wait()  # 等待线程完成

            # 返回成功查询的数量（这里简化处理，实际应该从线程获取结果）
            return len(missing_products)

        except Exception as e:
            error_msg = f"为上家状态查询，查询上家信息异常: {str(e)}"
            print(error_msg)
            return 0

    def extract_supplier_id_from_product(self, product, shop_name=None):
        """从商品数据中提取上家ID - 优先从上家缓存获取"""
        try:
            # 获取商品ID
            product_id = product.get('itemId', '') if isinstance(product, dict) else ''
            if not product_id:
                return None

            # 如果没有传入店铺名，尝试从商品数据中获取
            if not shop_name and isinstance(product, dict):
                shop_name = product.get('shop_name') or product.get('_shop_name')
                if shop_name:
                    # 清理店铺名称
                    if '(' in shop_name and shop_name.endswith(')'):
                        shop_name = shop_name.split('(')[0].strip()
                    elif '（' in shop_name and shop_name.endswith('）'):
                        shop_name = shop_name.split('（')[0].strip()

            # 方法1：从上家缓存中获取（优先，按店铺读取）
            supplier_id = self.get_supplier_id_from_cache(str(product_id), shop_name)
            if supplier_id:
                return supplier_id

            # 方法2：从商品数据结构中获取（备用）
            if isinstance(product, dict):
                # 检查常见的上家ID字段
                supplier_id = product.get('supplierId') or product.get('supplier_id')

                # 如果没有直接的上家ID，尝试从其他字段获取
                if not supplier_id:
                    # 从商品来源信息获取
                    source_info = product.get('sourceInfo', {})
                    if isinstance(source_info, dict):
                        supplier_id = source_info.get('supplierId') or source_info.get('supplier_id')

                # 从商品扩展信息获取
                if not supplier_id:
                    ext_info = product.get('extInfo', {})
                    if isinstance(ext_info, dict):
                        supplier_id = ext_info.get('supplierId') or ext_info.get('supplier_id')

            # 清理和验证上家ID
            if supplier_id:
                supplier_id = str(supplier_id).strip()
                # 只要不为空就是有效的上家ID
                if supplier_id:
                    return supplier_id

            return None

        except Exception as e:
            print(f"提取上家ID异常: {str(e)}")
            return None

    def get_supplier_id_from_cache(self, product_id, shop_name=None):
        """从店铺上家缓存中获取商品的上家ID（支持SQLite和JSON）"""
        try:
            import sqlite3
            import json

            # 如果没有传入店铺名，尝试从内存缓存获取
            if not shop_name and hasattr(self, 'supplier_cache') and self.supplier_cache:
                supplier_id = self.supplier_cache.get(str(product_id))
                if supplier_id:
                    supplier_id = str(supplier_id).strip()
                    if supplier_id:
                        return supplier_id

            # 从店铺文件读取
            if shop_name:
                supplier_dir = get_config_path("上家信息")

                # 优先尝试SQLite数据库
                db_file_path = os.path.join(supplier_dir, f"{shop_name}.db")
                if os.path.exists(db_file_path):
                    try:
                        conn = sqlite3.connect(db_file_path)
                        cursor = conn.cursor()
                        cursor.execute('SELECT supplier_id FROM supplier_info WHERE product_id = ?', (str(product_id),))
                        result = cursor.fetchone()
                        conn.close()

                        if result:
                            supplier_id = str(result[0]).strip()
                            if supplier_id:
                                return supplier_id
                    except Exception as e:
                        print(f"读取店铺 {shop_name} SQLite缓存失败: {str(e)}")

                # 兼容旧的JSON文件
                json_file_path = os.path.join(supplier_dir, f"{shop_name}.json")
                if os.path.exists(json_file_path):
                    try:
                        with open(json_file_path, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            if content:
                                supplier_data = json.loads(content)
                                supplier_id = supplier_data.get(str(product_id))
                                if supplier_id:
                                    supplier_id = str(supplier_id).strip()
                                    if supplier_id:
                                        return supplier_id
                    except Exception as e:
                        print(f"读取店铺 {shop_name} JSON缓存失败: {str(e)}")

            return None

        except Exception as e:
            print(f"从缓存获取上家ID异常: {str(e)}")
            return None

    def get_shop_names_from_table(self):
        """从表格中获取所有店铺名称"""
        try:
            shop_names = set()
            row_count = self.product_table.rowCount()

            # 找到店铺列的索引
            shop_column = -1
            for col in range(self.product_table.columnCount()):
                header_item = self.product_table.horizontalHeaderItem(col)
                if header_item and header_item.text() == "店铺":
                    shop_column = col
                    break

            if shop_column == -1:
                print("未找到店铺列")
                return set()

            # 遍历表格获取店铺名称
            for row in range(row_count):
                shop_item = self.product_table.item(row, shop_column)
                if shop_item and shop_item.text().strip():
                    shop_name = shop_item.text().strip()
                    # 清理店铺名称，去掉评分等信息
                    if '(' in shop_name and shop_name.endswith(')'):
                        shop_name = shop_name.split('(')[0].strip()
                    elif '（' in shop_name and shop_name.endswith('）'):
                        shop_name = shop_name.split('（')[0].strip()

                    if shop_name:
                        shop_names.add(shop_name)

            print(f"从表格中获取到 {len(shop_names)} 个店铺: {list(shop_names)}")
            return shop_names

        except Exception as e:
            print(f"获取表格店铺名称失败: {str(e)}")
            return set()

    def save_supplier_data_to_db(self, shop_name):
        """按店铺保存上家信息到SQLite数据库（高性能解决方案）"""
        try:
            import sqlite3

            # 检查是否有上家数据和店铺名称
            if not hasattr(self, 'supplier_data') or not self.supplier_data or not shop_name:
                print(f"警告：无法保存店铺 {shop_name} 的上家信息，supplier_data为空或店铺名称缺失")
                return

            # 创建上家信息目录
            supplier_dir = get_config_path("上家信息")
            if not os.path.exists(supplier_dir):
                os.makedirs(supplier_dir)

            # 使用SQLite数据库文件
            db_file_path = os.path.join(supplier_dir, f"{shop_name}.db")

            # 连接数据库
            conn = sqlite3.connect(db_file_path)
            cursor = conn.cursor()

            # 创建表（如果不存在）- 添加supplier_name和category_name字段
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS supplier_info (
                    product_id TEXT PRIMARY KEY,
                    supplier_id TEXT NOT NULL,
                    supplier_name TEXT DEFAULT '',
                    category_name TEXT DEFAULT '',
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 检查并添加缺失的列（兼容现有数据库）
            try:
                cursor.execute("PRAGMA table_info(supplier_info)")
                columns = [column[1] for column in cursor.fetchall()]

                if 'supplier_name' not in columns:
                    cursor.execute('ALTER TABLE supplier_info ADD COLUMN supplier_name TEXT DEFAULT ""')
                    print("✅ 数据库已升级：添加supplier_name字段")

                if 'category_name' not in columns:
                    cursor.execute('ALTER TABLE supplier_info ADD COLUMN category_name TEXT DEFAULT ""')
                    print("✅ 数据库已升级：添加category_name字段")

            except Exception as alter_error:
                print(f"⚠️ 数据库升级警告: {str(alter_error)}")  # 不影响主要功能

            # 创建索引（提高查询性能）
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_product_id ON supplier_info(product_id)
            ''')

            # 批量插入或更新数据（使用UPSERT）
            new_count = 0
            update_count = 0

            for product_id, supplier_data in self.supplier_data.items():
                # 兼容旧格式（只有supplier_id）和新格式（包含supplier_name和category_name）
                if isinstance(supplier_data, dict):
                    supplier_id = supplier_data.get('supplier_id', '')
                    supplier_name = supplier_data.get('supplier_name', '')
                    category_name = supplier_data.get('category_name', '')
                else:
                    # 旧格式，只有supplier_id
                    supplier_id = str(supplier_data)
                    supplier_name = ''
                    category_name = ''

                # 检查是否已存在
                cursor.execute('SELECT supplier_id, supplier_name, category_name FROM supplier_info WHERE product_id = ?', (str(product_id),))
                existing = cursor.fetchone()

                if existing:
                    # 如果数据不同则更新（检查supplier_id、supplier_name或category_name是否有变化）
                    existing_supplier_id = existing[0] if existing[0] else ''
                    existing_supplier_name = existing[1] if len(existing) > 1 and existing[1] else ''
                    existing_category_name = existing[2] if len(existing) > 2 and existing[2] else ''

                    if (existing_supplier_id != str(supplier_id) or
                        existing_supplier_name != str(supplier_name) or
                        existing_category_name != str(category_name)):
                        cursor.execute('''
                            UPDATE supplier_info
                            SET supplier_id = ?, supplier_name = ?, category_name = ?, updated_time = CURRENT_TIMESTAMP
                            WHERE product_id = ?
                        ''', (str(supplier_id), str(supplier_name), str(category_name), str(product_id)))
                        update_count += 1
                else:
                    # 插入新记录，包含上家名称和类目信息
                    cursor.execute('''
                        INSERT INTO supplier_info (product_id, supplier_id, supplier_name, category_name)
                        VALUES (?, ?, ?, ?)
                    ''', (str(product_id), str(supplier_id), str(supplier_name), str(category_name)))
                    new_count += 1

            # 提交事务
            conn.commit()
            conn.close()

            print(f"店铺 {shop_name} 上家信息已保存到SQLite数据库: 新增 {new_count} 条，更新 {update_count} 条")

        except Exception as e:
            print(f"保存店铺 {shop_name} 上家信息到SQLite数据库失败: {str(e)}")
            import traceback
            traceback.print_exc()








def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理函数"""
    if issubclass(exc_type, KeyboardInterrupt):
        # 允许键盘中断
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    import traceback
    error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    print(f"未捕获的异常: {error_msg}")
    
    # 尝试记录到日志文件
    try:
        with open("error.log", "a", encoding="utf-8") as f:
            f.write(f"\n[{datetime.now()}] 未捕获的异常:\n{error_msg}\n")
    except:
        pass

if __name__ == '__main__':
    # 设置全局异常处理
    sys.excepthook = handle_exception
    
    try:
        app = QApplication(sys.argv)
        window = ProductManager()
        window.show()
        sys.exit(app.exec_())
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        try:
            with open("error.log", "a", encoding="utf-8") as f:
                f.write(f"\n[{datetime.now()}] 程序启动失败: {str(e)}\n")
        except:
            pass