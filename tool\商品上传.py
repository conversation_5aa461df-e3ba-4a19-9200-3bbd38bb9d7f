#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
微信小店商品上传工具

此模块提供完整的微信小店商品上传功能，包括：
1. 基于JSON模板的数据转换
2. 图片批量上传处理
3. 商品数据验证和清洗
4. 错误处理和重试机制
5. 与商品复制界面的集成

作者: AI工程师
创建日期: 2025-06-30
"""

import sys
import os
import json
from datetime import datetime
import re
import requests
import time
import tempfile
import hashlib
import io
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import difflib
from PIL import Image, ImageOps

# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tool.快手api import KuaishouAPI

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class KuaishouStoreUploadTemplate:
    """快手小店上传模板管理器"""

    def __init__(self):
        """初始化模板配置"""
        self.template_config = self._get_default_template()

    def _get_default_template(self) -> Dict:
        """获取默认模板配置"""
        return {
            "template_info": {
                "name": "快手小店商品上传模板",
                "version": "1.0",
                "description": "用于将1688商品数据转换为快手小店格式的配置模板"
            },

            "default_config": {
                "express_template_id": 0,
                "category_id": 1003,
                "stock_partner": False,
                "purchase_limit": False,
                "sale_time_flag": False,
                "pay_way": 2,
                "multiple_stock": False,
                "immediately_on_offline_flag": 0
            },
            
            "field_mapping": {
                "basic_info": {
                    "title": {
                        "source_field": "title",
                        "max_length": 60,
                        "required": True,
                        "filters": ["remove_emoji", "trim_spaces"],
                        "default": ""
                    },
                    "relItemId": {
                        "source_field": "product_id",
                        "required": True,
                        "default_generator": "generate_rel_item_id"
                    },
                    "categoryId": {
                        "source_field": "category_id",
                        "required": True,
                        "data_type": "integer",
                        "default": 1003
                    },
                    "details": {
                        "source_field": "description",
                        "max_length": 2000,
                        "required": True,
                        "default": "商品详情"
                    }
                },

                "image_info": {
                    "imageUrls": {
                        "source_field": "main_images",
                        "required": True,
                        "min_count": 1,
                        "max_count": 9,
                        "process_type": "upload_to_kuaishou",
                        "upload_type": 1
                    },
                    "detailImageUrls": {
                        "source_field": "detail_images",
                        "required": True,
                        "min_count": 1,
                        "max_count": 50,
                        "process_type": "upload_to_kuaishou",
                        "upload_type": 2
                    }
                },
                
                "sku_info": {
                    "skuList": {
                        "source_field": "skus",
                        "required": True,
                        "min_count": 1,
                        "max_count": 600,
                        "fields": {
                            "relSkuId": {
                                "source_field": "sku_id",
                                "required": True,
                                "data_type": "string"
                            },
                            "skuStock": {
                                "source_field": "stock",
                                "required": True,
                                "data_type": "integer",
                                "min_value": 0,
                                "max_value": 9999999,
                                "default": 999
                            },
                            "skuSalePrice": {
                                "source_field": "price",
                                "required": True,
                                "data_type": "price_in_cents",
                                "validation": "must_be_positive"
                            },
                            "skuNick": {
                                "source_field": "sku_code",
                                "required": False,
                                "default": ""
                            },
                            "skuProps": {
                                "source_field": "attributes",
                                "required": False,
                                "process_type": "convert_to_kuaishou_props"
                            }
                        }
                    }
                },

                "service_info": {
                    "serviceRule": {
                        "refundRule": {
                            "default": "1"
                        },
                        "promiseDeliveryTime": {
                            "default": 172800
                        },
                        "servicePromise": {
                            "freshRotRefund": {
                                "default": False
                            },
                            "brokenRefund": {
                                "default": False
                            },
                            "allergyRefund": {
                                "default": False
                            }
                        }
                    },
                    "expressTemplateId": {
                        "source_field": "express_template_id",
                        "required": True,
                        "data_type": "integer",
                        "default": 0
                    }
                }
            },
            
            "data_processors": {
                "remove_emoji": {
                    "description": "移除emoji表情符号",
                    "regex": "[\\u1F600-\\u1F64F\\u1F300-\\u1F5FF\\u1F680-\\u1F6FF\\u1F1E0-\\u1F1FF]"
                },
                "trim_spaces": {
                    "description": "去除首尾空格并压缩多余空格"
                },
                "price_in_cents": {
                    "description": "将价格转换为分（乘以100）",
                    "formula": "int(float(value) * 100)"
                },
                "upload_to_kuaishou": {
                    "description": "通过快手图片上传接口处理图片",
                    "api_method": "upload_item_image",
                    "params": {
                        "upload_type": "dynamic"
                    }
                },
                "convert_to_kuaishou_props": {
                    "description": "转换属性为快手SKU属性格式",
                    "format": {
                        "propName": "string",
                        "propValueName": "string",
                        "isMainProp": "integer",
                        "propVersion": 1
                    }
                },
                "generate_rel_item_id": {
                    "description": "生成快手外部商品ID",
                    "format": "KS_{timestamp}_{random}"
                }
            },
            
            "category_templates": {
                "女装": {
                    "category_id": 1001,
                    "default_freight_template": 1
                },
                "女鞋": {
                    "category_id": 1002,
                    "default_freight_template": 1
                },
                "百货": {
                    "category_id": 1003,
                    "default_freight_template": 1
                }
            }
        }


# 添加属性查询和智能匹配的混入类
class AttributeQueryMixin:
    """属性查询和智能匹配的混入类"""

    def _query_prop_values(self, category_id: int, prop_id: int, prop_name: str) -> list:
        """查询属性的可用值"""
        try:



            print(f"[快手小店上传] 🔍 查询属性 '{prop_name}' (ID: {prop_id}) 的可用值...")

            # 调用我们自己的search_category_prop_values方法（包含正确的API参数和返回格式处理）
            response = self.search_category_prop_values(
                category_id=category_id,
                prop_id=prop_id,
                cursor=0,  # 必需参数：游标，从0开始
                limit=500  # 最大500，获取更多属性值
            )

            if response and response.get('success'):
                prop_values = response.get('propValues', [])
                print(f"[快手小店上传] ✅ 找到 {len(prop_values)} 个可用值 (总计: {response.get('total', 0)})")

                # 详细打印API返回的原始数据
                print(f"[快手小店上传] 🔍 API返回的完整响应:")
                print(f"  response.keys(): {list(response.keys())}")
                print(f"  success: {response.get('success')}")
                print(f"  total: {response.get('total')}")
                print(f"  propValues类型: {type(prop_values)}")
                print(f"  propValues长度: {len(prop_values)}")

                # 详细打印每个属性值的结构
                print(f"[快手小店上传] 📋 {prop_name} 的所有可用值详细信息:")
                for i, value in enumerate(prop_values):
                    print(f"  {i+1}. 完整数据: {value}")
                    print(f"      propValue: {value.get('propValue')}")
                    print(f"      propValueId: {value.get('propValueId')}")
                    print(f"      其他字段: {[k for k in value.keys() if k not in ['propValue', 'propValueId']]}")



                return prop_values
            else:
                print(f"[快手小店上传] ❌ 查询属性值失败: {response}")
                return []

        except Exception as e:
            print(f"[快手小店上传] 查询属性值异常: {e}")
            return []

    def _smart_match_prop_value(self, prop_name: str, available_values: list, product_data: dict) -> dict:
        """智能匹配属性值"""
        try:
            # 🚀 新增：优先检查强制属性配置
            force_value = self._check_force_attribute(prop_name)
            if force_value:
                print(f"[快手小店上传] 🔒 使用强制属性配置: {prop_name} = {force_value.get('propValue')}")
                return force_value

            if not available_values:
                return None

            # 获取商品标题用于智能匹配
            title = product_data.get('title', '').lower()

            # 定义智能匹配规则
            smart_rules = {
                '领型': {
                    '方领': ['方领', '一字领'],
                    'V领': ['v领', 'v型领'],
                    '圆领': ['圆领', '圆形领'],
                    '高领': ['高领', '立领'],
                    '连帽': ['连帽', '帽子']
                },
                '袖长': {
                    '短袖': ['短袖', '半袖'],
                    '长袖': ['长袖', '全袖'],
                    '无袖': ['无袖', '背心'],
                    '七分袖': ['七分袖', '3/4袖']
                },
                '面料材质': {
                    '棉': ['纯棉', '棉', '100%棉'],
                    '聚酯纤维': ['涤纶', '聚酯'],
                    '棉混纺': ['棉涤', '棉聚酯']
                },
                '服装版型': {
                    '修身': ['显瘦', '修身', '紧身'],
                    '宽松': ['宽松', 'oversize'],
                    '标准': ['标准', '正常']
                }
            }

            # 获取该属性的匹配规则
            rules = smart_rules.get(prop_name, {})

            # 基于标题关键词匹配
            for prop_value in available_values:
                value_name = prop_value.get('propValue', '').lower()

                # 检查是否有匹配的规则
                for target_value, keywords in rules.items():
                    if target_value.lower() == value_name:
                        # 检查标题是否包含相关关键词
                        for keyword in keywords:
                            if keyword.lower() in title:
                                print(f"[快手小店上传] 🎯 基于标题关键词 '{keyword}' 匹配到: {prop_name} = {target_value}")
                                return prop_value

                # 直接匹配属性值名称
                if value_name in title:
                    print(f"[快手小店上传] 🎯 基于标题直接匹配到: {prop_name} = {value_name}")
                    return prop_value

            # 如果没有智能匹配到，使用第一个可用值
            if available_values:
                first_value = available_values[0]
                print(f"[快手小店上传] 🔄 使用第一个可用值: {prop_name} = {first_value.get('propValue')}")
                return first_value

            return None

        except Exception as e:
            print(f"[快手小店上传] 智能匹配属性值异常: {e}")
            return None

    def _check_force_attribute(self, prop_name: str) -> dict:
        """检查是否有强制属性配置"""
        try:
            # 加载智能匹配配置
            attr_config = self._load_attribute_mapping_config()
            if not attr_config:
                return None

            # 获取全局属性映射
            global_mapping = attr_config.get('global_attribute_mapping', {})
            prop_config = global_mapping.get(prop_name, {})

            # 检查是否有强制属性配置
            if 'force_attribute' in prop_config:
                force_config = prop_config['force_attribute']
                if force_config.get('enabled', False):
                    print(f"[快手小店上传] 🔒 发现强制属性配置: {prop_name}")
                    return {
                        'propValueId': force_config.get('propValueId'),
                        'propValue': force_config.get('propValue'),
                        'brandOwner': force_config.get('brandOwner', ''),
                        'hasBrandCertificate': force_config.get('hasBrandCertificate', True)
                    }

            return None

        except Exception as e:
            print(f"[快手小店上传] 检查强制属性配置异常: {e}")
            return None



    def _smart_match_from_config(self, prop_name: str, title: str, available_values: list) -> dict:
        """从智能匹配配置文件中匹配属性值"""
        try:
            # 加载智能匹配配置
            attr_config = self._load_attribute_mapping_config()
            if not attr_config:
                print(f"[快手小店上传] 智能匹配配置文件为空或不存在")
                return None

            # 获取全局属性映射
            global_mapping = attr_config.get('global_attribute_mapping', {})
            prop_config = global_mapping.get(prop_name, {})

            if not prop_config:
                print(f"[快手小店上传] 属性 '{prop_name}' 在智能匹配配置中不存在")
                return None

            print(f"[快手小店上传] 🎯 使用智能匹配配置匹配属性: {prop_name}")

            # 🚀 新增：优先检查强制属性配置
            if 'force_attribute' in prop_config:
                force_config = prop_config['force_attribute']
                if force_config.get('enabled', False):
                    print(f"[快手小店上传] 🔒 使用强制属性配置: {prop_name}")
                    return {
                        'propValueId': force_config.get('propValueId'),
                        'propValue': force_config.get('propValue'),
                        'brandOwner': force_config.get('brandOwner', ''),
                        'hasBrandCertificate': force_config.get('hasBrandCertificate', True)
                    }

            # 检查是否有固定值
            if 'fixed_value' in prop_config:
                fixed_value = prop_config['fixed_value']
                # 在可用值中查找匹配的固定值
                for value in available_values:
                    if value.get('propValue', '').lower() == fixed_value.lower():
                        print(f"[快手小店上传] ✅ 使用固定值: {prop_name} = {fixed_value}")
                        return value
                print(f"[快手小店上传] ❌ 固定值 '{fixed_value}' 在可用值中不存在")

            # 优先级映射匹配
            priority_mapping = prop_config.get('priority_mapping', {})
            if priority_mapping:
                print(f"[快手小店上传] 🔍 在 {len(priority_mapping)} 个优先级映射中搜索...")

                for target_value, keywords in priority_mapping.items():
                    # 在可用值中查找目标值
                    matched_value = None
                    for value in available_values:
                        if value.get('propValue', '').lower() == target_value.lower():
                            matched_value = value
                            break

                    if matched_value:
                        # 检查关键词是否在标题中
                        for keyword in keywords:
                            if keyword == '智能匹配':
                                # 基于标题的智能匹配
                                if target_value.lower() in title.lower():
                                    print(f"[快手小店上传] ✅ 智能匹配成功: {prop_name} = {target_value} (标题包含目标值)")
                                    return matched_value
                            elif keyword.lower() in title.lower():
                                print(f"[快手小店上传] ✅ 关键词匹配成功: {prop_name} = {target_value} (关键词: {keyword})")
                                return matched_value

            # 使用fallback值
            fallback = prop_config.get('fallback')
            if fallback:
                for value in available_values:
                    if value.get('propValue', '').lower() == fallback.lower():
                        print(f"[快手小店上传] ✅ 使用fallback值: {prop_name} = {fallback}")
                        return value
                print(f"[快手小店上传] ❌ fallback值 '{fallback}' 在可用值中不存在")

            print(f"[快手小店上传] ❌ 智能匹配配置无法匹配属性: {prop_name}")
            return None

        except Exception as e:
            print(f"[快手小店上传] 智能匹配配置异常: {e}")
            return None

    def _get_main_category_name_from_table(self, row: int) -> str:
        """从表格行获取主类目名称，用于缓存分组"""
        try:
            print(f"[快手小店上传] 🔍 调试 - 获取第 {row+1} 行的主类目名称")

            # 优先从类目全称列（第9列）获取主类目名称
            category_names_item = self.product_table.item(row, 9)  # 类目全称列
            print(f"[快手小店上传] 🔍 调试 - 第9列(类目全称)项目: {category_names_item}")

            if category_names_item:
                category_names_str = category_names_item.text().strip()
                print(f"[快手小店上传] 🔍 调试 - 第9列内容: '{category_names_str}'")

                if category_names_str:
                    # 解析类目名称：女装,抹胸上衣 -> 女装
                    category_names = [name.strip() for name in category_names_str.split(',') if name.strip()]
                    print(f"[快手小店上传] 🔍 调试 - 解析后的类目名称列表: {category_names}")

                    if category_names:
                        main_category_name = category_names[0]  # 第一个是主类目
                        print(f"[快手小店上传] ✅ 从类目全称获取主类目名称: {main_category_name}")
                        return main_category_name
                else:
                    print(f"[快手小店上传] ⚠️ 第9列内容为空")
            else:
                print(f"[快手小店上传] ⚠️ 第9列项目为None")

            # 备选方案：从类目列（第3列）获取主类目名称
            category_item = self.product_table.item(row, 3)  # 类目列
            print(f"[快手小店上传] 🔍 调试 - 第3列(类目)项目: {category_item}")

            if category_item:
                category_str = category_item.text().strip()
                print(f"[快手小店上传] 🔍 调试 - 第3列内容: '{category_str}'")

                if category_str:
                    # 解析类目名称：女装,抹胸上衣 -> 女装
                    category_names = [name.strip() for name in category_str.split(',') if name.strip()]
                    print(f"[快手小店上传] 🔍 调试 - 解析后的类目名称列表: {category_names}")

                    if category_names:
                        main_category_name = category_names[0]  # 第一个是主类目
                        print(f"[快手小店上传] ✅ 从类目列获取主类目名称: {main_category_name}")
                        return main_category_name

            # 最后尝试从类目ID列（第4列）推断
            category_id_item = self.product_table.item(row, 4)  # 类目ID列
            print(f"[快手小店上传] 🔍 调试 - 第4列(类目ID)项目: {category_id_item}")

            if category_id_item:
                category_id_str = category_id_item.text().strip()
                print(f"[快手小店上传] 🔍 调试 - 第4列内容: '{category_id_str}'")

                if category_id_str:
                    # 如果类目ID列包含ID格式（如"1713,10377,10378"），解析主类目ID
                    if ',' in category_id_str and category_id_str.replace(',', '').replace(' ', '').isdigit():
                        category_ids = [id.strip() for id in category_id_str.split(',') if id.strip()]
                        print(f"[快手小店上传] 🔍 调试 - 解析后的类目ID列表: {category_ids}")

                        if category_ids:
                            main_category_id = category_ids[0]  # 第一个是主类目ID
                            print(f"[快手小店上传] ⚠️ 使用主类目ID作为名称: 类目_{main_category_id}")
                            return f"类目_{main_category_id}"

            # 默认值
            print(f"[快手小店上传] ❌ 第 {row+1} 行无法获取类目信息，使用默认值")
            return "未知类目"
        except Exception as e:
            print(f"[快手小店上传] ❌ 获取主类目名称失败: {e}")
            import traceback
            traceback.print_exc()
            return "未知类目"

    def _get_main_category_id_from_table(self, row: int) -> str:
        """从表格行获取主类目ID"""
        try:
            # 从类目ID列（第4列）获取主类目ID
            category_id_item = self.product_table.item(row, 4)  # 类目ID列
            if category_id_item:
                category_id_str = category_id_item.text().strip()
                if category_id_str:
                    # 如果类目ID列包含ID格式（如"1713,10377,10378"），解析主类目ID
                    if ',' in category_id_str and category_id_str.replace(',', '').replace(' ', '').isdigit():
                        category_ids = [id.strip() for id in category_id_str.split(',') if id.strip()]
                        if category_ids:
                            main_category_id = category_ids[0]  # 第一个是主类目ID
                            print(f"[快手小店上传] 从类目ID列获取主类目ID: {main_category_id}")
                            return main_category_id

            return "未知"
        except Exception as e:
            print(f"[快手小店上传] 获取主类目ID失败: {e}")
            return "未知"





    def _download_and_process_image(self, image_url: str, target_size: int = 800) -> Optional[str]:
        """下载并处理图片为1:1方形，上传到快手获取新链接"""
        try:
            print(f"[快手小店上传] 🖼️ 开始处理图片: {image_url}")

            # 1. 下载图片（添加请求头绕过防盗链）
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://detail.1688.com/',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
            response = requests.get(image_url, headers=headers, timeout=30)
            response.raise_for_status()

            # 2. 打开图片
            image = Image.open(io.BytesIO(response.content))
            print(f"[快手小店上传] 📏 原始图片尺寸: {image.size}")

            # 3. 转换为RGB模式（如果需要）
            if image.mode != 'RGB':
                image = image.convert('RGB')
                print(f"[快手小店上传] 🎨 转换图片模式为RGB")

            # 4. 处理为1:1方形
            processed_image = self._make_square_image(image, target_size)
            print(f"[快手小店上传] ✂️ 处理后图片尺寸: {processed_image.size}")

            # 5. 保存到临时文件
            temp_file = self._save_temp_image(processed_image, image_url)
            print(f"[快手小店上传] 💾 临时文件: {temp_file}")

            # 6. 上传到快手获取新链接
            new_url = self._upload_image_to_kuaishou(temp_file)

            # 7. 清理临时文件
            try:
                os.remove(temp_file)
                print(f"[快手小店上传] 🗑️ 已清理临时文件")
            except:
                pass

            if new_url:
                print(f"[快手小店上传] ✅ 图片处理成功: {new_url}")
                return new_url
            else:
                print(f"[快手小店上传] ❌ 图片上传失败")
                return None

        except Exception as e:
            print(f"[快手小店上传] ❌ 图片处理异常: {e}")
            return None

    def _make_square_image(self, image: Image.Image, target_size: int = 800) -> Image.Image:
        """将图片处理为1:1方形"""
        try:
            width, height = image.size

            # 计算最小边长
            min_side = min(width, height)

            # 如果图片已经是正方形，直接调整大小
            if width == height:
                return image.resize((target_size, target_size), Image.Resampling.LANCZOS)

            # 方案1：智能裁剪（推荐）- 保持图片主要内容
            if abs(width - height) / max(width, height) < 0.3:  # 比例差异小于30%，使用裁剪
                # 从中心裁剪为正方形
                left = (width - min_side) // 2
                top = (height - min_side) // 2
                right = left + min_side
                bottom = top + min_side

                square_image = image.crop((left, top, right, bottom))
                print(f"[快手小店上传] ✂️ 使用中心裁剪: {image.size} -> {square_image.size}")

            else:  # 比例差异较大，使用填充
                # 方案2：添加白色背景填充
                square_image = Image.new('RGB', (max(width, height), max(width, height)), 'white')

                # 将原图居中粘贴
                paste_x = (max(width, height) - width) // 2
                paste_y = (max(width, height) - height) // 2
                square_image.paste(image, (paste_x, paste_y))
                print(f"[快手小店上传] 🎨 使用白色填充: {image.size} -> {square_image.size}")

            # 调整到目标大小
            final_image = square_image.resize((target_size, target_size), Image.Resampling.LANCZOS)
            return final_image

        except Exception as e:
            print(f"[快手小店上传] ❌ 图片方形处理异常: {e}")
            # 如果处理失败，返回原图调整大小
            return image.resize((target_size, target_size), Image.Resampling.LANCZOS)

    def _save_temp_image(self, image: Image.Image, original_url: str) -> str:
        """保存图片到临时文件"""
        try:
            # 创建临时目录
            temp_dir = os.path.join(tempfile.gettempdir(), "kuaishou_images")
            os.makedirs(temp_dir, exist_ok=True)

            # 生成唯一文件名
            url_hash = hashlib.md5(original_url.encode()).hexdigest()[:8]
            timestamp = int(time.time())
            filename = f"img_{timestamp}_{url_hash}.jpg"
            temp_file = os.path.join(temp_dir, filename)

            # 保存图片
            image.save(temp_file, 'JPEG', quality=95, optimize=True)
            return temp_file

        except Exception as e:
            print(f"[快手小店上传] ❌ 保存临时图片异常: {e}")
            raise

    def _upload_image_to_kuaishou(self, image_path: str, upload_type: int = 1) -> Optional[str]:
        """上传图片到快手平台获取链接"""
        try:
            print(f"[快手小店上传] 📤 上传图片到快手平台...")

            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_bytes = f.read()

            print(f"[快手小店上传] 📏 图片大小: {len(image_bytes)} 字节")

            # 检查图片大小（不能超过2M）
            if len(image_bytes) > 2 * 1024 * 1024:
                print(f"[快手小店上传] ❌ 图片过大: {len(image_bytes)} 字节 > 2MB")
                return None

            # 🚀 使用当前实例的快手API（已经有access_token）
            if not hasattr(self, 'kuaishou_api') or not self.kuaishou_api:
                print(f"[快手小店上传] ❌ 快手API未初始化")
                return None

            print(f"[快手小店上传] 🔄 调用快手图片上传API...")

            # 使用现有的multipart上传方法
            response = self.kuaishou_api._upload_image_multipart(
                img_url='',  # 原始URL（可以为空）
                img_bytes=image_bytes,
                upload_type=upload_type
            )

            if response and response.get('success'):
                data = response.get('data', {})
                kwai_img_url = data.get('kwaiImgUrl')

                if kwai_img_url:
                    print(f"[快手小店上传] ✅ 图片上传成功: {kwai_img_url}")
                    return kwai_img_url
                else:
                    print(f"[快手小店上传] ❌ 响应中没有图片URL: {data}")
                    return None
            else:
                error_msg = response.get('message', '未知错误') if response else '请求失败'
                print(f"[快手小店上传] ❌ 图片上传失败: {error_msg}")
                return None

        except Exception as e:
            print(f"[快手小店上传] ❌ 上传图片到快手异常: {e}")
            import traceback
            print(f"[快手小店上传] 异常详情: {traceback.format_exc()}")
            return None


class KuaishouStoreUploadProcessor(AttributeQueryMixin):
    """快手小店商品上传处理器"""

    def __init__(self, template_path: str = None):
        """初始化上传处理器"""
        if template_path is None:
            # 使用绝对路径确保能找到模板文件
            import os
            current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            template_path = os.path.join(current_dir, "config", "快手小店上传模板.json")
            print(f"[快手小店上传] 使用默认模板路径: {template_path}")

        self.template_path = template_path
        self.template_config = self._load_template()
        self.kuaishou_api = None
        self.image_cache = {}  # 图片上传缓存
        self.attr_mapping_config = self._load_attr_mapping_config()  # 属性智能匹配配置
        self.attr_mapping_updates = {}  # 记录需要更新的属性映射
        self.image_filter_settings = {  # 图片过滤设置
            'main_image': {
                'delete_before': 0,  # 主图删除前N张
                'delete_after': 0    # 主图删除后N张
            },
            'detail_image': {
                'delete_before': 0,  # 详情图删除前N张
                'delete_after': 0    # 详情图删除后N张
            }
        }
        self.skip_copy_settings = {  # 跳过复制设置
            'enabled': False,
            'keywords': []
        }

    def _load_template(self) -> Dict:
        """加载外部模板配置文件"""
        try:
            if os.path.exists(self.template_path):
                with open(self.template_path, 'r', encoding='utf-8') as f:
                    template_config = json.load(f)
                print(f"[微信小店上传] 成功加载模板配置: {self.template_path}")
                return template_config
            else:
                print(f"[微信小店上传] 模板文件不存在: {self.template_path}")
                # 如果外部模板不存在，使用内置模板
                template = KuaishouStoreUploadTemplate()
                return template.template_config
        except Exception as e:
            print(f"[快手小店上传] 加载模板配置失败: {e}")
            # 如果加载失败，使用内置模板
            template = KuaishouStoreUploadTemplate()
            return template.template_config

    def _load_attr_mapping_config(self) -> Dict:
        """加载属性智能匹配配置"""
        try:
            config_path = "config/属性智能匹配.json"
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    print(f"[微信小店上传] 成功加载属性智能匹配配置")
                    return config
            else:
                print(f"[微信小店上传] 属性智能匹配配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            print(f"[微信小店上传] 加载属性智能匹配配置失败: {e}")
            return {}



    def set_kuaishou_api(self, config_data: Dict = None):
        """设置快手API实例"""
        try:
            if config_data:
                # 使用传入的配置数据
                self.kuaishou_api = KuaishouAPI(
                    app_key=config_data.get('app_key'),
                    app_secret=config_data.get('app_secret'),
                    access_token=config_data.get('access_token'),
                    sign_secret=config_data.get('signSecret')
                )
            else:
                # 从配置文件自动加载
                self.kuaishou_api = KuaishouAPI()
            print(f"[快手小店上传] 快手API初始化成功")
        except Exception as e:
            print(f"[快手小店上传] 快手API初始化失败: {e}")
            self.kuaishou_api = None
    
    def process_product_data(self, cache_data: Dict, category_id: int,
                           freight_template_id: int = None, row: int = -1) -> Dict:
        """
        处理商品数据，转换为微信小店格式

        Args:
            cache_data: 1688商品缓存数据
            category_id: 微信小店类目ID
            freight_template_id: 运费模板ID

        Returns:
            转换后的微信小店商品数据
        """
        try:
            # 确保 freight_template_id 有默认值
            if freight_template_id is None:
                freight_template_id = 19219058228

            print(f"[微信小店上传] ===== 开始处理商品数据 =====")
            print(f"[微信小店上传] cache_data keys: {list(cache_data.keys())}")
            print(f"[微信小店上传] category_id: {category_id}")
            print(f"[微信小店上传] freight_template_id: {freight_template_id}")
            print(f"[微信小店上传] row: {row}")
            logger.info(f"开始处理商品数据: {cache_data.get('title', 'Unknown')}")

            # 🚀 保存当前处理的行号，供属性查询和缓存使用
            self.current_row = row

            # 🚀 如果没有product_table，尝试从parent_window获取
            if not hasattr(self, 'product_table') or self.product_table is None:
                if hasattr(self, 'parent_window') and hasattr(self.parent_window, 'product_table'):
                    self.product_table = self.parent_window.product_table
                    print(f"[微信小店上传] 从parent_window获取product_table")
                else:
                    print(f"[微信小店上传] ⚠️ 无法获取product_table，主类目名称获取可能失败")

            # 设置当前商品标题，供属性智能判断使用
            self.current_product_title = cache_data.get('title', '')

            # 获取模板配置
            field_mapping = self.template_config['field_mapping']
            default_config = self.template_config['default_config']
            
            # 构建基础商品数据 - 按照微信小店API要求
            product_data = {
                # 基础信息
                'deliver_method': 0,  # 0-快递发货
                'brand_id': str(default_config.get('brand_id', 2100000000)),  # 无品牌为"2100000000"
                'listing': 1 if default_config.get('listing', False) else 0,  # 转换为数字

                # 售后服务（必填）
                'extra_service': {
                    'seven_day_return': 1,  # 支持七天无理由退货
                    'freight_insurance': 0   # 不支持运费险
                },

                # 售后地址（必填）
                'after_sale_info': {
                    'after_sale_address_id': 1  # 默认售后地址ID，需要商家先设置
                }
            }
            
            # 处理基础信息 - 直接按照新模板结构处理
            product_data.update(self._process_all_fields(cache_data, field_mapping, freight_template_id, category_id))
            
            # 处理图片信息
            print(f"[微信小店上传] 准备调用 _process_images 方法")
            image_data = self._process_images(cache_data, field_mapping)
            print(f"[微信小店上传] _process_images 方法调用完成，返回数据: {list(image_data.keys()) if image_data else 'None'}")
            product_data.update(image_data)

            # 处理SKU信息
            product_data['skus'] = self._process_skus(cache_data, field_mapping)

            # 处理商品属性 - 从缓存的 product_attribute 获取
            product_attributes = cache_data.get('product_attribute', [])
            if product_attributes:
                print(f"[微信小店上传] 找到 {len(product_attributes)} 个商品属性")
                # 这里可以根据需要处理商品属性
                # 微信小店的商品属性格式可能需要特殊处理

            # 数据验证
            self._validate_product_data(product_data)

            # 保存属性映射更新建议
            self._save_mapping_updates()

            logger.info(f"商品数据处理完成: {product_data.get('title')}")
            return product_data
            
        except Exception as e:
            logger.error(f"处理商品数据失败: {e}")
            raise
    
    def _process_all_fields(self, cache_data: Dict, field_mapping: Dict, freight_template_id: int = None, category_id: int = None) -> Dict:
        """处理所有字段 - 按照新模板结构"""
        result = {}

        for field_name, config in field_mapping.items():
            # 跳过特殊处理的字段
            if field_name in ['image_info', 'sku_info', 'service_info']:
                continue

            # 处理字段分组（如 basic_info）
            if isinstance(config, dict) and any(isinstance(v, dict) and 'source_field' in v for v in config.values()):
                # 这是一个字段分组，处理其中的每个字段
                for sub_field_name, sub_config in config.items():
                    if isinstance(sub_config, dict) and 'source_field' in sub_config:
                        field_value = self._extract_field_value(cache_data, sub_config)
                        if field_value is not None:
                            result[sub_field_name] = field_value
                continue

            try:
                if field_name == 'cats_v2':
                    # 处理类目信息 - 优先使用传入的category_id参数
                    if category_id and category_id not in [1001, 1002, 1003]:  # 排除模板默认值
                        # 使用传入的category_id参数（这是从表格解析的正确值）
                        print(f"[微信小店上传] 使用传入的类目ID参数: {category_id}")
                        cats_v2 = [{"cat_id": str(category_id)}]
                    else:
                        # 从缓存数据中获取类目ID作为备选
                        category_text = str(cache_data.get('category_id', ''))
                        cats_v2 = []
                        if category_text and category_text not in ['1001', '1002', '1003', '']:
                            # 解析类目ID，可能是逗号分隔的多个ID：10000212,10000213,7389
                            category_ids = [id.strip() for id in category_text.split(',') if id.strip()]
                            for cat_id in category_ids:
                                cats_v2.append({"cat_id": cat_id})
                            print(f"[微信小店上传] 从缓存数据获取类目ID: {category_text}")
                            print(f"[微信小店上传] 解析后的类目列表: {cats_v2}")
                        else:
                            # 如果没有类目ID，使用默认的测试类目
                            cats_v2 = [
                                {"cat_id": "10000111"},  # 一级类目：服饰鞋包
                                {"cat_id": "10000113"},  # 二级类目：女鞋
                                {"cat_id": "6091"}       # 三级类目：运动鞋
                            ]
                            print(f"[微信小店上传] 未找到类目ID，使用默认测试类目: {cats_v2}")

                    result[field_name] = cats_v2

                elif field_name == 'attrs':
                    # 只添加类目要求的必填属性，不使用缓存中的属性
                    # 获取最后一个类目ID（叶子类目）
                    category_text = str(cache_data.get('category_id', ''))
                    leaf_category_id = "10378"  # 使用有效的叶子类目ID作为默认值
                    if category_text:
                        category_ids = [id.strip() for id in category_text.split(',') if id.strip()]
                        if category_ids:
                            leaf_category_id = category_ids[-1]  # 使用最后一个类目ID（叶子类目）

                    required_attrs = self._get_required_attrs_only(leaf_category_id, cache_data)
                    result[field_name] = required_attrs
                    print(f"[微信小店上传] 使用类目ID {leaf_category_id} 获取必填属性: {len(required_attrs)} 个")

                elif field_name == 'express_info':
                    # 处理运费模板 - 使用传入的运费模板ID
                    template_id = freight_template_id if freight_template_id else 1
                    result[field_name] = {"template_id": str(template_id)}
                    print(f"[微信小店上传] 使用运费模板ID: {template_id}")

                elif field_name == 'extra_service':
                    # 处理售后服务（必填）
                    result[field_name] = {
                        "seven_day_return": 1,  # 支持七天无理由退货
                        "freight_insurance": 0   # 不支持运费险
                    }

                elif field_name == 'after_sale_info':
                    # 处理售后地址（必填）
                    return_address_id = self._get_return_address_id()
                    result[field_name] = {
                        "after_sale_address_id": return_address_id
                    }
                    print(f"[微信小店上传] 使用退货地址ID: {return_address_id}")

                elif field_name == 'listing':
                    # 处理上架状态 - 微信API要求数字类型：1=立即上架，0=不立即上架
                    shelf_status = self._get_shelf_status()
                    result[field_name] = shelf_status  # 直接使用数字值（1或0）
                    print(f"[微信小店上传] 设置上架状态: {result[field_name]} ({'立即上架' if shelf_status == 1 else '不立即上架'})")

                elif field_name == 'cats':
                    # 旧版类目，设为空数组
                    result[field_name] = []

                elif field_name == 'spu_code':
                    # 处理商品编码 - 从缓存数据中获取product_id
                    product_id = self._get_product_id_from_cache(cache_data)
                    if product_id:
                        result[field_name] = str(product_id)
                        print(f"[微信小店上传] 设置商品编码(spu_code): {result[field_name]}")
                    else:
                        print(f"[微信小店上传] 未找到product_id，跳过spu_code设置")

                else:
                    # 处理普通字段
                    source_field = config.get('source_field', field_name)
                    value = cache_data.get(source_field, config.get('default', ''))

                    # 应用数据处理器
                    if 'filters' in config:
                        for filter_name in config['filters']:
                            value = self._apply_filter(value, filter_name)

                    # 长度限制
                    if 'max_length' in config and len(str(value)) > config['max_length']:
                        value = str(value)[:config['max_length']]

                    result[field_name] = value

            except Exception as e:
                print(f"[微信小店上传] 处理字段 {field_name} 失败: {e}")
                # 设置默认值
                result[field_name] = config.get('default', '')

        # 添加运费模板ID和类目ID到结果中
        result['freight_template_id'] = freight_template_id
        result['category_id'] = category_id  # 确保类目ID被正确传递
        print(f"[微信小店上传] 设置最终类目ID到结果: {category_id}")

        return result

    def _get_product_id_from_cache(self, cache_data: Dict) -> str:
        """从缓存数据中获取商品ID"""
        # 尝试从不同位置获取product_id
        product_id = None

        # 首先尝试从顶层获取
        if 'product_id' in cache_data:
            product_id = cache_data['product_id']
        # 然后尝试从product_info层级获取
        elif 'product_info' in cache_data and 'product_id' in cache_data['product_info']:
            product_id = cache_data['product_info']['product_id']

        return str(product_id) if product_id else None

    def _convert_to_attrs(self, product_attrs: List) -> List[Dict]:
        """转换商品属性为微信小店格式"""
        attrs = []
        attr_groups = {}  # 用于合并相同属性名的多个值

        for attr in product_attrs:
            # 根据实际缓存格式处理属性
            if isinstance(attr, dict):
                attr_name = attr.get('attributeName', attr.get('name', ''))
                attr_value = attr.get('value', '')

                if attr_name and attr_value:
                    # 将相同属性名的值合并
                    if attr_name not in attr_groups:
                        attr_groups[attr_name] = []

                    # 避免重复的值
                    if attr_value not in attr_groups[attr_name]:
                        attr_groups[attr_name].append(attr_value)

        # 转换为微信小店格式
        for attr_name, values in attr_groups.items():
            if len(values) == 1:
                # 单个值
                attrs.append({
                    "attr_key": str(attr_name),
                    "attr_value": str(values[0])
                })
            else:
                # 多个值用分号分隔（根据微信API文档）
                combined_value = ";".join(values)
                attrs.append({
                    "attr_key": str(attr_name),
                    "attr_value": combined_value
                })
                print(f"[微信小店上传] 合并属性 {attr_name}: {combined_value}")

        print(f"[微信小店上传] 转换商品属性: {len(attrs)} 个属性")
        return attrs

    def _get_required_attrs_only(self, category_id: str, cache_data: Dict = None) -> List[Dict]:
        """通过API查询类目要求的必填属性，并尝试从商品缓存中匹配属性值"""
        try:
            # 设置当前类目ID，用于类目特定的属性匹配
            self.current_category_id = category_id

            # 获取类目详情
            print(f"[微信小店上传] 查询类目 {category_id} 的必填属性")
            category_detail = self._get_category_detail(category_id)

            if category_detail and 'attr' in category_detail and 'product_attr_list' in category_detail['attr']:
                required_attrs = []
                product_attr_list = category_detail['attr']['product_attr_list']

                # 从商品缓存中获取现有属性
                cache_attrs = {}
                if cache_data:
                    # 尝试从不同的字段获取属性信息
                    # 首先尝试从 product_info 层级获取
                    product_attrs = []
                    if 'product_info' in cache_data and 'product_attribute' in cache_data['product_info']:
                        product_attrs = cache_data['product_info']['product_attribute']
                    else:
                        # 备用：从顶层获取
                        product_attrs = cache_data.get('product_attribute', [])

                    if isinstance(product_attrs, list):
                        for attr in product_attrs:
                            if isinstance(attr, dict) and 'value' in attr:
                                # 支持两种字段名格式：attributeName 和 name
                                attr_name = attr.get('attributeName', attr.get('name', ''))
                                if attr_name:
                                    cache_attrs[attr_name] = attr['value']

                    # 也尝试从其他可能的字段获取属性
                    if 'attributes' in cache_data:
                        attrs = cache_data['attributes']
                        if isinstance(attrs, dict):
                            cache_attrs.update(attrs)

                # 如果没有产地属性，尝试从 sendGoodsAddressText 提取
                if '产地' not in cache_attrs:
                    send_goods_address = ''
                    # 尝试从不同位置获取发货地址
                    if 'product_info' in cache_data and 'product_shipping_info' in cache_data['product_info']:
                        send_goods_address = cache_data['product_info']['product_shipping_info'].get('sendGoodsAddressText', '')
                    elif 'product_info' in cache_data:
                        send_goods_address = cache_data['product_info'].get('sendGoodsAddressText', '')

                    if send_goods_address:
                        # 提取城市名（格式：江苏省 徐州市 → 徐州市）
                        import re
                        city_match = re.search(r'(\S+市)', send_goods_address)
                        if city_match:
                            city_name = city_match.group(1)
                            cache_attrs['产地'] = city_name
                            print(f"[微信小店上传] 从发货地址提取产地: {city_name}")

                print(f"[微信小店上传] 从缓存中找到 {len(cache_attrs)} 个属性")
                if cache_attrs:
                    print(f"[微信小店上传] 缓存属性详情: {cache_attrs}")

                for attr in product_attr_list:
                    if attr.get('is_required', False):  # 只处理必填属性
                        attr_name = attr['name']
                        attr_type = attr.get('type_v2', attr.get('type', 'string'))
                        attr_options = attr.get('value', '')

                        # 智能匹配属性值
                        attr_value = None

                        # 首先检查是否有固定值配置
                        if self.attr_mapping_config and 'global_attribute_mapping' in self.attr_mapping_config:
                            mapping_config = self.attr_mapping_config['global_attribute_mapping']
                            if attr_name in mapping_config:
                                attr_config = mapping_config[attr_name]

                                # 检查固定值配置
                                if 'fixed_value' in attr_config:
                                    fixed_value = attr_config['fixed_value']
                                    if not attr_options or fixed_value in [opt.strip() for opt in attr_options.split(';') if opt.strip()]:
                                        attr_value = fixed_value
                                        print(f"[微信小店上传] 使用固定值配置: {attr_name} = {fixed_value}")
                                    else:
                                        print(f"[微信小店上传] 固定值'{fixed_value}'不在可选项中，跳过固定值配置")

                        # 如果没有固定值配置，继续正常的匹配逻辑
                        if attr_value is None:
                            # 🔧 修复：优先检查特定类目映射，再检查缓存匹配
                            smart_match_value = self._smart_match_attr_value(attr_name, attr_type, attr_options, cache_attrs)

                            if smart_match_value:
                                # 智能匹配成功（包括特定类目映射）
                                attr_value = smart_match_value
                                print(f"[微信小店上传] 智能匹配属性: {attr_name} = {attr_value}")
                                # 记录智能匹配情况
                                self._log_attribute_mismatch(attr_name, "智能匹配", attr_value, "smart_match")
                            elif attr_name in cache_attrs:
                                # 缓存匹配：作为备选方案
                                original_value = cache_attrs[attr_name]
                                attr_value = self._format_attr_value(str(original_value), attr_name, attr_type)
                                # 对于选择类型，需要验证值是否在微信允许的选项中
                                if attr_type in ['select_one', 'select_many'] and attr_options:
                                    attr_value = self._match_to_wechat_options(attr_value, attr_options, attr_name)
                                print(f"[微信小店上传] 从缓存匹配属性: {attr_name} = {attr_value} (原始值: {original_value})")
                                # 记录属性匹配情况
                                self._log_attribute_mismatch(attr_name, str(original_value), attr_value, "cache_match")
                            else:
                                # 没有任何匹配，使用默认值
                                attr_value = self._get_default_attr_value(attr_name, attr_type, attr_options)
                                print(f"[微信小店上传] 使用默认属性: {attr_name} = {attr_value}")
                                # 记录默认值使用情况
                                self._log_attribute_mismatch(attr_name, "无匹配", attr_value, "default_value")

                        if attr_value:
                            required_attrs.append({
                                "attr_key": attr_name,
                                "attr_value": attr_value
                            })
                            print(f"[微信小店上传] 添加到必填属性列表: {attr_name} = {attr_value}")

                print(f"[微信小店上传] 从API获取到 {len(required_attrs)} 个必填属性")
                return required_attrs
            else:
                print(f"[微信小店上传] 无法获取类目详情，使用默认属性")

        except Exception as e:
            print(f"[微信小店上传] 获取类目详情失败: {e}")

        # 如果API调用失败，返回空数组（不添加任何属性）
        return []

    def _get_category_detail(self, cat_id: str) -> Dict:
        """获取类目详情"""
        try:
            if not self.wechat_api:
                print(f"[微信小店上传] 微信API未初始化")
                return {}

            # 调用微信类目详情接口
            response = self.wechat_api.get_category_detail(cat_id)

            if response.get('errcode') == 0:
                print(f"[微信小店上传] 成功获取类目 {cat_id} 详情")
                return response
            else:
                print(f"[微信小店上传] 获取类目详情失败: {response.get('errmsg', '未知错误')}")
                return {}

        except Exception as e:
            print(f"[微信小店上传] 获取类目详情异常: {e}")
            return {}

    def _get_default_attr_value(self, attr_name: str, attr_type: str, attr_options: str) -> str:
        """根据属性类型和选项获取默认值，使用智能匹配配置"""
        try:
            # 解析微信API返回的选项
            available_options = []
            if attr_options:
                available_options = [opt.strip() for opt in attr_options.split(';') if opt.strip()]

            # 使用智能匹配配置
            if self.attr_mapping_config and 'global_attribute_mapping' in self.attr_mapping_config:
                mapping_config = self.attr_mapping_config['global_attribute_mapping']

                if attr_name in mapping_config:
                    attr_config = mapping_config[attr_name]

                    # 注意：fixed_value 配置已在主属性匹配逻辑中处理，这里不再重复检查

                    # 检查是否有基于标题的配置
                    if 'title_based' in attr_config and attr_config['title_based']:
                        product_title = getattr(self, 'current_product_title', '')
                        if 'title_keywords' in attr_config:
                            title_keywords = attr_config['title_keywords']
                            for keyword, value in title_keywords.items():
                                if keyword in product_title and (not available_options or value in available_options):
                                    print(f"[微信小店上传] 基于标题关键词'{keyword}'匹配: {attr_name} = {value}")
                                    # 记录基于标题的匹配
                                    self._log_attribute_mismatch(attr_name, f"标题关键词:{keyword}", value, "title_based")
                                    return value
                        # 使用配置中的fallback
                        if 'fallback' in attr_config:
                            fallback_value = attr_config['fallback']
                            if not available_options or fallback_value in available_options:
                                print(f"[微信小店上传] 使用配置fallback: {attr_name} = {fallback_value}")
                                # 记录配置fallback的使用
                                self._log_attribute_mismatch(attr_name, "配置fallback", fallback_value, "config_fallback")
                                return fallback_value

                    # 优先级匹配
                    if 'priority_mapping' in attr_config and available_options:
                        priority_mapping = attr_config['priority_mapping']

                        # 按优先级查找匹配的选项
                        for preferred_value, keywords in priority_mapping.items():
                            if preferred_value in available_options:
                                print(f"[微信小店上传] 智能匹配属性: {attr_name} = {preferred_value} (优先级匹配)")
                                return preferred_value

                        # 模糊匹配
                        for preferred_value, keywords in priority_mapping.items():
                            for keyword in keywords:
                                # 使用difflib进行模糊匹配
                                matches = difflib.get_close_matches(keyword, available_options, n=1, cutoff=0.6)
                                if matches:
                                    print(f"[微信小店上传] 智能匹配属性: {attr_name} = {matches[0]} (模糊匹配: {keyword})")
                                    return matches[0]

                    # 使用fallback值
                    if 'fallback' in attr_config and attr_config['fallback'] in available_options:
                        fallback_value = attr_config['fallback']
                        print(f"[微信小店上传] 使用fallback值: {attr_name} = {fallback_value}")
                        return fallback_value

            # 处理不同类型的属性
            if attr_type in ['select_one', 'select_many'] and available_options:
                # 获取商品标题用于智能判断
                product_title = getattr(self, 'current_product_title', '')

                # 根据属性名和标题内容进行智能判断
                if attr_name == "出水方式":
                    if "吸管" in product_title and "吸管" in available_options:
                        print(f"[微信小店上传] 根据标题'{product_title}'智能判断: {attr_name} = 吸管")
                        return "吸管"
                    elif "按压" in product_title and "按压" in available_options:
                        print(f"[微信小店上传] 根据标题'{product_title}'智能判断: {attr_name} = 按压")
                        return "按压"
                    elif "直饮" in available_options:
                        print(f"[微信小店上传] 根据标题'{product_title}'智能判断: {attr_name} = 直饮 (默认)")
                        return "直饮"

                elif attr_name == "是否带吸管":
                    if "吸管" in product_title and "是" in available_options:
                        print(f"[微信小店上传] 根据标题'{product_title}'智能判断: {attr_name} = 是")
                        return "是"
                    elif "否" in available_options:
                        print(f"[微信小店上传] 根据标题'{product_title}'智能判断: {attr_name} = 否 (默认)")
                        return "否"

                elif attr_name == "杯身材质":
                    # 杯身材质匹配不到默认使用塑料
                    if "塑料" in available_options:
                        print(f"[微信小店上传] 杯身材质默认使用: 塑料")
                        return "塑料"

                # 选择类型，从可用选项中选择第一个
                selected = available_options[0]
                print(f"[微信小店上传] 选择类型属性: {attr_name} = {selected}")
                return selected
            elif attr_type == 'integer':
                # 整数类型
                return "1"
            elif attr_type == 'decimal4':
                # 小数类型
                return "1.0"
            elif attr_type == 'integer_unit' and available_options:
                # 整数+单位类型
                units = available_options
                if units:
                    return f"1 {units[0]}"
            elif attr_type == 'decimal4_unit' and available_options:
                # 小数+单位类型
                units = available_options
                if units:
                    return f"1.0 {units[0]}"
            elif attr_type == 'string':
                # 文本类型
                return "其他"

            # 如果没有匹配的处理方式，返回空字符串
            print(f"[微信小店上传] 无法为属性 {attr_name} (类型: {attr_type}) 生成默认值")
            return ""

        except Exception as e:
            print(f"[微信小店上传] 生成默认属性值失败: {e}")
            return ""

    def _apply_attribute_mapping(self, target_attr: str, attr_config: dict, cache_attrs: dict) -> str:
        """应用特定类目映射配置"""
        try:
            # 获取配置信息
            fallback = attr_config.get('fallback', '')
            priority_mapping = attr_config.get('priority_mapping', {})

            print(f"[微信小店上传] 应用特定类目映射: {target_attr}, fallback={fallback}")

            # 查找可能的1688属性值
            possible_values = []

            # 从缓存中查找相关属性值
            attr_mapping = {
                "材质": ["材质", "主要材质", "杯身材质", "内胆材质", "外壳材质"],
                "容量": ["容量", "体积", "规格"],
                "款式": ["款式", "风格", "样式"],
                "颜色": ["颜色", "色彩", "主色调"]
            }

            possible_attrs = attr_mapping.get(target_attr, [target_attr])

            for possible_attr in possible_attrs:
                if possible_attr in cache_attrs:
                    value = str(cache_attrs[possible_attr])
                    possible_values.append(value)
                    print(f"[微信小店上传] 找到1688属性值: {possible_attr}={value}")

            # 如果没有找到1688属性值，从标题中提取关键词
            if not possible_values and hasattr(self, 'current_title') and self.current_title:
                title = self.current_title
                print(f"[微信小店上传] 从标题中查找关键词: {title}")

                # 检查标题中是否包含映射的关键词
                for wechat_value, keywords in priority_mapping.items():
                    for keyword in keywords:
                        if keyword in title:
                            print(f"[微信小店上传] 配置智能匹配: 从标题'{title}' 中的关键词'{keyword}' 匹配到'{target_attr}': {wechat_value}")
                            return wechat_value

            # 使用优先级映射匹配
            for value in possible_values:
                for wechat_value, keywords in priority_mapping.items():
                    for keyword in keywords:
                        if keyword in value:
                            print(f"[微信小店上传] 配置智能匹配: 从缓存值'{value}' 中的关键词'{keyword}' 匹配到'{target_attr}': {wechat_value}")
                            return wechat_value

            # 如果没有匹配到，使用fallback
            if fallback:
                print(f"[微信小店上传] 使用fallback值: {target_attr}={fallback}")
                return fallback

            return None

        except Exception as e:
            print(f"[微信小店上传] 应用特定类目映射失败: {str(e)}")
            return None

    def _format_attr_value(self, value: str, attr_name: str, attr_type: str) -> str:
        """格式化属性值，处理微信平台不支持的格式"""
        try:
            # 处理容量等带单位的范围值
            if attr_name in ['容量', '重量', '尺寸'] and '-' in value:
                import re

                # 🔧 优化：处理多种范围格式
                # 格式1: "201mL(含)-300mL(含)" -> "201ml"
                # 格式2: "401-500ml" -> "401ml"
                # 格式3: "401ml-500ml" -> "401ml"
                # 格式4: "1-2L" -> "1l"

                # 先尝试匹配复杂格式：数字+单位(含)-数字+单位(含)
                complex_match = re.search(r'(\d+)(\w+)\([^)]*\)-(\d+)(\w+)\([^)]*\)', value)
                if complex_match:
                    min_val, min_unit, max_val, max_unit = complex_match.groups()
                    # 使用最小值和单位，并转换为小写
                    unit = min_unit.lower()
                    formatted_value = f"{min_val}{unit}"
                    print(f"[微信小店上传] 格式化复杂范围属性: {attr_name} {value} -> {formatted_value}")
                    return formatted_value

                # 尝试匹配单位+数字-单位+数字格式：401ml-500ml
                unit_range_match = re.search(r'(\d+)(\w+)-(\d+)(\w+)', value)
                if unit_range_match:
                    min_val, min_unit, max_val, max_unit = unit_range_match.groups()
                    # 使用最小值和单位，并转换为小写
                    unit = min_unit.lower()
                    formatted_value = f"{min_val}{unit}"
                    print(f"[微信小店上传] 格式化单位范围属性: {attr_name} {value} -> {formatted_value}")
                    return formatted_value

                # 最后尝试匹配简单格式：数字-数字+单位
                simple_match = re.search(r'(\d+)-(\d+)(\w+)', value)
                if simple_match:
                    min_val, max_val, unit = simple_match.groups()
                    # 使用最小值和单位，并转换为小写
                    unit = unit.lower()
                    formatted_value = f"{min_val}{unit}"
                    print(f"[微信小店上传] 格式化简单范围属性: {attr_name} {value} -> {formatted_value}")
                    return formatted_value

            # 其他属性直接返回原值
            return value

        except Exception as e:
            print(f"[微信小店上传] 格式化属性值失败: {e}")
            return value

    def _match_to_wechat_options(self, value: str, options_str: str, attr_name: str) -> str:
        """将属性值匹配到微信允许的选项中"""
        try:
            available_options = [opt.strip() for opt in options_str.split(';') if opt.strip()]

            # 直接匹配
            if value in available_options:
                return value

            # 模糊匹配
            import difflib
            matches = difflib.get_close_matches(value, available_options, n=1, cutoff=0.6)
            if matches:
                print(f"[微信小店上传] 模糊匹配 {attr_name}: {value} -> {matches[0]}")
                return matches[0]

            # 关键词匹配
            value_lower = value.lower()
            for option in available_options:
                option_lower = option.lower()
                if option_lower in value_lower or value_lower in option_lower:
                    print(f"[微信小店上传] 关键词匹配 {attr_name}: {value} -> {option}")
                    return option

            # 特殊规则匹配 - 根据微信实际允许的选项进行匹配
            if attr_name == "材质":
                # 塑料相关材质的智能匹配
                if "塑料" in value or "塑胶" in value:
                    if "塑料" in available_options:
                        return "塑料"
                    elif "复合材料" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 复合材料 (塑料类材质)")
                        return "复合材料"
                    elif "其他" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 其他 (塑料类材质)")
                        return "其他"

                # 玻璃相关材质
                elif "玻璃" in value:
                    if "玻璃" in available_options:
                        return "玻璃"
                    elif "玻璃钢" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 玻璃钢 (玻璃类材质)")
                        return "玻璃钢"
                    elif "其他" in available_options:
                        return "其他"

                # 金属相关材质
                elif "不锈钢" in value or "金属" in value or "铝" in value or "钛" in value:
                    if "不锈钢" in available_options:
                        return "不锈钢"
                    elif "铝合金" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 铝合金 (金属类材质)")
                        return "铝合金"
                    elif "钛合金" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 钛合金 (金属类材质)")
                        return "钛合金"
                    elif "其他" in available_options:
                        return "其他"

                # 碳纤维相关材质
                elif "碳纤维" in value or "碳素" in value:
                    if "碳纤维" in available_options:
                        return "碳纤维"
                    elif "高碳纤维" in available_options:
                        print(f"[微信小店上传] 材质匹配: {value} -> 高碳纤维 (碳纤维类材质)")
                        return "高碳纤维"
                    elif "复合材料" in available_options:
                        return "复合材料"
                    elif "其他" in available_options:
                        return "其他"

            # 特殊处理：杯身材质匹配不上时使用塑料
            if attr_name == "杯身材质":
                if "塑料" in available_options:
                    print(f"[微信小店上传] 杯身材质无法匹配 {value}，使用默认值: 塑料")
                    return "塑料"

            # 如果都匹配不上，返回"其他"（如果存在）
            if "其他" in available_options:
                print(f"[微信小店上传] 无法匹配 {attr_name}: {value}，使用'其他'")
                return "其他"

            # 最后返回第一个选项
            if available_options:
                print(f"[微信小店上传] 无法匹配 {attr_name}: {value}，使用第一个选项: {available_options[0]}")
                return available_options[0]

            return value

        except Exception as e:
            print(f"[微信小店上传] 匹配微信选项失败: {e}")
            return value

    def _smart_match_attr_value(self, target_attr: str, attr_type: str, attr_options: str, cache_attrs: dict) -> str:
        """智能匹配：从1688缓存属性中找到最合适的值"""
        try:
            # 🔧 修复：优先检查特定类目映射，再进行常规匹配
            if self.attr_mapping_config:
                # 首先尝试类目特定的配置
                if hasattr(self, 'current_category_id') and self.current_category_id:
                    category_mapping = self.attr_mapping_config.get('category_specific_mapping', {})
                    print(f"[微信小店上传] 🔍 查找特定类目映射: current_category_id={self.current_category_id}, 可用配置={list(category_mapping.keys())}")

                    # 查找匹配的类目配置
                    matched_config = None
                    matched_name = None

                    # 方式1：直接匹配类目ID
                    if str(self.current_category_id) in category_mapping:
                        matched_config = category_mapping[str(self.current_category_id)]
                        matched_name = str(self.current_category_id)
                        print(f"[微信小店上传] ✅ 方式1匹配成功: 直接匹配类目ID {self.current_category_id}")
                    else:
                        print(f"[微信小店上传] ❌ 方式1失败: 类目ID {self.current_category_id} 不在配置键中")

                        # 方式2：通过category_ids数组匹配
                        for config_name, config_data in category_mapping.items():
                            category_ids = config_data.get('category_ids', [])
                            print(f"[微信小店上传] 🔍 检查配置'{config_name}': category_ids={category_ids}")
                            if str(self.current_category_id) in category_ids:
                                matched_config = config_data
                                matched_name = config_name
                                print(f"[微信小店上传] ✅ 方式2匹配成功: 在'{config_name}'的category_ids中找到{self.current_category_id}")
                                break

                        if not matched_config:
                            print(f"[微信小店上传] ❌ 方式2失败: 类目ID {self.current_category_id} 不在任何category_ids中")

                    if matched_config:
                        # 支持两种格式：attributes 和 override_attributes
                        override_attrs = matched_config.get('override_attributes', matched_config.get('attributes', {}))
                        print(f"[微信小店上传] 🔍 检查'{matched_name}'配置中的属性: {list(override_attrs.keys())}")
                        if target_attr in override_attrs:
                            attr_config = override_attrs[target_attr]
                            print(f"[微信小店上传] ✅ 使用类目{matched_name}特定配置: {target_attr}")

                            # 使用特定类目映射进行匹配
                            matched_value = self._apply_attribute_mapping(target_attr, attr_config, cache_attrs)
                            if matched_value:
                                return matched_value
                        else:
                            print(f"[微信小店上传] ❌ 属性'{target_attr}'不在'{matched_name}'的特定配置中")

            # 如果特定类目映射没有匹配，使用常规匹配逻辑
            print(f"[微信小店上传] 使用常规匹配逻辑: {target_attr}")

            # 属性名映射规则
            attr_mapping = {
                "材质": ["材质", "主要材质", "杯身材质", "内胆材质", "外壳材质"],
                "内胆材质": ["内胆材质", "材质", "主要材质"],
                "杯身材质": ["杯身材质", "材质", "主要材质"],
                "颜色": ["颜色", "色彩", "主色调"],
                "容量": ["容量", "体积", "规格"],
                "重量": ["重量", "净重", "毛重"],
                "尺寸": ["尺寸", "规格", "大小", "尺寸(高度、口径)"],
                "产地": ["产地", "生产地", "制造地"],
                "适用人群": ["适用人群", "适用对象", "目标人群"],
                "功能": ["功能", "特点", "用途"],
                "风格": ["风格", "款式", "样式"],
                "形状": ["形状", "外形", "造型"]
            }

            # 查找匹配的1688属性
            possible_attrs = attr_mapping.get(target_attr, [target_attr])

            for possible_attr in possible_attrs:
                if possible_attr in cache_attrs:
                    original_value = str(cache_attrs[possible_attr])

                    # 格式化值
                    formatted_value = self._format_attr_value(original_value, target_attr, attr_type)

                    # 如果是选择类型，匹配到微信选项
                    if attr_type in ['select_one', 'select_many'] and attr_options:
                        matched_value = self._match_to_wechat_options(formatted_value, attr_options, target_attr)
                        print(f"[微信小店上传] 从1688属性'{possible_attr}': {original_value} 匹配到微信'{target_attr}': {matched_value}")
                        return matched_value
                    else:
                        print(f"[微信小店上传] 从1688属性'{possible_attr}': {original_value} 映射到微信'{target_attr}': {formatted_value}")
                        return formatted_value

            # 如果没有直接匹配，尝试使用全局配置文件的智能匹配
            if self.attr_mapping_config:
                attr_config = None

                # 首先尝试类目特定的配置
                if hasattr(self, 'current_category_id') and self.current_category_id:
                    category_mapping = self.attr_mapping_config.get('category_specific_mapping', {})
                    print(f"[微信小店上传] 🔍 查找特定类目映射: current_category_id={self.current_category_id}, 可用配置={list(category_mapping.keys())}")

                    # 查找匹配的类目配置
                    matched_config = None
                    matched_name = None

                    # 方式1：直接匹配类目ID
                    if str(self.current_category_id) in category_mapping:
                        matched_config = category_mapping[str(self.current_category_id)]
                        matched_name = str(self.current_category_id)
                        print(f"[微信小店上传] ✅ 方式1匹配成功: 直接匹配类目ID {self.current_category_id}")
                    else:
                        print(f"[微信小店上传] ❌ 方式1失败: 类目ID {self.current_category_id} 不在配置键中")

                        # 方式2：通过category_ids数组匹配
                        for config_name, config_data in category_mapping.items():
                            category_ids = config_data.get('category_ids', [])
                            print(f"[微信小店上传] 🔍 检查配置'{config_name}': category_ids={category_ids}")
                            if str(self.current_category_id) in category_ids:
                                matched_config = config_data
                                matched_name = config_name
                                print(f"[微信小店上传] ✅ 方式2匹配成功: 在'{config_name}'的category_ids中找到{self.current_category_id}")
                                break

                        if not matched_config:
                            print(f"[微信小店上传] ❌ 方式2失败: 类目ID {self.current_category_id} 不在任何category_ids中")

                    if matched_config:
                        # 支持两种格式：attributes 和 override_attributes
                        override_attrs = matched_config.get('override_attributes', matched_config.get('attributes', {}))
                        print(f"[微信小店上传] 🔍 检查'{matched_name}'配置中的属性: {list(override_attrs.keys())}")
                        if target_attr in override_attrs:
                            attr_config = override_attrs[target_attr]
                            print(f"[微信小店上传] ✅ 使用类目{matched_name}特定配置: {target_attr}")
                        else:
                            print(f"[微信小店上传] ❌ 属性'{target_attr}'不在'{matched_name}'的特定配置中")
                    else:
                        print(f"[微信小店上传] ❌ 未找到匹配的特定类目配置")

                # 如果没有类目特定配置，使用全局配置
                if not attr_config and 'global_attribute_mapping' in self.attr_mapping_config:
                    mapping_config = self.attr_mapping_config['global_attribute_mapping']
                    if target_attr in mapping_config:
                        attr_config = mapping_config[target_attr]
                        print(f"[微信小店上传] 使用全局配置: {target_attr}")

                # 如果找到了配置，进行智能匹配
                if attr_config and 'priority_mapping' in attr_config:
                    priority_mapping = attr_config['priority_mapping']

                    # 首先检查商品标题（优先级最高）
                    product_title = getattr(self, 'current_product_title', '')
                    if product_title:
                        title_str = product_title.lower()
                        for wechat_value, keywords in priority_mapping.items():
                            for keyword in keywords:
                                if keyword.lower() in title_str:
                                    print(f"[微信小店上传] 配置智能匹配: 从标题'{product_title}' 中的关键词'{keyword}' 匹配到'{target_attr}': {wechat_value}")
                                    return wechat_value

                    # 然后检查缓存属性
                    for cache_attr_name, cache_attr_value in cache_attrs.items():
                        cache_value_str = str(cache_attr_value).lower()

                        # 检查每个优先级选项的关键词
                        for wechat_value, keywords in priority_mapping.items():
                            for keyword in keywords:
                                if keyword.lower() in cache_value_str:
                                    print(f"[微信小店上传] 配置智能匹配: 从'{cache_attr_name}': {cache_attr_value} 中的关键词'{keyword}' 匹配到'{target_attr}': {wechat_value}")
                                    return wechat_value

                    # 如果没有匹配到，使用fallback
                    if 'fallback' in attr_config:
                        fallback_value = attr_config['fallback']
                        print(f"[微信小店上传] 配置智能匹配: 使用fallback值 '{target_attr}': {fallback_value}")
                        return fallback_value

            return ""

        except Exception as e:
            print(f"[微信小店上传] 智能匹配属性失败: {e}")
            return ""

    def _log_attribute_mismatch(self, attr_name: str, original_value: str, final_value: str, match_type: str):
        """记录属性匹配情况，用于自动更新配置"""
        try:
            validation_config = self.attr_mapping_config.get('validation_config', {})

            if validation_config.get('log_mismatches', True):
                print(f"[属性匹配日志] {attr_name}: '{original_value}' -> '{final_value}' ({match_type})")

            # 如果启用自动更新配置
            if validation_config.get('auto_update_mapping', True):
                self._record_mapping_update(attr_name, original_value, final_value, match_type)

        except Exception as e:
            print(f"[微信小店上传] 记录属性匹配日志失败: {e}")

    def _record_mapping_update(self, attr_name: str, original_value: str, final_value: str, match_type: str):
        """记录需要更新的属性映射"""
        try:
            # 记录需要改进的匹配更新
            if match_type in ['fallback_used', 'first_option_used', 'manual_match', 'default_value', 'smart_match']:
                if attr_name not in self.attr_mapping_updates:
                    self.attr_mapping_updates[attr_name] = {
                        'priority_mapping': {},
                        'suggested_updates': []
                    }

                # 记录建议的映射更新
                if original_value and final_value and original_value != final_value:
                    suggestion = {
                        'original_value': original_value,
                        'final_value': final_value,
                        'match_type': match_type,
                        'frequency': 1
                    }

                    # 检查是否已存在相同的建议
                    existing = None
                    for item in self.attr_mapping_updates[attr_name]['suggested_updates']:
                        if item['original_value'] == original_value and item['final_value'] == final_value:
                            existing = item
                            break

                    if existing:
                        existing['frequency'] += 1
                    else:
                        self.attr_mapping_updates[attr_name]['suggested_updates'].append(suggestion)

        except Exception as e:
            print(f"[微信小店上传] 记录映射更新失败: {e}")

    def _save_mapping_updates(self):
        """保存属性映射更新到配置文件"""
        try:
            validation_config = self.attr_mapping_config.get('validation_config', {})

            if not validation_config.get('auto_update_mapping', True):
                return

            if not self.attr_mapping_updates:
                return

            print(f"[微信小店上传] 发现 {len(self.attr_mapping_updates)} 个属性需要更新映射")

            # 生成更新建议报告
            update_report = {
                'timestamp': datetime.now().isoformat(),
                'suggested_updates': self.attr_mapping_updates
            }

            # 保存到单独的更新建议文件
            report_path = "config/属性映射更新建议.json"
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(update_report, f, ensure_ascii=False, indent=2)

            print(f"[微信小店上传] 属性映射更新建议已保存到: {report_path}")

            # 如果频次足够高，自动更新配置文件
            self._auto_update_config_if_needed()

        except Exception as e:
            print(f"[微信小店上传] 保存映射更新失败: {e}")

    def _auto_update_config_if_needed(self):
        """如果建议频次足够高，自动更新配置文件"""
        try:
            auto_update_threshold = 3  # 出现3次以上的建议才自动更新

            for attr_name, updates in self.attr_mapping_updates.items():
                for suggestion in updates['suggested_updates']:
                    if suggestion['frequency'] >= auto_update_threshold:
                        self._apply_config_update(attr_name, suggestion)

        except Exception as e:
            print(f"[微信小店上传] 自动更新配置失败: {e}")

    def _apply_config_update(self, attr_name: str, suggestion: dict):
        """应用配置更新"""
        try:
            if 'global_attribute_mapping' not in self.attr_mapping_config:
                self.attr_mapping_config['global_attribute_mapping'] = {}

            mapping_config = self.attr_mapping_config['global_attribute_mapping']

            if attr_name not in mapping_config:
                mapping_config[attr_name] = {'priority_mapping': {}}

            if 'priority_mapping' not in mapping_config[attr_name]:
                mapping_config[attr_name]['priority_mapping'] = {}

            # 添加新的映射规则
            final_value = suggestion['final_value']
            original_value = suggestion['original_value']

            if final_value not in mapping_config[attr_name]['priority_mapping']:
                mapping_config[attr_name]['priority_mapping'][final_value] = []

            if original_value not in mapping_config[attr_name]['priority_mapping'][final_value]:
                mapping_config[attr_name]['priority_mapping'][final_value].append(original_value)
                print(f"[微信小店上传] 自动更新配置: {attr_name}.{final_value} 添加关键词 '{original_value}'")

                # 保存更新后的配置
                self._save_updated_config()

        except Exception as e:
            print(f"[微信小店上传] 应用配置更新失败: {e}")

    def _save_updated_config(self):
        """保存更新后的配置文件"""
        try:
            config_path = "config/属性智能匹配.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.attr_mapping_config, f, ensure_ascii=False, indent=2)
            print(f"[微信小店上传] 配置文件已更新: {config_path}")

        except Exception as e:
            print(f"[微信小店上传] 保存配置文件失败: {e}")

    def _add_required_attrs(self, attrs: List[Dict]) -> List[Dict]:
        """添加必填属性（如果缺少）并修正属性值格式"""
        # 检查已有的属性名
        existing_attr_keys = {attr['attr_key'] for attr in attrs}

        # 定义必填属性及其默认值
        required_attrs = {
            "面料材质": "棉",
            "面料材质成分含量（%）": "100 %"  # 格式：数字 单位
        }

        # 添加缺少的必填属性
        for attr_key, default_value in required_attrs.items():
            if attr_key not in existing_attr_keys:
                attrs.append({
                    "attr_key": attr_key,
                    "attr_value": default_value
                })
                print(f"[微信小店上传] 添加必填属性: {attr_key} = {default_value}")

        # 修正已有属性的值格式
        attrs = self._fix_attr_values(attrs)

        return attrs

    def _fix_attr_values(self, attrs: List[Dict]) -> List[Dict]:
        """修正属性值格式以符合微信API要求"""
        # 需要移除的有问题属性
        problematic_attrs = ["风格"]  # 暂时移除风格属性，因为不知道正确的选项

        # 过滤掉有问题的属性
        filtered_attrs = []
        for attr in attrs:
            attr_key = attr['attr_key']
            attr_value = attr['attr_value']

            # 跳过有问题的属性
            if attr_key in problematic_attrs:
                print(f"[微信小店上传] 跳过有问题的属性: {attr_key} = {attr_value}")
                continue

            # 特殊处理百分比格式
            if "%" in attr_key and "%" in attr_value:
                # 将 "其他100%" 格式转换为 "100 %" 格式
                import re
                match = re.search(r'(\d+(?:\.\d+)?)%?', attr_value)
                if match:
                    number = match.group(1)
                    attr['attr_value'] = f"{number} %"
                    print(f"[微信小店上传] 修正百分比格式: {attr_key} {attr_value} -> {attr['attr_value']}")

            # 修正面料材质的值
            if attr_key == "面料材质" and attr_value == "其他":
                attr['attr_value'] = "棉"
                print(f"[微信小店上传] 修正面料材质: 其他 -> 棉")

            filtered_attrs.append(attr)

        return filtered_attrs
    
    def _process_images(self, cache_data: Dict, field_mapping: Dict) -> Dict:
        """处理图片信息 - 按照模板配置处理"""
        print(f"[微信小店上传] ===== 开始处理图片信息 =====")
        print(f"[微信小店上传] cache_data keys: {list(cache_data.keys())}")
        print(f"[微信小店上传] field_mapping keys: {list(field_mapping.keys())}")
        result = {}

        # 🚀 首先检查是否有 image_info 配置
        image_info_config = field_mapping.get('image_info', {})

        # 处理主图 - 按照模板配置的 head_imgs 或 imageUrls 字段
        main_img_field = None
        if 'imageUrls' in image_info_config:
            main_img_field = 'imageUrls'
            head_img_config = image_info_config['imageUrls']
        elif 'head_imgs' in image_info_config:
            main_img_field = 'head_imgs'
            head_img_config = image_info_config['head_imgs']
        elif 'imageUrls' in field_mapping:
            main_img_field = 'imageUrls'
            head_img_config = field_mapping['imageUrls']
        elif 'head_imgs' in field_mapping:
            main_img_field = 'head_imgs'
            head_img_config = field_mapping['head_imgs']

        if main_img_field:
            source_field = head_img_config.get('source_field', 'main_images')
            print(f"[快手小店上传] 使用主图字段: {main_img_field}, 源字段: {source_field}")

            # 根据source_field获取图片数据
            if source_field == 'main_images':
                # 从 product_image.images 获取
                product_image_data = cache_data.get('product_image', {})
                if isinstance(product_image_data, dict):
                    main_images = product_image_data.get('images', [])
                else:
                    main_images = product_image_data if isinstance(product_image_data, list) else []
            elif source_field == 'product_image.images':
                # 从 product_info.product_image.images 获取
                print(f"[微信小店上传] 调试 - 缓存数据顶层键: {list(cache_data.keys())}")

                # 检查是否有 product_info 层级
                if 'product_info' in cache_data:
                    product_info = cache_data['product_info']
                    product_image_data = product_info.get('product_image', {})
                    print(f"[微信小店上传] 调试 - 从 product_info.product_image 获取")
                else:
                    # 直接从顶层获取
                    product_image_data = cache_data.get('product_image', {})
                    print(f"[微信小店上传] 调试 - 从顶层 product_image 获取")

                print(f"[微信小店上传] 调试 - product_image 数据类型: {type(product_image_data)}")
                print(f"[微信小店上传] 调试 - product_image 内容: {product_image_data}")

                if isinstance(product_image_data, dict):
                    main_images = product_image_data.get('images', [])
                    print(f"[微信小店上传] 调试 - images 字段内容: {main_images}")
                else:
                    main_images = []
                    print(f"[微信小店上传] 调试 - product_image 不是字典类型")
                print(f"[微信小店上传] 从 product_image.images 获取到 {len(main_images)} 张主图")
            else:
                # 直接从指定字段获取
                main_images = cache_data.get(source_field, [])

            if isinstance(main_images, str):
                main_images = [main_images]

            # 处理主图上传
            if main_images:
                # 应用搬家设置中的主图过滤
                filtered_main_images = self._filter_images(main_images, 'main_image')

                if not filtered_main_images:
                    # 检查是否设置了主图过滤参数
                    main_filter_settings = self.image_filter_settings.get('main_image', {})
                    delete_before = main_filter_settings.get('delete_before', 0)
                    delete_after = main_filter_settings.get('delete_after', 0)

                    if delete_before > 0 or delete_after > 0:
                        print(f"[微信小店上传] 主图过滤后为空，按用户设置跳过主图上传")
                        uploaded_main_imgs = []
                    else:
                        print(f"[微信小店上传] 主图过滤后为空且未设置过滤，使用原始图片")
                        filtered_main_images = main_images
                        max_count = head_img_config.get('max_count', 9)
                        uploaded_main_imgs = self._upload_images_batch(filtered_main_images[:max_count], upload_type=1)
                else:
                    max_count = head_img_config.get('max_count', 9)
                    print(f"[快手小店上传] 开始处理 {len(filtered_main_images)} 张主图（过滤后）")
                    try:
                        uploaded_main_imgs = self._upload_images_batch(filtered_main_images[:max_count], upload_type=1)
                    except Exception as e:
                        # 快手API错误处理
                        print(f"[快手小店上传] 主图上传异常: {e}")
                        raise e

                # 快手小店要求最少1张主图，最多9张
                min_count = 1
                if uploaded_main_imgs and len(uploaded_main_imgs) >= min_count:
                    # 根据使用的字段名设置结果
                    if main_img_field == 'imageUrls':
                        result['imageUrls'] = uploaded_main_imgs  # 快手API使用 imageUrls 字段
                        print(f"[快手小店上传] 主图上传完成，成功 {len(uploaded_main_imgs)} 张")
                        print(f"[快手小店上传] imageUrls: {uploaded_main_imgs}")
                    else:
                        result['head_imgs'] = uploaded_main_imgs  # 微信API使用 head_imgs 字段
                        print(f"[微信小店上传] 主图上传完成，成功 {len(uploaded_main_imgs)} 张")
                else:
                    # 如果图片上传失败，抛出异常停止处理
                    raise Exception(f"主图上传失败，快手小店要求至少 {min_count} 张主图，实际成功 {len(uploaded_main_imgs) if uploaded_main_imgs else 0} 张")
            else:
                # 如果没有主图，抛出异常
                raise Exception("商品缺少主图，无法提交到快手小店")
        else:
            # 如果模板中没有配置主图字段，使用默认处理
            result['imageUrls'] = []
            print("[快手小店上传] 模板中未配置主图字段（head_imgs 或 imageUrls）")

        # 🚀 处理详情图 - 按照模板配置处理
        detail_img_field = None
        detail_img_config = None

        # 检查模板配置中的详情图字段
        if 'detailImageUrls' in image_info_config:
            detail_img_field = 'detailImageUrls'
            detail_img_config = image_info_config['detailImageUrls']
        elif 'detailImageUrls' in field_mapping:
            detail_img_field = 'detailImageUrls'
            detail_img_config = field_mapping['detailImageUrls']

        if detail_img_field and detail_img_config:
            # 🚀 按照模板配置处理详情图
            source_field = detail_img_config.get('source_field', 'detail_images')
            print(f"[快手小店上传] 🔍 按模板配置处理详情图字段: {detail_img_field}, 源字段: {source_field}")

            # 根据source_field获取详情图数据
            detail_images = []
            if source_field == 'detail_images':
                # 从缓存的 detail_images 字段获取
                detail_images = cache_data.get('detail_images', [])
                print(f"[快手小店上传] 🔍 从缓存字段 {source_field} 获取到 {len(detail_images)} 张详情图")
            else:
                # 从其他字段获取
                detail_images = cache_data.get(source_field, [])
                print(f"[快手小店上传] 🔍 从缓存字段 {source_field} 获取到 {len(detail_images)} 张详情图")

            if isinstance(detail_images, str):
                detail_images = [detail_images]

            if detail_images:
                # 🚀 应用图片过滤设置
                print(f"[快手小店上传] 🔍 对详情图应用过滤设置，原始数量: {len(detail_images)}")
                filtered_detail_images = self._filter_images(detail_images, 'detail_image')
                print(f"[快手小店上传] 🔍 详情图过滤后数量: {len(filtered_detail_images)}")

                if filtered_detail_images:
                    # 上传详情图
                    max_count = detail_img_config.get('max_count', 50)
                    upload_type = detail_img_config.get('upload_type', 2)
                    print(f"[快手小店上传] 🔍 开始上传 {len(filtered_detail_images)} 张详情图（过滤后），最大数量: {max_count}")

                    uploaded_detail_imgs = self._upload_images_batch(filtered_detail_images[:max_count], upload_type=upload_type)
                    successful_imgs = [url for url in uploaded_detail_imgs if url]

                    result['detailImageUrls'] = successful_imgs
                    print(f"[快手小店上传] ✅ 详情图按模板配置处理完成: {len(successful_imgs)} 张")
                    print(f"[快手小店上传] detailImageUrls: {successful_imgs}")
                else:
                    # 过滤后为空
                    detail_filter_settings = self.image_filter_settings.get('detail_image', {})
                    delete_before = detail_filter_settings.get('delete_before', 0)
                    delete_after = detail_filter_settings.get('delete_after', 0)

                    if delete_before > 0 or delete_after > 0:
                        print(f"[快手小店上传] ⚠️ 详情图过滤后为空（删除前{delete_before}张，删除后{delete_after}张），使用空详情图")
                        result['detailImageUrls'] = []
                    else:
                        print(f"[快手小店上传] ⚠️ 详情图过滤后为空但未设置过滤，使用原始图片")
                        max_count = detail_img_config.get('max_count', 50)
                        upload_type = detail_img_config.get('upload_type', 2)
                        uploaded_detail_imgs = self._upload_images_batch(detail_images[:max_count], upload_type=upload_type)
                        successful_imgs = [url for url in uploaded_detail_imgs if url]
                        result['detailImageUrls'] = successful_imgs
                        print(f"[快手小店上传] detailImageUrls: {successful_imgs}")
            else:
                print(f"[快手小店上传] ⚠️ 从 {source_field} 字段未获取到详情图，尝试备用方案")
                # 如果按模板配置没有获取到详情图，继续使用备用方案
                result['detailImageUrls'] = []
        else:
            print(f"[快手小店上传] ⚠️ 模板配置中未找到 detailImageUrls 字段，使用备用方案")

        # 🚀 备用方案：如果按模板配置没有处理成功，使用原有的HTML提取逻辑
        if 'detailImageUrls' not in result:
            print(f"[微信小店上传] 开始备用方案：从HTML描述中提取详情图")

        # 从正确的路径获取详情HTML
        description_html = ''
        if 'product_info' in cache_data:
            description_html = cache_data['product_info'].get('description', '')
        else:
            description_html = cache_data.get('description', '')

        print(f"[微信小店上传] 详情HTML长度: {len(description_html)}")
        print(f"[微信小店上传] 详情HTML前200字符: {description_html[:200]}")
        detail_images = []

        if description_html:
            # 先过滤掉广告图片
            filtered_description_html = self._filter_description_images(description_html)
            print(f"[微信小店上传] 过滤广告图片后HTML长度: {len(filtered_description_html)}")

            # 从过滤后的HTML中提取所有图片链接 - 使用更简单的正则表达式
            import re
            img_pattern = r'src=["\']([^"\']+)["\']'
            img_matches = re.findall(img_pattern, filtered_description_html)
            print(f"[微信小店上传] 正则匹配到 {len(img_matches)} 个src属性")

            # 过滤出有效的图片链接
            for img_url in img_matches:
                img_url = img_url.strip()
                print(f"[微信小店上传] 检查图片URL: {img_url[:100]}...")
                if img_url and ('alicdn.com' in img_url or 'alibaba.com' in img_url):
                    # 清理URL参数
                    if '?' in img_url:
                        img_url = img_url.split('?')[0]
                    detail_images.append(img_url)
                    print(f"[微信小店上传] 添加有效图片: {img_url}")

            print(f"[微信小店上传] 从详情描述中提取到 {len(detail_images)} 张有效图片")

        if detail_images:
            # 应用搬家设置中的详情图过滤
            filtered_detail_images = self._filter_images(detail_images, 'detail_image')

            if not filtered_detail_images:
                # 检查是否设置了详情图过滤参数
                detail_filter_settings = self.image_filter_settings.get('detail_image', {})
                delete_before = detail_filter_settings.get('delete_before', 0)
                delete_after = detail_filter_settings.get('delete_after', 0)

                if delete_before > 0 or delete_after > 0:
                    print(f"[微信小店上传] 详情图过滤后为空，按用户设置跳过详情图上传")
                    filtered_detail_images = []
                else:
                    print(f"[微信小店上传] 详情图过滤后为空且未设置过滤，使用原始图片")
                    filtered_detail_images = detail_images

            if filtered_detail_images:
                print(f"[微信小店上传] 开始处理 {len(filtered_detail_images)} 张详情图（过滤后）")
                try:
                    uploaded_detail_imgs = self._upload_images_batch(filtered_detail_images[:50], upload_type=2)  # 最多50张详情图
                    print(f"[微信小店上传] 上传结果: {uploaded_detail_imgs}")
                except Exception as e:
                    # 快手API错误处理
                    print(f"[快手小店上传] 详情图上传异常: {e}")
                    raise e
            else:
                print(f"[微信小店上传] 详情图过滤后为空，跳过上传")
                uploaded_detail_imgs = []

            if uploaded_detail_imgs:
                # 替换HTML中的图片链接为微信链接
                processed_description = description_html
                successful_uploads = 0

                for original_url, wechat_url in zip(filtered_detail_images[:20], uploaded_detail_imgs):
                    if wechat_url:
                        processed_description = processed_description.replace(original_url, wechat_url)
                        successful_uploads += 1
                        print(f"[微信小店上传] 详情图上传成功: {wechat_url}")
                    else:
                        print(f"[微信小店上传] 详情图上传失败: {original_url}")

                # 只保留上传成功的图片
                successful_imgs = [url for url in uploaded_detail_imgs if url]

                result['detailImageUrls'] = successful_imgs  # 快手API使用 detailImageUrls 字段
                print(f"[快手小店上传] 详情图处理完成，成功 {successful_uploads}/{len(filtered_detail_images)} 张")
                print(f"[快手小店上传] 最终detailImageUrls数组: {successful_imgs}")
            else:
                # 如果没有上传成功的详情图，使用空数组
                result['detailImageUrls'] = []
                print(f"[快手小店上传] 详情图上传为空，使用空的detailImageUrls")
        else:
            print("[快手小店上传] 没有找到有效的详情图片，尝试使用主图作为详情图")
            # 如果没有详情图，使用已经上传的主图作为详情图
            if 'imageUrls' in result and result['imageUrls']:
                # 🚀 对主图也应用详情图过滤设置
                main_images_for_detail = result['imageUrls']
                print(f"[快手小店上传] 🔍 对主图应用详情图过滤设置，原始数量: {len(main_images_for_detail)}")

                # 应用详情图过滤设置
                filtered_main_images = self._filter_images(main_images_for_detail, 'detail_image')
                print(f"[快手小店上传] 🔍 主图过滤后数量: {len(filtered_main_images)}")

                if filtered_main_images:
                    # 使用过滤后的主图作为详情图
                    successful_imgs = filtered_main_images[:3]  # 最多使用前3张
                    result['detailImageUrls'] = successful_imgs
                    print(f"[快手小店上传] ✅ 使用过滤后的主图作为详情图: {len(successful_imgs)} 张")
                    print(f"[快手小店上传] 详情图URLs: {successful_imgs}")
                else:
                    # 如果过滤后为空，检查是否设置了过滤参数
                    detail_filter_settings = self.image_filter_settings.get('detail_image', {})
                    delete_before = detail_filter_settings.get('delete_before', 0)
                    delete_after = detail_filter_settings.get('delete_after', 0)

                    if delete_before > 0 or delete_after > 0:
                        print(f"[快手小店上传] ⚠️ 主图过滤后为空（删除前{delete_before}张，删除后{delete_after}张），使用空详情图")
                        result['detailImageUrls'] = []
                    else:
                        print(f"[快手小店上传] ⚠️ 主图过滤后为空但未设置过滤，使用原始主图")
                        successful_imgs = main_images_for_detail[:3]
                        result['detailImageUrls'] = successful_imgs
                        print(f"[快手小店上传] 详情图URLs: {successful_imgs}")
            else:
                # 如果连主图都没有，这是异常情况
                result['detailImageUrls'] = []
                print(f"[快手小店上传] 警告：没有找到任何图片作为详情图！")

        return result

    def _process_desc_info(self, cache_data: Dict) -> Dict:
        """处理详情信息 - 从HTML中提取图片并上传"""
        try:
            # 获取详情HTML
            description_html = cache_data.get('description', '')
            print(f"[微信小店上传] 详情HTML长度: {len(description_html)}")

            if not description_html:
                print("[微信小店上传] 没有找到详情描述")
                return {
                    'desc': '',
                    'imgs': []
                }

            # 先过滤掉广告图片
            filtered_description_html = self._filter_description_images(description_html)
            print(f"[微信小店上传] 过滤广告图片后HTML长度: {len(filtered_description_html)}")

            # 从过滤后的HTML中提取所有图片链接
            import re
            img_pattern = r'src=["\']([^"\']+)["\']'
            img_matches = re.findall(img_pattern, filtered_description_html)
            print(f"[微信小店上传] 正则匹配到 {len(img_matches)} 个src属性")

            # 过滤出有效的图片链接
            detail_images = []
            for img_url in img_matches:
                img_url = img_url.strip()
                print(f"[微信小店上传] 检查图片URL: {img_url[:100]}...")
                if img_url and ('alicdn.com' in img_url or 'alibaba.com' in img_url):
                    # 清理URL参数
                    if '?' in img_url:
                        img_url = img_url.split('?')[0]
                    detail_images.append(img_url)
                    print(f"[微信小店上传] 添加有效图片: {img_url}")

            print(f"[微信小店上传] 从详情描述中提取到 {len(detail_images)} 张有效图片")

            if detail_images:
                # 应用搬家设置中的详情图过滤
                filtered_detail_images = self._filter_images(detail_images, 'detail_image')

                if not filtered_detail_images:
                    # 检查是否设置了详情图过滤参数
                    detail_filter_settings = self.image_filter_settings.get('detail_image', {})
                    delete_before = detail_filter_settings.get('delete_before', 0)
                    delete_after = detail_filter_settings.get('delete_after', 0)

                    if delete_before > 0 or delete_after > 0:
                        print(f"[微信小店上传] 详情图过滤后为空，按用户设置跳过详情图上传")
                        filtered_detail_images = []
                    else:
                        print(f"[微信小店上传] 详情图过滤后为空且未设置过滤，使用原始图片")
                        filtered_detail_images = detail_images

                if filtered_detail_images:
                    # 上传详情图片（最多20张）
                    print(f"[微信小店上传] 开始上传 {len(filtered_detail_images[:20])} 张详情图（过滤后）")
                    uploaded_detail_imgs = self._upload_images_batch(filtered_detail_images[:50], upload_type=2)
                    print(f"[微信小店上传] 上传结果: {uploaded_detail_imgs}")
                else:
                    print(f"[微信小店上传] 详情图过滤后为空，跳过上传")
                    uploaded_detail_imgs = []

                # 替换HTML中的图片链接为微信链接
                processed_description = description_html
                successful_uploads = 0

                for original_url, wechat_url in zip(detail_images[:20], uploaded_detail_imgs):
                    if wechat_url:
                        processed_description = processed_description.replace(original_url, wechat_url)
                        successful_uploads += 1
                        print(f"[微信小店上传] 详情图上传成功: {wechat_url}")
                    else:
                        print(f"[微信小店上传] 详情图上传失败: {original_url}")

                # 只保留上传成功的图片
                successful_imgs = [url for url in uploaded_detail_imgs if url]

                print(f"[微信小店上传] 详情图处理完成，成功 {successful_uploads}/{len(detail_images[:20])} 张")
                print(f"[微信小店上传] 最终imgs数组: {successful_imgs}")

                return {
                    'desc': processed_description,
                    'imgs': successful_imgs
                }
            else:
                print("[微信小店上传] 没有找到有效的详情图片，尝试使用主图作为详情图")
                # 如果没有详情图，尝试使用主图
                main_images = cache_data.get('product_image', {}).get('images', [])
                if main_images:
                    # 应用主图过滤设置
                    filtered_main_images = self._filter_images(main_images, 'main_image')
                    if filtered_main_images:
                        print(f"[微信小店上传] 使用主图作为详情图: {len(filtered_main_images)} 张（过滤后）")
                        uploaded_main_imgs = self._upload_images_batch(filtered_main_images[:5])  # 最多5张主图作为详情图
                        successful_imgs = [url for url in uploaded_main_imgs if url]
                    else:
                        print(f"[微信小店上传] 主图过滤后为空，无法作为详情图")
                        successful_imgs = []

                    return {
                        'desc': description_html or "商品详情",
                        'imgs': successful_imgs
                    }
                else:
                    return {
                        'desc': description_html or "商品详情",
                        'imgs': []
                    }

        except Exception as e:
            print(f"[微信小店上传] 处理详情信息失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'desc': cache_data.get('description', ''),
                'imgs': []
            }

    def _get_freight_template_id(self) -> str:
        """获取运费模板ID"""
        try:
            # 尝试从外部传入的运费模板ID获取
            if hasattr(self, 'freight_template_id') and self.freight_template_id:
                print(f"[微信小店上传] 使用传入的运费模板ID: {self.freight_template_id}")
                return str(self.freight_template_id)

            # 默认运费模板ID
            print(f"[微信小店上传] 使用默认运费模板ID: 1")
            return "1"

        except Exception as e:
            print(f"[微信小店上传] 获取运费模板ID失败: {e}")
            return "1"

    def set_freight_template_id(self, template_id: str):
        """设置运费模板ID"""
        self.freight_template_id = template_id
        print(f"[微信小店上传] 设置运费模板ID: {template_id}")

    def _get_return_address_id(self) -> int:
        """获取退货地址ID"""
        try:
            # 尝试从外部传入的退货地址ID获取
            if hasattr(self, 'return_address_id') and self.return_address_id:
                print(f"[微信小店上传] 使用传入的退货地址ID: {self.return_address_id}")
                return int(self.return_address_id)

            # 默认退货地址ID
            print(f"[微信小店上传] 使用默认退货地址ID: 1")
            return 1

        except Exception as e:
            print(f"[微信小店上传] 获取退货地址ID失败: {e}")
            return 1

    def set_return_address_id(self, address_id: str):
        """设置退货地址ID"""
        self.return_address_id = address_id
        print(f"[微信小店上传] 设置退货地址ID: {address_id}")

    def set_shelf_status(self, status: int):
        """设置上架状态"""
        self.shelf_status = status
        print(f"[微信小店上传] 设置上架状态: {status}")

    def _get_shelf_status(self) -> int:
        """获取上架状态"""
        try:
            if hasattr(self, 'shelf_status'):
                print(f"[微信小店上传] 使用设置的上架状态: {self.shelf_status}")
                return self.shelf_status

            # 默认立即上架
            print(f"[微信小店上传] 使用默认上架状态: 1 (立即上架)")
            return 1

        except Exception as e:
            print(f"[微信小店上传] 获取上架状态失败: {e}")
            return 1

    def set_image_filter_settings(self, settings: Dict):
        """设置图片过滤设置"""
        self.image_filter_settings = settings
        main_settings = settings.get('main_image', {})
        detail_settings = settings.get('detail_image', {})
        print(f"[微信小店上传] 设置主图过滤: 删除前{main_settings.get('delete_before', 0)}张, 删除后{main_settings.get('delete_after', 0)}张")
        print(f"[微信小店上传] 设置详情图过滤: 删除前{detail_settings.get('delete_before', 0)}张, 删除后{detail_settings.get('delete_after', 0)}张")

    def set_skip_copy_settings(self, settings: Dict):
        """设置跳过复制设置"""
        self.skip_copy_settings = settings
        print(f"[微信小店上传] 设置跳过复制: 启用={settings.get('enabled', False)}, 关键词数量={len(settings.get('keywords', []))}")

    def should_skip_product(self, title: str) -> tuple:
        """检查商品是否应该跳过上传

        Returns:
            tuple: (是否跳过, 命中的关键词)
        """
        try:
            # 如果跳过复制功能未启用，不跳过
            if not self.skip_copy_settings.get('enabled', False):
                return False, None

            # 如果没有跳过关键词，不跳过
            skip_keywords = self.skip_copy_settings.get('keywords', [])
            if not skip_keywords:
                return False, None

            # 检查标题是否包含跳过关键词
            title_lower = title.lower()
            for keyword in skip_keywords:
                if keyword and keyword.lower() in title_lower:
                    print(f"[微信小店上传] 商品标题包含跳过关键词 '{keyword}': {title}")
                    return True, keyword

            return False, None

        except Exception as e:
            print(f"[微信小店上传] 检查跳过商品异常: {e}")
            return False, None

    def _filter_images(self, images: List[str], image_type: str = 'main_image') -> List[str]:
        """根据搬家设置过滤图片"""
        print(f"[🔍 图片过滤调试] _filter_images方法被调用！")
        print(f"[🔍 图片过滤调试] 参数: image_type={image_type}, 图片数量={len(images) if images else 0}")
        print(f"[🔍 图片过滤调试] 当前过滤设置: {self.image_filter_settings}")

        if not images:
            print(f"[🔍 图片过滤调试] 图片列表为空，直接返回")
            return images

        # 根据图片类型获取对应的过滤设置
        filter_settings = self.image_filter_settings.get(image_type, {})
        delete_before = filter_settings.get('delete_before', 0)
        delete_after = filter_settings.get('delete_after', 0)

        image_type_name = "主图" if image_type == 'main_image' else "详情图"
        print(f"[微信小店上传] {image_type_name}过滤前: {len(images)} 张")
        print(f"[微信小店上传] {image_type_name}过滤设置: 删除前{delete_before}张, 删除后{delete_after}张")

        # 应用过滤规则
        filtered_images = images[:]  # 复制列表

        # 删除前N张
        if delete_before > 0:
            if delete_before >= len(filtered_images):
                # 如果要删除的数量大于等于总数量，清空列表
                filtered_images = []
                print(f"[微信小店上传] {image_type_name}删除前{delete_before}张，原有{len(images)}张，全部删除，剩余: 0 张")
            else:
                filtered_images = filtered_images[delete_before:]
                print(f"[微信小店上传] {image_type_name}删除前{delete_before}张后剩余: {len(filtered_images)} 张")

        # 删除后N张
        if delete_after > 0 and len(filtered_images) > 0:
            if delete_after >= len(filtered_images):
                # 如果要删除的数量大于等于剩余数量，清空列表
                filtered_images = []
                print(f"[微信小店上传] {image_type_name}删除后{delete_after}张，剩余全部删除，最终剩余: 0 张")
            else:
                filtered_images = filtered_images[:-delete_after]
                print(f"[微信小店上传] {image_type_name}删除后{delete_after}张后剩余: {len(filtered_images)} 张")

        print(f"[微信小店上传] {image_type_name}过滤后: {len(filtered_images)} 张")

        # 输出过滤详情（仅显示前几张图片的URL用于调试）
        if len(images) != len(filtered_images):
            print(f"[微信小店上传] 过滤详情:")
            print(f"  原始图片: {[img[:50] + '...' for img in images[:3]]}{'...' if len(images) > 3 else ''}")
            print(f"  过滤后: {[img[:50] + '...' for img in filtered_images[:3]]}{'...' if len(filtered_images) > 3 else ''}")

        return filtered_images

    def _process_skus(self, cache_data: Dict, field_mapping: Dict) -> List[Dict]:
        """处理SKU信息 - 从缓存的 product_sku_infos 获取"""
        # 从缓存获取SKU信息 - 检查 product_info 层级
        if 'product_info' in cache_data:
            original_skus = cache_data['product_info'].get('product_sku_infos', [])
            print(f"[微信小店上传] 从 product_info.product_sku_infos 获取SKU")
        else:
            original_skus = cache_data.get('product_sku_infos', [])
            print(f"[微信小店上传] 从顶层 product_sku_infos 获取SKU")
        if not original_skus:
            # 如果没有SKU，创建默认SKU
            original_skus = [self._create_default_sku(cache_data)]
            print(f"[微信小店上传] 没有找到SKU信息，创建默认SKU")
        else:
            print(f"[微信小店上传] 找到 {len(original_skus)} 个SKU")

        processed_skus = []

        # 获取商品ID用于生成sku_code
        product_id = self._get_product_id_from_cache(cache_data)

        for i, sku in enumerate(original_skus):
            try:
                # 根据实际缓存结构获取SKU数据
                sku_id = sku.get('skuId', sku.get('sku_id', f'sku_{i}'))
                cargo_number = sku.get('cargoNumber', '')
                price = sku.get('consignPrice', sku.get('price', 0))
                stock = sku.get('amountOnSale', sku.get('stock', 999))

                # 生成sku_code：B_商品ID格式
                sku_code = f"B_{product_id}" if product_id else f"B_sku_{i}"

                processed_sku = {
                    'relSkuId': int(product_id) if product_id else int(sku_id),  # 使用1688商品ID作为relSkuId（Long类型）
                    'skuSalePrice': int(float(price) * 100),  # 快手使用 skuSalePrice，转换为分
                    'skuStock': int(stock),  # 快手使用 skuStock
                    'skuNick': sku_code  # 快手使用 skuNick
                }

                print(f"[微信小店上传] SKU {i+1} 设置sku_code: {sku_code}")

                # 处理SKU图片 - 从attributes中获取skuImageUrl
                sku_attrs = sku.get('attributes', [])
                sku_image_url = None
                uploaded_sku_img = None  # 保存上传后的快手URL

                for attr in sku_attrs:
                    if 'skuImageUrl' in attr and attr['skuImageUrl']:
                        sku_image_url = attr['skuImageUrl']
                        break

                if sku_image_url:
                    uploaded_sku_img = self._upload_single_image(sku_image_url)
                    if uploaded_sku_img:
                        processed_sku['thumb_img'] = uploaded_sku_img
                        # 将上传的快手图片URL保存到attributes中，供后续使用
                        for attr in sku_attrs:
                            if 'skuImageUrl' in attr and attr['skuImageUrl'] == sku_image_url:
                                attr['uploadedSkuImageUrl'] = uploaded_sku_img
                                print(f"[快手小店上传] 保存已上传的SKU图片URL: {uploaded_sku_img}")
                                break

                # 处理SKU属性 - 转换为快手格式
                converted_props = []
                print(f"[快手小店上传] SKU {i+1} 属性调试: sku_attrs = {sku_attrs}")
                if sku_attrs:
                    for j, attr in enumerate(sku_attrs):
                        print(f"[快手小店上传] 处理属性 {j+1}: {attr}")
                        if 'attributeName' in attr and 'attributeValue' in attr:
                            prop_data = {
                                'propName': attr['attributeName'],
                                'propValueName': attr['attributeValue'],
                                'isMainProp': 1 if j == 0 else 0,  # 第一个属性设为主属性
                                'propVersion': 1,
                                'propSortNum': j + 1,  # 属性排序值
                                'propValueSortNum': i + 1  # 属性值排序值
                            }
                            # 为主属性添加图片URL - 使用上传后的快手URL
                            if j == 0:
                                if uploaded_sku_img:
                                    # 优先使用已上传的快手图片URL
                                    prop_data['imageUrl'] = uploaded_sku_img
                                    print(f"[快手小店上传] 使用已上传的SKU图片: {uploaded_sku_img}")
                                elif hasattr(self, 'current_product_images') and self.current_product_images:
                                    # 备选：使用主图
                                    prop_data['imageUrl'] = self.current_product_images[0]
                                    print(f"[快手小店上传] 使用主图作为SKU图片: {self.current_product_images[0]}")

                            converted_props.append(prop_data)
                            print(f"[快手小店上传] 添加属性: {attr['attributeName']} = {attr['attributeValue']}")
                else:
                    print(f"[快手小店上传] SKU {i+1} 没有找到attributes属性")

                # 如果没有SKU属性，添加默认属性（快手要求至少有一个规格）
                if not converted_props:
                    # 根据SKU ID生成默认规格值
                    default_spec = f"规格{i+1}"
                    # 为主属性添加图片URL（使用商品主图）
                    image_url = ""
                    if hasattr(self, 'current_product_images') and self.current_product_images:
                        image_url = self.current_product_images[0]  # 使用第一张主图

                    converted_props.append({
                        'propName': '规格',
                        'propValueName': default_spec,
                        'imageUrl': image_url,  # 添加图片URL
                        'isMainProp': 1,  # 1是主属性，0不是
                        'propVersion': 1,  # 自定义属性版本为1
                        'propSortNum': 1,  # 属性排序值
                        'propValueSortNum': i + 1  # 属性值排序值
                    })

                processed_sku['skuProps'] = converted_props

                # 保存原始的attributes信息，包含uploadedSkuImageUrl
                processed_sku['attributes'] = sku_attrs

                processed_skus.append(processed_sku)
                print(f"[快手小店上传] 处理SKU {i+1}: {processed_sku['relSkuId']}, 价格: {processed_sku['skuSalePrice']/100}元, 库存: {processed_sku['skuStock']}")

            except Exception as e:
                print(f"[微信小店上传] 处理SKU {i+1} 失败: {e}")
                continue

        if not processed_skus:
            # 如果所有SKU都处理失败，创建默认SKU
            # 优先使用计算后的价格
            default_price = 0

            # 尝试从 product_info.reference_price_calculated 获取计算后的价格
            if 'product_info' in cache_data and 'reference_price_calculated' in cache_data['product_info']:
                default_price = float(cache_data['product_info']['reference_price_calculated'])
                print(f"[微信小店上传] 使用计算后的价格创建默认SKU: {default_price}")
            # 如果没有计算后的价格，尝试从 product_info.reference_price 获取
            elif 'product_info' in cache_data and 'reference_price' in cache_data['product_info']:
                default_price = float(cache_data['product_info']['reference_price'])
                print(f"[微信小店上传] 使用reference_price创建默认SKU: {default_price}")
            # 最后尝试从顶层price字段获取
            else:
                default_price = float(cache_data.get('price', 0))
                print(f"[微信小店上传] 使用顶层price创建默认SKU: {default_price}")

            # 获取商品ID用于默认SKU
            product_id = cache_data.get('product_id', '')
            default_sku = {
                'relSkuId': int(product_id) if product_id else 999999,  # 使用1688商品ID作为relSkuId（Long类型）
                'skuSalePrice': int(default_price * 100),  # 快手使用 skuSalePrice
                'skuStock': 999,  # 快手使用 skuStock
                'skuNick': ''  # 快手使用 skuNick
            }
            processed_skus.append(default_sku)
            print(f"[快手小店上传] 创建默认SKU，价格: {default_price}元")

        return processed_skus

    def _upload_images_batch(self, image_urls: List[str], upload_type: int = 1, max_workers: int = 10) -> List[str]:
        """批量上传图片到快手 - 并发上传提高效率"""
        if not image_urls:
            return []

        uploaded_urls = []
        print(f"[快手小店上传] 开始并发上传 {len(image_urls)} 张图片，类型: {upload_type}，并发数: {max_workers}")

        # 使用线程池并发上传，但控制并发数避免触发频率限制
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_url = {
                executor.submit(self._upload_single_image, url, upload_type): url
                for url in image_urls if url
            }

            for future in as_completed(future_to_url):
                original_url = future_to_url[future]
                try:
                    uploaded_url = future.result()
                    if uploaded_url:
                        uploaded_urls.append(uploaded_url)
                        print(f"[快手小店上传] 图片上传成功: {len(uploaded_urls)}/{len(image_urls)}")
                    else:
                        logger.warning(f"图片上传失败: {original_url}")
                        print(f"[快手小店上传] 图片上传失败: {original_url}")
                except Exception as e:
                    logger.error(f"图片上传异常: {original_url}, 错误: {e}")
                    print(f"[快手小店上传] 图片上传异常: {original_url}, 错误: {e}")

                # 减少延迟，但避免触发API限制
                time.sleep(0.2)  # 增加间隔避免触发限制

        print(f"[快手小店上传] 并发上传完成，成功: {len(uploaded_urls)}/{len(image_urls)} 张")
        return uploaded_urls

    def _upload_single_image(self, image_url: str, upload_type: int = 1) -> Optional[str]:
        """上传单张图片到快手"""
        if not image_url:
            return None

        # 检查缓存
        cache_key = f"{image_url}_{upload_type}"
        if cache_key in self.image_cache:
            return self.image_cache[cache_key]

        # 检查是否已经是快手图片链接
        if 'eckwai.com' in image_url or 'kwaixiaodian.com' in image_url:
            self.image_cache[cache_key] = image_url
            return image_url

        # 重试机制
        max_retries = 3
        last_error = None

        for attempt in range(max_retries):
            try:
                if not self.kuaishou_api:
                    raise Exception("快手API未初始化")

                # 调用快手图片上传接口（现在自动处理1:1）
                result = self.kuaishou_api.upload_item_image(image_url, upload_type)

                if result and result.get('success'):
                    data = result.get('data', {})
                    # 快手API返回的图片URL字段
                    uploaded_url = data.get('kwaiImgUrl') or data.get('imgUrl')
                    if uploaded_url:
                        self.image_cache[cache_key] = uploaded_url
                        logger.info(f"图片上传成功: {image_url} -> {uploaded_url}")
                        return uploaded_url
                    else:
                        logger.error(f"图片上传失败，未获取到图片URL: {result}")
                        last_error = "未获取到图片URL"
                else:
                    error_msg = result.get('message', '未知错误') if result else '接口调用失败'
                    logger.error(f"图片上传失败 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                    last_error = error_msg

                    # 如果是Invalid appId错误，检查是否需要重试
                    if "Invalid appId" in error_msg and attempt < max_retries - 1:
                        logger.info(f"检测到Invalid appId错误，等待后重试...")
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    elif attempt < max_retries - 1:
                        time.sleep(0.5)  # 其他错误等待0.5秒后重试
                        continue

            except Exception as e:
                logger.error(f"图片上传异常 (尝试 {attempt + 1}/{max_retries}): {image_url}, 错误: {e}")
                last_error = str(e)
                if attempt < max_retries - 1:
                    time.sleep(1)  # 异常情况等待1秒后重试
                    continue

        # 所有重试都失败了
        logger.error(f"图片上传最终失败: {image_url}, 最后错误: {last_error}")
        return None

    def _create_default_sku(self, cache_data: Dict) -> Dict:
        """创建默认SKU"""
        # 从正确的位置获取价格信息
        price = 0
        stock = 999

        if 'product_info' in cache_data:
            product_info = cache_data['product_info']

            # 从 product_sale_info 获取价格
            if 'product_sale_info' in product_info:
                sale_info = product_info['product_sale_info']
                price = sale_info.get('consignPrice', 0)
                stock = sale_info.get('amountOnSale', 999)

                # 如果库存为0，使用默认值999
                if stock <= 0:
                    stock = 999

            # 从 processed_sale_info 获取价格（备用）
            elif 'processed_sale_info' in product_info:
                sale_info = product_info['processed_sale_info']
                price = sale_info.get('consign_price', 0)
                stock = sale_info.get('amount_on_sale', 999)

                # 如果库存为0，使用默认值999
                if stock <= 0:
                    stock = 999

        # 生成唯一的SKU ID
        product_id = self._get_product_id_from_cache(cache_data)
        sku_id = f"sku_{product_id}" if product_id else "default_sku"

        return {
            'skuId': sku_id,
            'sku_id': sku_id,
            'consignPrice': price,
            'price': price,
            'market_price': price,
            'amountOnSale': stock,
            'stock': stock,
            'sku_code': '',
            'barcode': '',
            'attributes': []
        }

    def _get_product_id_from_cache(self, cache_data: Dict) -> str:
        """从缓存数据中获取商品ID"""
        if 'product_info' in cache_data:
            product_info = cache_data['product_info']
            return str(product_info.get('product_id', ''))
        elif 'product_id' in cache_data:
            return str(cache_data['product_id'])
        return ''

    def _convert_sku_attributes(self, attributes: List) -> List[Dict]:
        """转换SKU属性格式"""
        converted_attrs = []

        for attr in attributes:
            if isinstance(attr, dict):
                converted_attr = {
                    'attr_key': attr.get('name', ''),
                    'attr_value': attr.get('value', '')
                }
                converted_attrs.append(converted_attr)

        return converted_attrs

    def _apply_filter(self, value: str, filter_name: str) -> str:
        """应用数据过滤器"""
        processors = self.template_config.get('data_processors', {})

        if filter_name == 'remove_emoji' and filter_name in processors:
            regex_pattern = processors[filter_name]['regex']
            return re.sub(regex_pattern, '', str(value))
        elif filter_name == 'trim_spaces':
            return re.sub(r'\s+', ' ', str(value)).strip()

        return str(value)

    def _validate_product_data(self, product_data: Dict):
        """验证商品数据 - 快手API格式"""
        required_fields = ['title', 'imageUrls', 'skus']  # 快手小店使用的字段

        for field in required_fields:
            if field not in product_data or not product_data[field]:
                raise ValueError(f"缺少必填字段: {field}")

        # 验证商品标题
        title = product_data.get('title', '').strip()
        if not title or len(title) > 60:
            raise ValueError("商品标题不能为空且不能超过60个字符")

        # 验证主图数据
        if not isinstance(product_data['imageUrls'], list) or len(product_data['imageUrls']) == 0:
            raise ValueError("主图数据不能为空")

        # 验证SKU数据
        if not isinstance(product_data['skus'], list) or len(product_data['skus']) == 0:
            raise ValueError("SKU数据不能为空")

        for sku in product_data['skus']:
            if not sku.get('relSkuId') or sku.get('skuSalePrice', 0) <= 0:
                raise ValueError("SKU数据不完整")

    def upload_product_to_kuaishou(self, product_data: Dict) -> Dict:
        """上传商品到快手小店"""
        try:
            if not self.kuaishou_api:
                raise Exception("快手API未初始化")

            logger.info(f"开始上传商品到快手小店: {product_data.get('title')}")

            # 保存商品图片信息供SKU规格使用
            self.current_product_images = product_data.get('imageUrls', [])

            # 保存商品属性信息供属性匹配使用
            self.current_product_attributes = product_data.get('product_attribute', [])
            print(f"[快手小店上传] 🔍 调试 - 1688商品属性数量: {len(self.current_product_attributes)}")
            if self.current_product_attributes:
                print(f"[快手小店上传] 🔍 调试 - 1688商品属性列表:")
                for i, attr in enumerate(self.current_product_attributes[:10], 1):  # 只显示前10个
                    attr_name = attr.get('attributeName', 'N/A')
                    attr_value = attr.get('value', 'N/A')
                    print(f"  {i}. {attr_name} = {attr_value}")
                if len(self.current_product_attributes) > 10:
                    print(f"  ... 还有 {len(self.current_product_attributes) - 10} 个属性")
            else:
                print(f"[快手小店上传] ⚠️ 警告 - 1688商品没有属性数据！")

            # 转换数据格式为快手API要求的格式
            freight_template_id = product_data.get('freight_template_id', 19219058228)

            kuaishou_data = self._convert_to_kuaishou_format(product_data, freight_template_id)

            # 保存当前类目ID和商品数据供查询属性值使用（使用转换后的正确类目ID）
            self.current_category_id = kuaishou_data.get('categoryId', 10378)
            self.current_product_data = kuaishou_data  # 使用转换后的数据，包含正确的categoryId

            # 打印提交的数据用于调试
            print(f"[快手小店上传] 准备提交的商品数据:")
            print(f"  标题: {kuaishou_data.get('title', 'N/A')}")
            print(f"  外部商品ID: {kuaishou_data.get('relItemId', 'N/A')}")
            print(f"  类目ID: {kuaishou_data.get('categoryId', 'N/A')}")
            print(f"  主图数量: {len(kuaishou_data.get('imageUrls', []))}")
            print(f"  详情图数量: {len(kuaishou_data.get('detailImageUrls', []))}")
            print(f"  SKU数量: {len(kuaishou_data.get('skuList', []))}")

            # 完整的JSON数据（用于调试）
            import json
            print(f"[快手小店上传] 完整提交数据:")
            print(json.dumps(kuaishou_data, ensure_ascii=False, indent=2))

            # 调用快手创建商品接口
            result = self.kuaishou_api.create_item(kuaishou_data)

            if result and result.get('success'):
                data = result.get('data', {})
                kwai_item_id = data.get('kwaiItemId', '')
                logger.info(f"商品上传成功: {product_data.get('title')}")
                print(f"[快手小店上传] 获取到快手商品ID: {kwai_item_id}")
                return {
                    'success': True,
                    'product_id': kwai_item_id,
                    'data': data,  # 保留原始data数据
                    'message': '上传成功'
                }
            else:
                error_msg = result.get('message', '未知错误') if result else '接口调用失败'
                logger.error(f"商品上传失败: {error_msg}")
                return {
                    'success': False,
                    'message': error_msg
                }

        except Exception as e:
            logger.error(f"上传商品异常: {e}")
            return {
                'success': False,
                'message': f'上传异常: {str(e)}'
            }

    def _convert_to_kuaishou_format(self, product_data: Dict, freight_template_id: int = None) -> Dict:
        """将商品数据转换为快手API格式"""
        try:
            # 使用1688商品ID作为外部商品ID（Long类型）
            product_id = product_data.get('product_id', '')
            if product_id:
                rel_item_id = int(product_id)  # 转换为Long类型
            else:
                import random
                rel_item_id = int(f"{int(time.time())}{random.randint(1000, 9999)}")  # 生成Long类型ID
            print(f"[快手小店上传] 使用外部商品ID: {rel_item_id}")

            # 转换SKU数据
            skus = product_data.get('skus', [])
            kuaishou_skus = []

            for i, sku in enumerate(skus):
                # 确保 relSkuId 是 Long 类型
                rel_sku_id = sku.get('relSkuId', sku.get('out_sku_id', i+1))
                if isinstance(rel_sku_id, str) and rel_sku_id.isdigit():
                    rel_sku_id = int(rel_sku_id)
                elif not isinstance(rel_sku_id, int):
                    rel_sku_id = i + 1

                kuaishou_sku = {
                    'relSkuId': rel_sku_id,  # Long类型
                    'skuStock': sku.get('skuStock', sku.get('stock_num', 999)),
                    'skuSalePrice': sku.get('skuSalePrice', sku.get('sale_price', 1000)),  # 已经是分为单位
                    'skuNick': sku.get('skuNick', sku.get('sku_code', ''))
                }

                # 处理SKU属性 - 检查是否已经有转换好的属性
                existing_props = sku.get('skuProps', [])
                if existing_props:
                    print(f"[快手小店上传] SKU {i+1} 使用已转换的属性: {len(existing_props)} 个")
                    kuaishou_sku['skuProps'] = existing_props
                else:
                    # 从1688的attributes字段获取原始属性
                    sku_attrs = sku.get('attributes', [])
                    print(f"[快手小店上传] SKU {i+1} 原始属性调试: sku_attrs = {sku_attrs}")

                    if sku_attrs:
                        print(f"[快手小店上传] 找到 {len(sku_attrs)} 个原始SKU属性")
                        kuaishou_sku['skuProps'] = self._convert_sku_attrs_to_kuaishou(sku_attrs)
                    else:
                        # 如果没有SKU属性，添加默认属性（快手要求至少有一个规格）
                        default_spec = f"规格{i+1}"

                        # 为主属性添加图片URL（使用商品主图）
                        image_url = ""
                        if hasattr(self, 'current_product_images') and self.current_product_images:
                            image_url = self.current_product_images[0]  # 使用第一张主图

                        kuaishou_sku['skuProps'] = [{
                            'propName': '规格',
                            'propValueName': default_spec,
                            'imageUrl': image_url,  # 添加图片URL
                            'isMainProp': 1,  # 1是主属性，0不是
                            'propVersion': 1,  # 自定义属性版本为1
                            'propSortNum': 1,  # 属性排序值
                            'propValueSortNum': i + 1  # 属性值排序值
                        }]

                kuaishou_skus.append(kuaishou_sku)

            # 构建快手商品数据
            # 优先从cats_v2中获取叶子类目ID
            final_category_id = 10378  # 使用有效的叶子类目ID作为默认值
            cats_v2 = product_data.get('cats_v2', [])
            if cats_v2 and len(cats_v2) > 0:
                # 使用最后一个类目ID作为叶子类目
                final_category_id = int(cats_v2[-1].get('cat_id', 1003))
                print(f"[快手小店上传] 从cats_v2获取叶子类目ID: {final_category_id}")
            else:
                # 备选方案：从category_id字段获取
                final_category_id = product_data.get('category_id', 10378)
                print(f"[快手小店上传] 从category_id字段获取类目ID: {final_category_id}")

            print(f"[快手小店上传] 最终使用的类目ID: {final_category_id}")

            kuaishou_data = {
                'title': product_data.get('title', ''),
                'relItemId': rel_item_id,
                'categoryId': final_category_id,
                'imageUrls': product_data.get('imageUrls', []),
                'skuList': kuaishou_skus,
                'details': product_data.get('info', '商品详情'),
                'detailImageUrls': product_data.get('detailImageUrls', []),
                # 添加商品属性（使用销售属性API获取真实ID）
                'itemPropValues': self._get_required_item_prop_values(final_category_id, product_data),
                'serviceRule': {
                    'refundRule': '1',  # 支持7天无理由退货
                    'promiseDeliveryTime': 172800,  # 48小时发货（秒）
                    'servicePromise': {
                        'freshRotRefund': False,  # 坏了包退（当前类目不支持）
                        'brokenRefund': True,     # 破损包退（类目要求必须支持）
                        'allergyRefund': False    # 过敏包退（保守设置）
                    }
                },
                'expressTemplateId': freight_template_id if freight_template_id else 0,
                # 添加其他可能需要的字段
                'immediatelyOnOfflineFlag': 0,  # 立即上架
                'deliveryMethod': 'logistics',  # 物流配送
                'payWay': 2  # 在线支付
            }

            return kuaishou_data

        except Exception as e:
            logger.error(f"转换快手数据格式失败: {e}")
            raise e

    def _get_required_item_prop_values(self, category_id: int, product_data: Dict) -> List[Dict]:
        """根据类目ID和属性智能匹配配置获取必填的商品属性（快手API格式）"""
        item_prop_values = []

        try:
            # 获取商品标题用于智能匹配
            title = product_data.get('title', '')

            print(f"[快手小店上传] 为类目({category_id})智能匹配属性，商品标题: {title}")

            # 🚀 新方案：动态获取类目的所有必填属性
            return self._get_dynamic_required_props(category_id, title)

        except Exception as e:
            print(f"[快手小店上传] 获取必填属性失败: {e}")
            return []

    def _get_dynamic_required_props(self, category_id: int, title: str) -> List[Dict]:
        """动态获取类目的所有必填属性"""
        item_prop_values = []

        try:
            # 获取类目配置
            if not hasattr(self, 'kuaishou_api') or not self.kuaishou_api:
                print(f"[快手小店上传] 快手API未初始化")
                return []

            config_response = self.kuaishou_api.get_category_config(category_id)
            if not config_response or not config_response.get('success'):
                print(f"[快手小店上传] 获取类目配置失败")
                return []

            config_data = config_response.get('data', {})
            prop_configs = config_data.get('propConfigs', [])

            print(f"[快手小店上传] 开始处理{len(prop_configs)}个属性配置")

            # 从配置文件获取属性映射关系（不再硬编码）
            attr_config = self._load_attribute_mapping_config()
            attr_name_mapping = {}

            if attr_config and 'attribute_source_mapping' in attr_config:
                attr_name_mapping = attr_config['attribute_source_mapping']
                print(f"[快手小店上传] 从配置文件加载了 {len(attr_name_mapping)} 个属性映射")
            else:
                print(f"[快手小店上传] ⚠️ 配置文件中没有找到属性映射，将自动生成配置")

                # 自动生成配置文件
                if self.update_category_config_to_file(category_id):
                    print(f"[快手小店上传] ✅ 配置文件生成成功，重新加载配置")
                    # 重新加载配置
                    attr_config = self._load_attribute_mapping_config()
                    if attr_config and 'attribute_source_mapping' in attr_config:
                        attr_name_mapping = attr_config['attribute_source_mapping']
                        print(f"[快手小店上传] 重新加载了 {len(attr_name_mapping)} 个属性映射")
                else:
                    print(f"[快手小店上传] ❌ 配置文件生成失败，使用默认映射")
                    # 使用最基本的默认映射
                    attr_name_mapping = {}

            # 处理每个属性配置
            for prop_config in prop_configs:
                prop_name = prop_config.get('propName', '')
                prop_id = prop_config.get('propId')
                required = prop_config.get('required', False)
                prop_input_type = prop_config.get('propInputType', '')
                custom_input = prop_config.get('customInput', False)

                # 详细调试：记录每个属性的原始信息
                print(f"[快手小店上传] 🔍 处理属性: '{prop_name}' (ID: {prop_id}, 必填: {required})")

                # 检查是否有异常的属性名
                if "鞋面" in prop_name or "主材质" in prop_name:
                    print(f"[快手小店上传] ⚠️ 发现可疑的属性名: '{prop_name}' (ID: {prop_id})")
                    print(f"[快手小店上传] 🔍 原始prop_config: {prop_config}")

                    # 如果是ID 5504，应该是"帮面材质"
                    if prop_id == 5504:
                        print(f"[快手小店上传] 🔧 ID 5504应该是'帮面材质'，当前是'{prop_name}'")
                        if prop_name != "帮面材质":
                            print(f"[快手小店上传] 🔧 修正属性名: '{prop_name}' -> '帮面材质'")
                            prop_name = "帮面材质"
                            # 更新prop_config中的属性名
                            prop_config = prop_config.copy()
                            prop_config['propName'] = "帮面材质"

                # 只处理必填属性
                if required:
                    print(f"[快手小店上传] 🔍 调试 - 原始prop_config: {prop_config}")
                    print(f"[快手小店上传] 🔍 调试 - 提取的prop_name: '{prop_name}'")
                    print(f"[快手小店上传] 处理必填属性: {prop_name} (ID: {prop_id}, 类型: {prop_input_type})")
                    # 尝试智能匹配属性值
                    matched_value = self._match_prop_value(prop_name, title, attr_name_mapping, prop_config)

                    if matched_value and matched_value != "USE_DEFAULT_VALUE":
                        # 构建属性数据
                        prop_data = self._build_prop_data(prop_config, matched_value)
                        if prop_data == "USE_DEFAULT_VALUE":
                            # 如果构建过程中发现需要使用默认值，直接获取默认属性数据
                            default_prop_data = self._get_default_value_for_required_prop(prop_name, prop_config, category_id)
                            if default_prop_data and isinstance(default_prop_data, dict):
                                item_prop_values.append(default_prop_data)
                                print(f"[快手小店上传] 🔧 构建过程中使用默认值: {prop_name}")
                        elif prop_data:
                            item_prop_values.append(prop_data)
                            print(f"[快手小店上传] ✅ 添加属性: {prop_name} = {matched_value}")
                    else:
                        # 🚀 新增：如果没有匹配到值或返回USE_DEFAULT_VALUE，使用默认值兜底
                        if matched_value == "USE_DEFAULT_VALUE":
                            print(f"[快手小店上传] ⚠️ 属性 {prop_name} 无法获取属性值ID，使用默认值兜底")
                        else:
                            print(f"[快手小店上传] ⚠️ 属性 {prop_name} 未找到匹配值，使用默认值兜底")

                        # 🚀 修复：直接获取完整的默认属性数据
                        default_prop_data = self._get_default_value_for_required_prop(prop_name, prop_config, category_id)

                        if default_prop_data and isinstance(default_prop_data, dict):
                            item_prop_values.append(default_prop_data)
                            default_value_str = default_prop_data.get('radioPropValue', {}).get('propValue') or \
                                              default_prop_data.get('textPropValue') or \
                                              (default_prop_data.get('checkBoxPropValuesList', [{}])[0].get('propValue') if default_prop_data.get('checkBoxPropValuesList') else 'N/A')
                            print(f"[快手小店上传] 🔧 使用默认值: {prop_name} = {default_value_str}")
                        else:
                            print(f"[快手小店上传] ❌ 无法获取默认值: {prop_name}")

            print(f"[快手小店上传] 动态生成了{len(item_prop_values)}个属性")
            return item_prop_values

        except Exception as e:
            print(f"[快手小店上传] 动态获取必填属性异常: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _get_default_value_for_required_prop(self, prop_name: str, prop_config: Dict, category_id: int) -> Dict:
        """为必填属性获取默认值并构建完整的属性数据"""
        try:
            print(f"[快手小店上传] 🔧 为必填属性 '{prop_name}' 获取默认值")

            prop_id = prop_config.get('propId')
            prop_input_type = prop_config.get('propInputType', 'RADIO')

            # 🚀 修复：从配置文件中读取fallback值
            default_value = self._get_fallback_value_from_config(prop_name)
            print(f"[快手小店上传] 🎯 使用配置文件fallback值: {prop_name} = {default_value}")

            # 🚀 新增：尝试获取默认值对应的属性值ID
            prop_value_id = None
            try:
                available_values = self._query_prop_values(category_id, prop_id, prop_name)
                if available_values:
                    # 查找匹配的属性值ID
                    for value_item in available_values:
                        if value_item.get('propValue', '') == default_value:
                            prop_value_id = value_item.get('propValueId')
                            print(f"[快手小店上传] 🎯 找到默认值对应的属性值ID: {prop_value_id}")
                            break

                    # 如果没找到匹配的，使用第一个可用值
                    if not prop_value_id and len(available_values) > 0:
                        first_item = available_values[0]
                        prop_value_id = first_item.get('propValueId')
                        default_value = first_item.get('propValue', default_value)
                        print(f"[快手小店上传] 🎯 使用第一个可用值: {prop_value_id} = {default_value}")
            except Exception as e:
                print(f"[快手小店上传] 查询可用值失败: {e}")

            # 🚀 新增：根据官方文档构建完整的属性数据结构
            if prop_input_type == 'TEXT':
                return {
                    'propId': prop_id,
                    'textPropValue': default_value,
                    'propName': prop_name,
                    'propAlias': prop_name,
                    'inputType': 1,  # 1-文本
                    'propType': 2,  # 2-商品属性
                    'sortNum': prop_config.get('sortNum', 1)
                }
            elif prop_input_type == 'CHECKBOX':
                if prop_value_id:
                    return {
                        'propId': prop_id,
                        'checkBoxPropValuesList': [{
                            'propValueId': prop_value_id,
                            'propValue': default_value
                        }],
                        'propName': prop_name,
                        'propAlias': prop_name,
                        'inputType': 2,  # 2-checkbox
                        'propType': 2,  # 2-商品属性
                        'sortNum': prop_config.get('sortNum', 1)
                    }
                else:
                    # 降级为单选
                    return {
                        'propId': prop_id,
                        'radioPropValue': {
                            'propValueId': None,
                            'propValue': default_value
                        },
                        'propName': prop_name,
                        'propAlias': prop_name,
                        'inputType': 8,  # 8-单选框
                        'propType': 2,  # 2-商品属性
                        'sortNum': prop_config.get('sortNum', 1)
                    }
            else:
                # 默认使用RADIO格式
                return {
                    'propId': prop_id,
                    'radioPropValue': {
                        'propValueId': prop_value_id,
                        'propValue': default_value
                    },
                    'propName': prop_name,
                    'propAlias': prop_name,
                    'inputType': 8,  # 8-单选框
                    'propType': 2,  # 2-商品属性
                    'sortNum': prop_config.get('sortNum', 1)
                }

        except Exception as e:
            print(f"[快手小店上传] 获取默认值异常: {e}")
            # 返回最基本的兜底结构
            return {
                'propId': prop_config.get('propId'),
                'radioPropValue': {
                    'propValueId': None,
                    'propValue': "其他"
                },
                'propName': prop_name,
                'propAlias': prop_name,
                'inputType': 8,  # 8-单选框
                'propType': 2,  # 2-商品属性
                'sortNum': prop_config.get('sortNum', 1)
            }

    def _get_value_from_product_attributes(self, prop_name: str) -> str:
        """从1688商品属性字段中获取属性值"""
        try:
            if not hasattr(self, 'current_product_attributes') or not self.current_product_attributes:
                return ""

            # 属性名映射：快手属性名 -> 1688属性名
            attr_mapping = {
                '帮面材质': ['鞋面材质', '内里材质'],  # 如果没有鞋面材质，使用内里材质
                '跟底款式': ['产品类别'],
                '上市年份季节': ['上市年份季节（上市时间）', '上市年份季节'],
                '鞋底材质': ['鞋底材质'],
                '鞋垫材质': ['鞋垫材质'],
                '内里材质': ['内里材质'],
                '适用季节': ['适用季节'],
                '风格': ['风格'],
                '闭合方式': ['闭合方式'],
                '制作工艺': ['制作工艺']
            }

            # 获取对应的1688属性名列表
            target_attr_names = attr_mapping.get(prop_name, [prop_name])
            if not isinstance(target_attr_names, list):
                target_attr_names = [target_attr_names]

            # 在1688商品属性中查找匹配的值
            for target_attr_name in target_attr_names:
                for attr in self.current_product_attributes:
                    if attr.get('attributeName') == target_attr_name:
                        value = attr.get('value', '').strip()
                        if value:
                            print(f"[快手小店上传] 找到1688属性: {target_attr_name} = {value}")
                            return value

            return ""

        except Exception as e:
            print(f"[快手小店上传] 从1688属性获取值异常: {e}")
            return ""

    def _get_fallback_value_from_config(self, prop_name: str) -> str:
        """从配置文件中获取属性的fallback值"""
        try:
            # 读取智能匹配配置文件
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', '属性智能匹配.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                # 从global_attribute_mapping中获取fallback值
                global_mapping = config_data.get('global_attribute_mapping', {})
                prop_config = global_mapping.get(prop_name, {})
                fallback_value = prop_config.get('fallback', '')

                if fallback_value:
                    print(f"[快手小店上传] 📁 从配置文件获取fallback值: {prop_name} = {fallback_value}")
                    return fallback_value
                else:
                    print(f"[快手小店上传] ⚠️ 配置文件中未找到 '{prop_name}' 的fallback值")
            else:
                print(f"[快手小店上传] ⚠️ 配置文件不存在: {config_path}")
        except Exception as e:
            print(f"[快手小店上传] 读取配置文件异常: {e}")

        # 如果配置文件中没有，使用基础默认值
        basic_defaults = {
            "品牌": "无品牌",
            "上市年份季节": "2025年夏季",
            "材质": "棉",
            "面料材质": "棉"
        }
        default_value = basic_defaults.get(prop_name, "其他")
        print(f"[快手小店上传] 🔧 使用基础默认值: {prop_name} = {default_value}")
        return default_value

    def _match_prop_value(self, prop_name: str, title: str, attr_name_mapping: Dict, prop_config: Dict) -> str:
        """匹配属性值"""
        try:
            # 首先尝试从1688商品属性字段中获取值
            if hasattr(self, 'current_product_attributes') and self.current_product_attributes:
                # 从配置文件获取属性映射关系
                attr_config = self._load_attribute_mapping_config()
                if attr_config and 'attribute_source_mapping' in attr_config:
                    source_mapping = attr_config['attribute_source_mapping']

                    # 获取对应的1688属性名列表
                    source_attr_names = source_mapping.get(prop_name, [])

                    # 在1688商品属性中查找匹配的值
                    for source_attr_name in source_attr_names:
                        for attr in self.current_product_attributes:
                            if attr.get('attributeName') == source_attr_name:
                                raw_value = attr.get('value', '').strip()
                                if raw_value:
                                    print(f"[快手小店上传] 从1688属性获取: {source_attr_name} = {raw_value}")

                                    # 使用配置文件进行智能匹配
                                    matched_value = self._smart_match_attribute(
                                        attr_config,
                                        prop_name,  # 使用快手的属性名进行匹配
                                        title,
                                        raw_value  # 使用1688的原始值进行匹配
                                    )
                                    if matched_value:
                                        print(f"[快手小店上传] 智能匹配成功: {prop_name} = {matched_value} (来源: {source_attr_name}={raw_value})")
                                        return matched_value

                                    # 如果智能匹配失败，直接返回原始值
                                    print(f"[快手小店上传] 使用原始值: {prop_name} = {raw_value}")
                                    return raw_value

            # 如果没有从1688属性中找到值，尝试智能匹配
            config_attr_names = attr_name_mapping.get(prop_name, prop_name)
            if not isinstance(config_attr_names, list):
                config_attr_names = [config_attr_names]

            # 加载属性配置并使用智能匹配
            attr_config = self._load_attribute_mapping_config()
            if attr_config:
                for config_attr_name in config_attr_names:
                    matched_value = self._smart_match_attribute(
                        attr_config,
                        config_attr_name,
                        title,
                        ''
                    )
                    if matched_value:
                        print(f"[快手小店上传] 智能匹配成功: {prop_name} = {matched_value} (来源: {config_attr_name})")
                        return matched_value

            # 如果智能匹配失败，使用默认值
            default_values = {
                '品牌': '其他',
                '帮面材质': '真皮',
                '跟底款式': '平跟',
                '上市年份季节': '2024年夏季',
                '鞋底材质': '橡胶',
                '鞋垫材质': '布',
                '内里材质': '布',
                '适用季节': '夏季',
                '适用性别': '女',
                '闭合方式': '套脚',
                '后跟高': '平跟',
                '适用场景': '日常',
                '风格': '休闲',
                '功能': '舒适',
                '制作工艺': '其他',
                '开口深度': '浅口',
                '流行元素': '纯色',
                '皮质特征': '光面',
                '鞋头款式': '圆头',
                '厚薄': '标准',
                '毛重': '0.5'
            }

            default_value = default_values.get(prop_name, '其他')
            print(f"[快手小店上传] 使用默认值: {prop_name} = {default_value}")
            return default_value

        except Exception as e:
            print(f"[快手小店上传] 匹配属性值失败: {e}")
            return '其他'

    def _build_prop_data(self, prop_config: Dict, prop_value: str) -> Dict:
        """构建属性数据"""
        try:
            prop_id = prop_config.get('propId')
            prop_name = prop_config.get('propName', '')
            prop_input_type = prop_config.get('propInputType', 'RADIO')

            # 🚀 新增：优先检查强制属性配置
            force_value = self._check_force_attribute(prop_name)
            if force_value:
                print(f"[快手小店上传] 🔒 使用强制属性数据构建: {prop_name}")
                return {
                    'propId': prop_id,
                    'radioPropValue': {
                        'propValueId': force_value.get('propValueId'),
                        'propValue': force_value.get('propValue'),
                        'brandOwner': force_value.get('brandOwner', ''),
                        'hasBrandCertificate': force_value.get('hasBrandCertificate', True)
                    },
                    'propName': prop_name,
                    'inputType': 8,  # 8-单选
                    'propType': 2
                }

            # 获取属性值ID
            prop_value_id = None
            actual_prop_value = prop_value
            required = prop_config.get('required', False)

            if prop_input_type in ['RADIO', 'CHECKBOX']:
                # 对于单选和多选，需要获取属性值ID
                # 使用当前商品的类目ID
                current_product = getattr(self, 'current_product_data', {})
                category_id = current_product.get('categoryId', getattr(self, 'current_category_id', 10378))
                print(f"[快手小店上传] 使用类目ID {category_id} 查询属性 '{prop_name}' 的值")
                result = self._get_category_attribute_ids(category_id, prop_name, prop_value)
                if len(result) == 3:
                    _, prop_value_id, actual_prop_value = result
                else:
                    _, prop_value_id = result

                # 如果是必填属性但没有获取到属性值ID，强制获取一个
                if required and not prop_value_id:
                    print(f"[快手小店上传] 必填属性{prop_name}没有属性值ID，强制搜索")
                    prop_value_id, actual_prop_value = self._force_get_prop_value_id(category_id, prop_id, prop_name, prop_value)

                    # 如果强制获取也失败，对于必填属性不能跳过，返回默认值
                    if prop_value_id is None:
                        print(f"[快手小店上传] ⚠️ 必填属性 {prop_name} 无法获取属性值ID，将使用默认值")
                        # 对于必填属性，返回一个特殊标记，让上层处理使用默认值
                        return "USE_DEFAULT_VALUE"

            # 根据输入类型构建不同的数据结构
            if prop_input_type == 'TEXT':
                # 文本输入 - 使用正确的API格式
                # 🚀 新增：根据官方文档添加完整字段
                return {
                    'propId': prop_id,
                    'textPropValue': actual_prop_value,  # 使用API返回的确切值
                    'propName': prop_name,
                    'propAlias': prop_name,  # 属性别名
                    'inputType': 1,  # 1-文本
                    'propType': 2,  # 2-商品属性
                    'sortNum': prop_config.get('sortNum', 1)  # 序号，从配置中获取
                }
            elif prop_input_type == 'RADIO':
                # 单选 - 必填属性必须有属性值ID
                if required and not prop_value_id:
                    print(f"[快手小店上传] 必填RADIO属性{prop_name}没有属性值ID，强制获取")
                    prop_value_id, actual_prop_value = self._force_get_prop_value_id(category_id, prop_id, prop_name, prop_value)

                    # 🚀 修复：如果强制获取也失败，对必填属性使用默认值兜底
                    if prop_value_id is None:
                        print(f"[快手小店上传] ⚠️ 必填RADIO属性 {prop_name} 无法获取属性值ID，使用默认值兜底")
                        return "USE_DEFAULT_VALUE"

                # 🚀 新增：根据官方文档添加完整字段
                return {
                    'propId': prop_id,
                    'radioPropValue': {
                        'propValueId': prop_value_id,
                        'propValue': actual_prop_value  # 使用API返回的确切值
                    },
                    'propName': prop_name,
                    'propAlias': prop_name,  # 属性别名
                    'inputType': 8,  # 8-单选框
                    'propType': 2,  # 2-商品属性
                    'sortNum': prop_config.get('sortNum', 1)  # 序号，从配置中获取
                }
            elif prop_input_type == 'CHECKBOX':
                # 多选 - 使用正确的API格式
                # 如果是必填属性但没有属性值ID，强制获取
                if required and not prop_value_id:
                    print(f"[快手小店上传] 必填CHECKBOX属性{prop_name}没有属性值ID，强制获取")
                    prop_value_id, actual_prop_value = self._force_get_prop_value_id(category_id, prop_id, prop_name, prop_value)

                    # 🚀 修复：如果强制获取也失败，对必填属性使用默认值兜底
                    if prop_value_id is None:
                        print(f"[快手小店上传] ⚠️ 必填CHECKBOX属性 {prop_name} 无法获取属性值ID，使用默认值兜底")
                        return "USE_DEFAULT_VALUE"

                if prop_value_id:
                    print(f"[快手小店上传] CHECKBOX属性{prop_name}使用正确的API格式")
                    # 🚀 新增：根据官方文档添加完整字段
                    return {
                        'propId': prop_id,
                        'checkBoxPropValuesList': [  # 修复：使用正确的字段名
                            {
                                'propValueId': prop_value_id,
                                'propValue': actual_prop_value  # 使用API返回的确切值
                            }
                        ],
                        'propName': prop_name,
                        'propAlias': prop_name,  # 属性别名
                        'inputType': 2,  # 2-checkbox
                        'propType': 2,  # 2-商品属性
                        'sortNum': prop_config.get('sortNum', 1)  # 序号，从配置中获取
                    }
                else:
                    # 🚀 修复：必填属性不能降级，必须使用默认值
                    if required:
                        print(f"[快手小店上传] ⚠️ 必填CHECKBOX属性 {prop_name} 无属性值ID，使用默认值兜底")
                        return "USE_DEFAULT_VALUE"

                    # 非必填属性可以降级为单选格式
                    print(f"[快手小店上传] 非必填CHECKBOX属性{prop_name}无属性值ID，降级为单选格式")
                    return {
                        'propId': prop_id,
                        'radioPropValue': {
                            'propValueId': None,
                            'propValue': actual_prop_value  # 使用API返回的确切值
                        },
                        'propName': prop_name,
                        'propAlias': prop_name,  # 属性别名
                        'inputType': 8,  # 8-单选框
                        'propType': 2,  # 2-商品属性
                        'sortNum': prop_config.get('sortNum', 1)  # 序号，从配置中获取
                    }
            else:
                # 默认使用单选格式
                # 🚀 修复：必填属性必须有值
                if required and not prop_value_id:
                    print(f"[快手小店上传] ⚠️ 必填属性 {prop_name} (类型: {prop_input_type}) 无法获取属性值ID，使用默认值兜底")
                    return "USE_DEFAULT_VALUE"

                # 🚀 新增：根据官方文档添加完整字段
                return {
                    'propId': prop_id,
                    'radioPropValue': {
                        'propValueId': prop_value_id,
                        'propValue': prop_value
                    },
                    'propName': prop_name,
                    'propAlias': prop_name,  # 属性别名
                    'inputType': 8,  # 8-单选框
                    'propType': 2,  # 2-商品属性
                    'sortNum': prop_config.get('sortNum', 1)  # 序号，从配置中获取
                }

        except Exception as e:
            print(f"[快手小店上传] 构建属性数据失败: {e}")
            return None

    def _get_category_attribute_ids(self, category_id: int, attr_name: str, attr_value: str) -> tuple:
        """获取类目属性的propId和propValueId"""
        try:
            # 尝试调用快手API获取销售属性规则
            if hasattr(self, 'kuaishou_api') and self.kuaishou_api:
                print(f"[快手小店上传] 开始获取类目{category_id}的销售属性规则...")

                # 调用 open.item.saleprop.rule API
                saleprop_response = self.kuaishou_api.get_saleprop_rule(category_id)
                print(f"[快手小店上传] 销售属性API响应成功: {saleprop_response.get('success') if saleprop_response else False}")

                if saleprop_response and saleprop_response.get('success'):
                    saleprop_data = saleprop_response.get('data', {})
                    preset_sale_prop = saleprop_data.get('presetSaleProp', [])

                    print(f"[快手小店上传] 获取到{len(preset_sale_prop)}个预设销售属性")

                    if preset_sale_prop:
                        print(f"[快手小店上传] 销售属性列表:")
                        for i, prop in enumerate(preset_sale_prop):
                            prop_name = prop.get('propName', '')
                            prop_id = prop.get('propId', '')
                            print(f"  {i+1}. {prop_name} (ID: {prop_id})")

                        print(f"[快手小店上传] 正在查找匹配的属性: '{attr_name}'")

                        # 查找匹配的销售属性
                        for sale_prop in preset_sale_prop:
                            prop_name = sale_prop.get('propName', '')
                            prop_id = sale_prop.get('propId')

                            print(f"[快手小店上传] 检查属性匹配: API属性'{prop_name}' vs 目标属性'{attr_name}'")

                            # 尝试匹配属性名称
                            is_match = self._is_attribute_match(prop_name, attr_name)
                            print(f"[快手小店上传] 匹配结果: {is_match}")

                            if is_match:
                                print(f"[快手小店上传] ✅ 找到匹配的销售属性: {prop_name} -> {attr_name} (ID: {prop_id})")

                                # 查找属性值
                                prop_value_groups = sale_prop.get('propValueGroup', [])
                                for group in prop_value_groups:
                                    prop_values = group.get('propValue', [])
                                    for prop_val in prop_values:
                                        if prop_val.get('propValueName') == attr_value:
                                            prop_value_id = prop_val.get('propValueId')
                                            print(f"[快手小店上传] 找到精确匹配的销售属性值: {attr_name}({prop_id}) = {attr_value}({prop_value_id})")
                                            return prop_id, prop_value_id

                                    # 如果没有精确匹配，使用第一个可用值
                                    if prop_values:
                                        first_value = prop_values[0]
                                        prop_value_id = first_value.get('propValueId')
                                        actual_value = first_value.get('propValueName')
                                        print(f"[快手小店上传] 使用第一个可用销售属性值: {attr_name}({prop_id}) = {actual_value}({prop_value_id})")
                                        return prop_id, prop_value_id

                                print(f"[快手小店上传] 找到销售属性但无可用值: {attr_name}({prop_id})")
                                return prop_id, None
                else:
                    print(f"[快手小店上传] 销售属性API调用失败或返回格式错误")

                # 如果销售属性API失败，尝试类目配置API
                print(f"[快手小店上传] 尝试获取类目{category_id}的基础属性配置...")
                config_response = self.kuaishou_api.get_category_config(category_id)
                print(f"[快手小店上传] 类目配置API响应: {config_response}")

                if config_response and config_response.get('success'):
                    config_data = config_response.get('data', {})
                    # 修复：使用正确的字段名 propConfigs
                    attr_list = config_data.get('propConfigs', [])

                    print(f"[快手小店上传] 获取到{len(attr_list)}个基础属性配置")

                    if attr_list:
                        print(f"[快手小店上传] 基础属性列表:")
                        for i, attr in enumerate(attr_list):
                            attr_name_api = attr.get('propName', '')
                            attr_id = attr.get('propId', '')
                            required = attr.get('required', False)
                            print(f"  {i+1}. {attr_name_api} (ID: {attr_id}, 必填: {required})")

                        print(f"[快手小店上传] 正在查找匹配的基础属性: '{attr_name}'")

                        # 查找匹配的属性 - 优先精确匹配
                        matched_attr = None
                        for attr in attr_list:
                            attr_name_api = attr.get('propName', '')  # 修复：使用正确的字段名

                            print(f"[快手小店上传] 检查基础属性匹配: API属性'{attr_name_api}' vs 目标属性'{attr_name}'")

                            # 优先精确匹配
                            if attr_name_api == attr_name:
                                matched_attr = attr
                                print(f"[快手小店上传] ✅ 精确匹配: {attr_name_api} == {attr_name}")
                                break

                        # 如果没有精确匹配，再尝试智能匹配
                        if not matched_attr:
                            for attr in attr_list:
                                attr_name_api = attr.get('propName', '')

                                # 使用智能匹配，但排除容易混淆的情况
                                if self._is_safe_attribute_match(attr_name_api, attr_name):
                                    matched_attr = attr
                                    print(f"[快手小店上传] ✅ 智能匹配: {attr_name_api} -> {attr_name}")
                                    break

                        if matched_attr:
                            prop_id = matched_attr.get('propId')  # 修复：使用正确的字段名
                            print(f"[快手小店上传] ✅ 找到匹配的基础属性: {matched_attr.get('propName')} -> {attr_name} (ID: {prop_id})")

                            # 使用缓存的属性值查询方法
                            print(f"[快手小店上传] 🔍 通过缓存方法查询属性值...")
                            cached_prop_values = self._query_prop_values(category_id, prop_id, matched_attr.get('propName'))

                            # 使用智能匹配配置进行属性值匹配
                            print(f"[快手小店上传] 🎯 使用智能匹配配置匹配属性值...")

                            # 获取商品标题用于智能匹配（从当前处理的商品数据中获取）
                            title = getattr(self, 'current_product_title', '') or ""

                            # 使用智能匹配配置
                            attr_config = self._load_attribute_mapping_config()
                            matched_value = None

                            if attr_config:
                                matched_value = self._smart_match_attribute(
                                    attr_config,
                                    matched_attr.get('propName'),  # 属性名
                                    title,  # 商品标题
                                    attr_value  # 原始值作为fallback
                                )

                            # 在可用值中查找匹配的属性值
                            search_response = {'success': False, 'propValues': []}
                            if cached_prop_values and matched_value:
                                for prop_value_item in cached_prop_values:
                                    if prop_value_item.get('propValue', '').lower() == matched_value.lower():
                                        search_response = {
                                            'success': True,
                                            'propValues': [prop_value_item]
                                        }
                                        print(f"[快手小店上传] ✅ 智能匹配成功: {matched_attr.get('propName')} = {matched_value}")
                                        break

                            if search_response and search_response.get('success'):
                                # 直接从search_response获取propValues，不需要通过data字段
                                prop_values = search_response.get('propValues', [])

                                print(f"[快手小店上传] 搜索到{len(prop_values)}个属性值")

                                # 查找匹配的属性值
                                for prop_val in prop_values:
                                    cached_value = prop_val.get('propValue', '')
                                    # 🚀 修复：使用包含匹配而不是严格相等
                                    if (cached_value.lower() == attr_value.lower() or
                                        attr_value.lower() in cached_value.lower() or
                                        cached_value.lower() in attr_value.lower()):
                                        prop_value_id = prop_val.get('propValueId')
                                        actual_value = prop_val.get('propValue')
                                        print(f"[快手小店上传] 找到匹配: {attr_name}({prop_id}) = {actual_value}({prop_value_id}) (原值: {attr_value})")
                                        return prop_id, prop_value_id, actual_value

                                # 如果没有精确匹配，使用第一个可用值
                                if prop_values:
                                    first_value = prop_values[0]
                                    prop_value_id = first_value.get('propValueId')
                                    actual_value = first_value.get('propValue')
                                    print(f"[快手小店上传] 使用第一个可用值: {attr_name}({prop_id}) = {actual_value}({prop_value_id})")
                                    # 返回API的确切属性值，而不是我们传入的值
                                    return prop_id, prop_value_id, actual_value
                            else:
                                print(f"[快手小店上传] 搜索属性值失败: {search_response}")

                            # 如果没有找到属性值，尝试搜索常见的替代值
                            # 检查是否有可用的属性值
                            search_prop_values = search_response.get('propValues', []) if search_response.get('success') else []
                            if not search_prop_values:
                                alternative_values = {
                                    '布': ['棉', '棉布', '纺织品', '织物'],
                                    '橡胶': ['橡胶', 'TPR', 'PU'],
                                    '牛皮': ['牛皮', '真皮', '皮革', '牛皮（革）'],
                                    '平跟': ['平底', '平跟', '无跟', '平底鞋']
                                }

                                for alt_value in alternative_values.get(attr_value, []):
                                    print(f"[快手小店上传] 尝试搜索替代值: {alt_value}")
                                    # 使用缓存方法查询属性值
                                    cached_prop_values = self._query_prop_values(category_id, prop_id, matched_attr.get('propName'))

                                    # 在缓存的属性值中搜索匹配的值
                                    alt_search_response = {'success': False, 'propValues': []}
                                    if cached_prop_values:
                                        print(f"[快手小店上传] 🔍 在 {len(cached_prop_values)} 个缓存值中搜索: '{alt_value}'")
                                        for i, prop_value_item in enumerate(cached_prop_values):
                                            cached_value = prop_value_item.get('propValue', '')
                                            print(f"[快手小店上传]   {i+1}. 比较: '{cached_value}' vs '{alt_value}'")
                                            # 🚀 修复：使用包含匹配而不是严格相等
                                            if (cached_value.lower() == alt_value.lower() or
                                                alt_value.lower() in cached_value.lower() or
                                                cached_value.lower() in alt_value.lower()):
                                                print(f"[快手小店上传] ✅ 找到匹配: '{cached_value}' 匹配 '{alt_value}'")
                                                alt_search_response = {
                                                    'success': True,
                                                    'propValues': [prop_value_item]
                                                }
                                                break
                                            elif cached_value.lower().strip() == alt_value.lower().strip():
                                                print(f"[快手小店上传] ✅ 找到匹配（去空格后）: '{cached_value}' == '{alt_value}'")
                                                alt_search_response = {
                                                    'success': True,
                                                    'propValues': [prop_value_item]
                                                }
                                                break

                                        if not alt_search_response.get('success'):
                                            print(f"[快手小店上传] ❌ 未找到匹配的属性值: '{alt_value}'")

                                    if alt_search_response and alt_search_response.get('success'):
                                        alt_search_data = alt_search_response.get('data', {})
                                        alt_prop_values = alt_search_data.get('propValues', [])

                                        if alt_prop_values:
                                            first_value = alt_prop_values[0]
                                            prop_value_id = first_value.get('propValueId')
                                            actual_value = first_value.get('propValue')
                                            print(f"[快手小店上传] 找到替代值: {attr_name}({prop_id}) = {actual_value}({prop_value_id})")
                                            return prop_id, prop_value_id, actual_value

                            # 基础属性通常没有预设值，直接返回属性ID
                            # 对于基础属性，我们可能需要使用文本输入或者其他方式
                            print(f"[快手小店上传] 基础属性无需属性值ID，直接使用属性ID: {attr_name}({prop_id})")

                            # 对于基础属性，我们可以返回属性ID，属性值ID设为None
                            # 快手API可能支持只传属性ID而不传属性值ID的情况
                            return prop_id, None, attr_value

                        print(f"[快手小店上传] 未找到属性: {attr_name}")
                else:
                    print(f"[快手小店上传] 基础属性API调用失败或返回格式错误")
            else:
                print(f"[快手小店上传] 快手API未初始化，无法获取属性ID")

        except Exception as e:
            print(f"[快手小店上传] 获取属性ID异常: {e}")
            import traceback
            traceback.print_exc()

        # 如果无法获取真实ID，查询可用的属性值
        print(f"[快手小店上传] 无法获取真实ID，查询可用的属性值")

        # 尝试查询属性的可用值
        try:
            # 从当前处理的商品数据中获取类目ID
            current_product = getattr(self, 'current_product_data', {})
            category_id = current_product.get('categoryId', getattr(self, 'current_category_id', 10378))

            # 查询属性的可用值
            available_values = self._query_prop_values(category_id, prop_id, prop_name)
            if available_values:
                # 尝试智能匹配可用值
                # 获取当前处理的商品数据
                current_product = getattr(self, 'current_product_data', {})
                matched_value = self._smart_match_prop_value(prop_name, available_values, current_product)
                if matched_value:
                    print(f"[快手小店上传] 智能匹配到属性值: {prop_name} = {matched_value['propValue']}")
                    return {
                        'propId': prop_id,
                        'propValueId': matched_value['propValueId'],
                        'propValue': matched_value['propValue']
                    }
        except Exception as e:
            print(f"[快手小店上传] 查询属性值失败: {e}")

        print(f"[快手小店上传] ❌ 无法获取属性 '{attr_name}' 的真实ID，跳过此属性")
        print(f"[快手小店上传] 💡 建议：确保类目ID正确，或检查属性名称是否匹配API返回的属性")

        # 不再使用临时ID，直接返回None表示无法获取
        return None, None, None

    def _force_get_prop_value_id(self, category_id: int, prop_id: int, prop_name: str, prop_value: str) -> tuple:
        """强制获取属性值ID，用于必填属性"""
        try:
            print(f"[快手小店上传] 强制获取属性值ID: {prop_name} = {prop_value}")

            # 尝试搜索属性值（使用缓存方法）
            if hasattr(self, 'kuaishou_api') and self.kuaishou_api:
                # 使用缓存方法查询属性值
                cached_prop_values = self._query_prop_values(category_id, prop_id, prop_name)

                # 在缓存的属性值中搜索匹配的值
                search_response = {'success': False, 'propValues': []}
                if cached_prop_values:
                    for prop_value_item in cached_prop_values:
                        if prop_value_item.get('propValue', '').lower() == prop_value.lower():
                            search_response = {
                                'success': True,
                                'propValues': [prop_value_item]
                            }
                            break

                if search_response and search_response.get('success'):
                    search_data = search_response.get('data', {})
                    prop_values = search_data.get('propValues', [])

                    if prop_values:
                        # 使用第一个可用值
                        first_value = prop_values[0]
                        prop_value_id = first_value.get('propValueId')
                        actual_value = first_value.get('propValue')
                        print(f"[快手小店上传] 强制获取成功: {prop_name} = {actual_value}({prop_value_id})")
                        return prop_value_id, actual_value

                # 如果搜索失败，尝试替代值
                alternative_values = {
                    '平跟': ['平底', '平跟', '无跟', '平底鞋', '低跟'],
                    '布': ['棉', '棉布', '纺织品', '织物'],
                    '橡胶': ['橡胶', 'TPR', 'PU', '合成材料'],
                    '牛皮': ['牛皮', '真皮', '皮革', '牛皮（革）'],
                    '真皮': ['牛皮', '真皮', '皮革', '牛皮（革）'],
                    '套脚': ['套脚', '一脚蹬', '无系带'],
                    '夏季': ['夏季', '夏天', '春夏'],
                    '其他': ['其他', '无品牌', '自主品牌', '人造革', '合成革', 'PU', '人造皮', '仿皮', '合成材料'],
                    '高跟': ['高跟', '细跟', '超高跟', '中跟']
                }

                for alt_value in alternative_values.get(prop_value, [prop_value]):
                    print(f"[快手小店上传] 尝试搜索替代值: {alt_value}")
                    # 使用缓存方法查询属性值
                    cached_prop_values = self._query_prop_values(category_id, prop_id, prop_name)

                    # 在缓存的属性值中搜索匹配的值
                    alt_search_response = {'success': False, 'propValues': []}
                    if cached_prop_values:
                        for prop_value_item in cached_prop_values:
                            if prop_value_item.get('propValue', '').lower() == alt_value.lower():
                                alt_search_response = {
                                    'success': True,
                                    'propValues': [prop_value_item]
                                }
                                break

                    if alt_search_response and alt_search_response.get('success'):
                        alt_search_data = alt_search_response.get('data', {})
                        alt_prop_values = alt_search_data.get('propValues', [])

                        if alt_prop_values:
                            first_value = alt_prop_values[0]
                            prop_value_id = first_value.get('propValueId')
                            actual_value = first_value.get('propValue')
                            print(f"[快手小店上传] 找到替代值: {prop_name} = {actual_value}({prop_value_id})")
                            return prop_value_id, actual_value

                # 如果所有替代值都搜索不到，尝试获取该属性的所有可用值
                print(f"[快手小店上传] 所有替代值都搜索不到，获取属性{prop_name}的所有可用值")
                # 使用缓存方法获取所有属性值
                cached_prop_values = self._query_prop_values(category_id, prop_id, prop_name)
                all_values_response = {
                    'success': True if cached_prop_values else False,
                    'propValues': cached_prop_values or []
                }

                if all_values_response and all_values_response.get('success'):
                    all_search_data = all_values_response.get('data', {})
                    all_prop_values = all_search_data.get('propValues', [])

                    if all_prop_values:
                        # 优先选择包含"其他"、"人造"、"合成"等通用词的值
                        for value in all_prop_values:
                            value_name = value.get('propValue', '')
                            if any(keyword in value_name for keyword in ['其他', '人造', '合成', 'PU', '仿']):
                                prop_value_id = value.get('propValueId')
                                print(f"[快手小店上传] 找到通用值: {prop_name} = {value_name}({prop_value_id})")
                                return prop_value_id, value_name

                        # 🚀 修复：不要直接使用缓存中的第一个值，而是重新查询验证
                        print(f"[快手小店上传] ⚠️ 没有找到通用值，尝试重新查询最新属性值...")

                        # 强制重新查询最新的属性值（不使用缓存）
                        fresh_response = self.kuaishou_api.search_category_prop_values(
                            category_id=category_id,
                            prop_id=prop_id,
                            cursor=0,
                            limit=50
                        )

                        if fresh_response and fresh_response.get('success'):
                            fresh_prop_values = fresh_response.get('propValues', [])
                            if fresh_prop_values:
                                # 优先选择包含"其他"、"人造"、"合成"等通用词的值
                                for value in fresh_prop_values:
                                    value_name = value.get('propValue', '')
                                    if any(keyword in value_name for keyword in ['其他', '人造', '合成', 'PU', '仿', '布']):
                                        prop_value_id = value.get('propValueId')
                                        print(f"[快手小店上传] 🎯 重新查询找到通用值: {prop_name} = {value_name}({prop_value_id})")
                                        return prop_value_id, value_name

                                # 如果没有通用值，使用第一个最新值
                                first_value = fresh_prop_values[0]
                                prop_value_id = first_value.get('propValueId')
                                actual_value = first_value.get('propValue')
                                print(f"[快手小店上传] 🔄 使用重新查询的第一个可用值: {prop_name} = {actual_value}({prop_value_id})")
                                return prop_value_id, actual_value

                        # 如果重新查询也失败，使用缓存中的第一个值（作为最后的备选）
                        first_value = all_prop_values[0]
                        prop_value_id = first_value.get('propValueId')
                        actual_value = first_value.get('propValue')
                        print(f"[快手小店上传] ⚠️ 重新查询失败，使用缓存第一个值: {prop_name} = {actual_value}({prop_value_id})")
                        return prop_value_id, actual_value

            # 如果所有方法都失败，跳过这个属性
            print(f"[快手小店上传] 无法获取属性值ID，跳过属性: {prop_name}")
            return None, None

        except Exception as e:
            print(f"[快手小店上传] 强制获取属性值ID失败: {e}")
            return 999999, prop_value

    def query_and_update_prop_values(self, category_id: int, prop_name: str, prop_id: int):
        """查询属性的可用值并更新配置文件"""
        try:
            if not self.kuaishou_api:
                print(f"[快手小店上传] 快手API未初始化，无法查询属性值")
                return

            print(f"[快手小店上传] 🔍 查询属性 '{prop_name}' (ID: {prop_id}) 的可用值...")

            # 直接查询属性值
            prop_values = self._query_prop_values(category_id, prop_id, prop_name)
            response = {
                'success': True if prop_values else False,
                'data': {
                    'propValues': prop_values or [],
                    'total': len(prop_values) if prop_values else 0
                }
            }

            if response.get('success'):
                data = response.get('data', {})
                prop_values = data.get('propValues', [])
                total = data.get('total', 0)

                print(f"[快手小店上传] ✅ 找到 {len(prop_values)} 个可用值 (总计: {total})")
                print(f"[快手小店上传] 📋 {prop_name} 的可用值:")

                for i, pv in enumerate(prop_values):
                    prop_value = pv.get('propValue', '')
                    prop_value_id = pv.get('propValueId', '')
                    print(f"  {i+1:2d}. {prop_value} (ID: {prop_value_id})")

                # 建议更新配置文件
                print(f"\n[快手小店上传] 💡 建议更新配置文件 '属性智能匹配.json' 中的 '{prop_name}' 配置:")
                print(f"可用的属性值: {[pv.get('propValue') for pv in prop_values[:10]]}")

            else:
                print(f"[快手小店上传] ❌ 查询属性值失败: {response}")

        except Exception as e:
            print(f"[快手小店上传] 查询属性值异常: {e}")

    def update_category_config_to_file(self, category_id: int):
        """查询类目配置并更新到配置文件"""
        try:
            if not self.kuaishou_api:
                print(f"[快手小店上传] 快手API未初始化")
                return False

            print(f"[快手小店上传] 🔍 开始查询类目 {category_id} 的完整配置...")

            # 1. 先查询类目的基础配置
            print(f"[快手小店上传] 📋 步骤1: 查询类目基础配置...")
            config_response = self.kuaishou_api.get_category_config(category_id)

            if not config_response.get('success'):
                print(f"[快手小店上传] ❌ 查询类目配置失败: {config_response}")
                return False

            config_data = config_response.get('data', {})
            attr_list = config_data.get('attrList', [])

            print(f"[快手小店上传] ✅ 找到 {len(attr_list)} 个属性")

            # 2. 查询每个必填属性的可用值
            print(f"[快手小店上传] 📋 步骤2: 查询必填属性的可用值...")

            updated_config = {
                "template_info": {
                    "name": "快手小店属性智能匹配配置",
                    "version": "2.0",
                    "description": f"类目{category_id}的属性配置，自动生成",
                    "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "category_id": category_id
                },
                "attribute_source_mapping": {},
                "global_attribute_mapping": {}
            }

            required_attrs = [attr for attr in attr_list if attr.get('required', False)]
            print(f"[快手小店上传] 🎯 找到 {len(required_attrs)} 个必填属性")

            for i, attr in enumerate(required_attrs):
                attr_name = attr.get('attrName', '')
                attr_id = attr.get('attrId', 0)

                print(f"[快手小店上传] 📝 {i+1}/{len(required_attrs)}: 查询 '{attr_name}' (ID: {attr_id}) 的可用值...")

                # 使用缓存方法查询属性的可用值
                cached_prop_values = self._query_prop_values(category_id, attr_id, attr_name)
                values_response = {
                    'success': True if cached_prop_values else False,
                    'data': {
                        'propValues': cached_prop_values or [],
                        'total': len(cached_prop_values) if cached_prop_values else 0
                    }
                }

                if values_response.get('success'):
                    values_data = values_response.get('data', {})
                    prop_values = values_data.get('propValues', [])

                    print(f"[快手小店上传] ✅ '{attr_name}' 有 {len(prop_values)} 个可用值")

                    # 构建属性配置
                    priority_mapping = {}
                    for pv in prop_values:
                        value_name = pv.get('propValue', '')
                        if value_name:
                            priority_mapping[value_name] = [value_name, "智能匹配"]

                    # 添加到配置
                    updated_config["global_attribute_mapping"][attr_name] = {
                        "description": f"快手小店{attr_name}属性 (ID: {attr_id})",
                        "fallback": list(priority_mapping.keys())[0] if priority_mapping else "其他",
                        "priority_mapping": priority_mapping
                    }

                    # 添加属性源映射（默认映射到同名属性）
                    updated_config["attribute_source_mapping"][attr_name] = [attr_name]

                else:
                    print(f"[快手小店上传] ❌ 查询 '{attr_name}' 的值失败")

            # 3. 写入配置文件
            config_file_path = os.path.join(os.path.dirname(__file__), '..', 'config', '属性智能匹配.json')
            config_file_path = os.path.abspath(config_file_path)

            print(f"[快手小店上传] 💾 步骤3: 写入配置文件 {config_file_path}")

            with open(config_file_path, 'w', encoding='utf-8') as f:
                json.dump(updated_config, f, ensure_ascii=False, indent=2)

            print(f"[快手小店上传] ✅ 配置文件更新完成！")
            print(f"[快手小店上传] 📁 文件位置: {config_file_path}")
            print(f"[快手小店上传] 🎯 包含 {len(updated_config['global_attribute_mapping'])} 个属性的完整配置")

            return True

        except Exception as e:
            print(f"[快手小店上传] 更新配置文件异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def search_category_prop_values(self, category_id: int, prop_id: int, prop_value: str = "", cursor: int = 0, limit: int = 500) -> Dict:
        """搜索类目属性值"""
        try:
            if not self.kuaishou_api:
                raise Exception("快手API未初始化")

            print(f"[快手小店上传] 搜索属性值: 类目ID={category_id}, 属性ID={prop_id}, 搜索值='{prop_value}'")

            # 直接调用KuaishouAPI的现有方法
            response = self.kuaishou_api.search_category_prop_values(
                category_id=category_id,
                prop_id=prop_id,
                prop_value=prop_value if prop_value else None,
                cursor=cursor,
                limit=limit
            )

            # 检查API调用是否成功（兼容不同的返回格式）
            # 官方文档：result=1表示成功，但实际可能返回success=True
            is_success = (response.get('result') == 1) or response.get('success', False)

            if is_success:
                data = response.get('data', {})
                prop_values = data.get('propValues', [])
                total = data.get('total', 0)
                cursor = data.get('cursor', 0)

                print(f"[快手小店上传] ✅ API调用成功！找到 {len(prop_values)} 个属性值 (总计: {total}, 游标: {cursor})")
                print(f"[快手小店上传] 📋 属性值列表:")
                for i, pv in enumerate(prop_values[:10]):  # 只显示前10个
                    print(f"  {i+1}. {pv.get('propValue')} (ID: {pv.get('propValueId')})")
                if len(prop_values) > 10:
                    print(f"  ... 还有 {len(prop_values) - 10} 个属性值")

                # 返回统一格式的响应（兼容现有代码）
                return {
                    'success': True,
                    'data': data,
                    'propValues': prop_values,
                    'total': total,
                    'cursor': cursor
                }
            else:
                # 打印详细的错误信息
                error_msg = response.get('error_msg', '未知错误')
                sub_msg = response.get('sub_msg', '')
                print(f"[快手小店上传] ❌ API调用失败:")
                print(f"  错误信息: {error_msg}")
                print(f"  子错误信息: {sub_msg}")
                print(f"  完整响应: {response}")
                return {'success': False, 'propValues': [], 'error_msg': error_msg}

        except Exception as e:
            print(f"[快手小店上传] 搜索属性值异常: {e}")
            return {}

    def _is_attribute_match(self, api_attr_name: str, target_attr_name: str) -> bool:
        """判断API返回的属性名称是否匹配目标属性名称"""
        # 只进行精确匹配，不做任何转换
        # 使用配置文件进行智能匹配，而不是硬编码映射
        return api_attr_name == target_attr_name

    def _is_safe_attribute_match(self, api_attr_name: str, target_attr_name: str) -> bool:
        """安全的属性匹配，避免材质属性之间的错误匹配"""

        # 材质属性需要精确匹配，避免混淆
        material_attrs = ['帮面材质', '鞋底材质', '鞋垫材质', '内里材质']

        # 如果目标属性是材质类属性，只允许精确匹配
        if target_attr_name in material_attrs:
            return api_attr_name == target_attr_name

        # 如果API属性是材质类属性，只允许精确匹配
        if api_attr_name in material_attrs:
            return api_attr_name == target_attr_name

        # 对于非材质属性，使用原来的智能匹配逻辑
        return self._is_attribute_match(api_attr_name, target_attr_name)

    def _get_required_item_props(self, category_id: int, product_data: Dict) -> List[Dict]:
        """根据类目ID和属性智能匹配配置获取必填的商品属性"""
        item_props = []

        try:
            # 获取商品标题和1688属性用于智能匹配
            title = product_data.get('title', '')
            # cache_data = product_data  # 使用整个product_data作为缓存数据

            print(f"[快手小店上传] 为类目({category_id})智能匹配属性，商品标题: {title}")

            # 通用属性处理逻辑 - 适用于所有类目ID
            print(f"[快手小店上传] 使用通用逻辑处理类目 {category_id} 的属性")

            # 直接返回空列表，让现有的动态属性处理逻辑来处理
            # 现有的 _build_item_prop_values 方法已经能够动态处理所有类目的属性

        except Exception as e:
            print(f"[快手小店上传] 生成商品属性失败: {e}")

        return item_props

    def _get_attribute_ids(self, category_id: int, attr_name: str, attr_value: str) -> tuple:
        """获取属性ID和属性值ID"""
        try:
            # 尝试调用快手API获取类目属性
            if hasattr(self, 'kuaishou_api') and self.kuaishou_api:
                # 调用快手API获取类目属性列表
                attrs_response = self.kuaishou_api.get_category_attributes(category_id)
                if attrs_response and attrs_response.get('result') == 1:
                    attrs_data = attrs_response.get('data', {})
                    attr_list = attrs_data.get('attrList', [])

                    # 查找匹配的属性
                    for attr in attr_list:
                        if attr.get('attrName') == attr_name:
                            prop_id = attr.get('attrId')

                            # 查找匹配的属性值
                            attr_values = attr.get('attrValueList', [])
                            for attr_val in attr_values:
                                if attr_val.get('attrValueName') == attr_value:
                                    prop_value_id = attr_val.get('attrValueId')
                                    print(f"[快手小店上传] 找到属性ID: {attr_name}({prop_id}) = {attr_value}({prop_value_id})")
                                    return prop_id, prop_value_id

                            # 如果没有找到匹配的属性值，使用第一个可用值
                            if attr_values:
                                first_value = attr_values[0]
                                prop_value_id = first_value.get('attrValueId')
                                actual_value = first_value.get('attrValueName')
                                print(f"[快手小店上传] 属性值不匹配，使用第一个可用值: {attr_name}({prop_id}) = {actual_value}({prop_value_id})")
                                return prop_id, prop_value_id

                            print(f"[快手小店上传] 找到属性但无可用值: {attr_name}({prop_id})")
                            return prop_id, None

                    print(f"[快手小店上传] 未找到属性: {attr_name}")
                else:
                    print(f"[快手小店上传] 获取类目属性失败: {attrs_response}")
            else:
                print(f"[快手小店上传] 快手API未初始化，无法获取属性ID")

        except Exception as e:
            print(f"[快手小店上传] 获取属性ID失败: {e}")

        # 返回None表示无法获取ID
        return None, None

    def _load_attribute_mapping_config(self) -> Dict:
        """加载属性智能匹配配置文件"""
        try:
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', '属性智能匹配.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                print(f"[快手小店上传] 属性配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            print(f"[快手小店上传] 加载属性配置文件失败: {e}")
            return {}

    def _smart_match_attribute(self, attr_config: Dict, attr_name: str, title: str, fallback: str) -> str:
        """智能匹配属性值"""
        try:
            global_mapping = attr_config.get('global_attribute_mapping', {})
            attr_mapping = global_mapping.get(attr_name, {})

            if not attr_mapping:
                return fallback

            # 获取配置的fallback值
            config_fallback = attr_mapping.get('fallback', fallback)

            # 🚀 新增：优先检查强制属性配置
            if 'force_attribute' in attr_mapping:
                force_config = attr_mapping['force_attribute']
                if force_config.get('enabled', False):
                    force_value = force_config.get('propValue', '')
                    print(f"[快手小店上传] 🔒 使用强制属性值: {attr_name} = {force_value}")
                    return force_value

            # 检查是否有固定值
            if 'fixed_value' in attr_mapping:
                return attr_mapping['fixed_value']

            # 🚀 新增：尝试从1688属性中获取值并进行映射
            if hasattr(self, 'current_product_attributes') and self.current_product_attributes:
                source_mapping = attr_config.get('attribute_source_mapping', {})
                source_attrs = source_mapping.get(attr_name, [])

                for source_attr in source_attrs:
                    for attr in self.current_product_attributes:
                        if attr.get('attributeName') == source_attr:
                            raw_value = attr.get('value', '').strip()
                            if raw_value:
                                print(f"[快手小店上传] 从1688属性获取: {source_attr} = {raw_value}")

                                # 使用优先级映射进行值转换
                                priority_mapping = attr_mapping.get('priority_mapping', {})
                                for target_value, keywords in priority_mapping.items():
                                    for keyword in keywords:
                                        if keyword == '智能匹配':
                                            # 智能匹配逻辑
                                            if raw_value.lower() in target_value.lower() or target_value.lower() in raw_value.lower():
                                                print(f"[快手小店上传] 智能匹配成功: {attr_name} = {target_value} (来源: {raw_value})")
                                                return target_value
                                        elif keyword.lower() in raw_value.lower():
                                            print(f"[快手小店上传] 关键词匹配成功: {attr_name} = {target_value} (关键词: {keyword})")
                                            return target_value

                                # 如果没有匹配到，直接返回原始值
                                print(f"[快手小店上传] 使用原始值: {attr_name} = {raw_value}")
                                return raw_value

            # 基于标题的匹配
            if attr_mapping.get('title_based', False):
                title_keywords = attr_mapping.get('title_keywords', {})
                for keyword, value in title_keywords.items():
                    if keyword in title:
                        print(f"[快手小店上传] 标题匹配 {attr_name}: '{keyword}' -> '{value}'")
                        return value

            # 优先级映射匹配
            priority_mapping = attr_mapping.get('priority_mapping', {})
            for value, keywords in priority_mapping.items():
                for keyword in keywords:
                    if keyword == '智能匹配':
                        # 基于标题的智能匹配
                        if any(k in title.lower() for k in [value.lower(), attr_name.lower()]):
                            print(f"[快手小店上传] 智能匹配 {attr_name}: '{value}'")
                            return value
                    elif keyword in title:
                        print(f"[快手小店上传] 关键词匹配 {attr_name}: '{keyword}' -> '{value}'")
                        return value

            print(f"[快手小店上传] 属性 {attr_name} 无匹配，使用默认值: '{config_fallback}'")
            return config_fallback

        except Exception as e:
            print(f"[快手小店上传] 智能匹配属性失败 {attr_name}: {e}")
            return fallback

    def _get_default_item_props(self, category_id: int) -> List[Dict]:
        """获取默认的商品属性 - 通用处理所有类目"""
        item_props = []

        # 不再硬编码特定类目，返回空列表让动态属性处理逻辑来处理
        print(f"[快手小店上传] 类目 {category_id} 使用动态属性处理，不使用硬编码默认属性")

        # 返回空列表，让动态属性处理逻辑来处理所有类目的属性
        return item_props



    def _convert_sku_attrs_to_kuaishou(self, sku_attrs: List[Dict]) -> List[Dict]:
        """转换1688的SKU属性为快手格式"""
        kuaishou_props = []

        for i, attr in enumerate(sku_attrs):
            # 1688的属性格式：{'attributeName': '颜色', 'attributeValue': '黑色', 'attributeID': 3216}
            attr_name = attr.get('attributeName', f'属性{i+1}')
            attr_value = attr.get('attributeValue', '')

            print(f"[快手小店上传] 处理SKU属性 {i+1}: {attr_name} = {attr_value}")

            prop = {
                'propName': attr_name,
                'propValueName': attr_value,
                'isMainProp': 1 if i == 0 else 0,  # 第一个属性设为主属性
                'propVersion': 1,
                'propSortNum': i + 1,  # 属性排序值
                'propValueSortNum': i + 1  # 属性值排序值
            }

            # 为主属性添加图片URL - 使用已上传的快手图片URL
            if i == 0:
                # 获取已上传的快手图片URL
                uploaded_sku_url = attr.get('uploadedSkuImageUrl', '')

                if uploaded_sku_url:
                    # 使用已上传到快手的SKU图片URL
                    prop['imageUrl'] = uploaded_sku_url
                    print(f"[快手小店上传] 使用已上传的SKU图片: {uploaded_sku_url}")
                elif hasattr(self, 'current_product_images') and self.current_product_images:
                    # 没有SKU图片或上传失败，使用主图
                    prop['imageUrl'] = self.current_product_images[0]
                    print(f"[快手小店上传] 使用主图作为SKU图片: {self.current_product_images[0]}")
                else:
                    print(f"[快手小店上传] 没有可用的图片URL")

            kuaishou_props.append(prop)
            print(f"[快手小店上传] 添加SKU属性: {attr_name} = {attr_value}")

        return kuaishou_props

    def _extract_field_value(self, cache_data: Dict, field_config: Dict):
        """从缓存数据中提取字段值"""
        try:
            source_field = field_config.get('source_field')
            if not source_field:
                return field_config.get('default', '')

            # 从缓存数据中获取值
            value = cache_data.get(source_field, '')

            # 如果值为空，尝试从product_info中获取
            if not value and 'product_info' in cache_data:
                product_info = cache_data['product_info']
                if isinstance(product_info, dict):
                    value = product_info.get(source_field, '')

            # 如果还是为空，使用默认值
            if not value:
                if 'default_generator' in field_config:
                    if field_config['default_generator'] == 'generate_rel_item_id':
                        import time
                        import random
                        value = f"KS_{int(time.time())}_{random.randint(1000, 9999)}"
                else:
                    value = field_config.get('default', '')

            # 应用过滤器
            filters = field_config.get('filters', [])
            for filter_name in filters:
                value = self._apply_filter(str(value), filter_name)

            # 检查最大长度
            max_length = field_config.get('max_length')
            if max_length and len(str(value)) > max_length:
                value = str(value)[:max_length]

            # 数据类型转换
            data_type = field_config.get('data_type')
            if data_type == 'integer':
                try:
                    value = int(value) if value else field_config.get('default', 0)
                except ValueError:
                    value = field_config.get('default', 0)

            return value

        except Exception as e:
            print(f"[快手小店上传] 提取字段值失败: {source_field}, 错误: {e}")
            return field_config.get('default', '')





    def _filter_description_images(self, description: str) -> str:
        """
        过滤商品详情描述中的广告图片

        过滤规则：
        1. 跳过table标签内的所有图片（通常是广告图片）
        2. 跳过带有超链接href属性的图片（通常是广告图片）

        Parameters:
        -----------
        description: str
            原始商品详情描述HTML

        Returns:
        --------
        str:
            过滤后的商品详情描述HTML
        """
        if not description:
            return description

        try:
            import re

            # 记录过滤前的图片数量
            original_img_count = len(re.findall(r'<img[^>]*>', description, re.IGNORECASE))

            # 1. 移除table标签内的所有内容（包括图片）
            # 使用非贪婪匹配，支持嵌套table
            table_pattern = r'<table[^>]*>.*?</table>'
            description = re.sub(table_pattern, '', description, flags=re.IGNORECASE | re.DOTALL)

            # 2. 移除带有href属性的img标签（通常包裹在a标签内）
            # 匹配 <a href="..."><img ...></a> 模式
            href_img_pattern = r'<a[^>]*href[^>]*>.*?<img[^>]*>.*?</a>'
            description = re.sub(href_img_pattern, '', description, flags=re.IGNORECASE | re.DOTALL)

            # 3. 移除单独的带有onclick等链接属性的img标签
            # 匹配包含onclick、href、data-href等链接属性的img标签
            link_img_pattern = r'<img[^>]*(?:onclick|href|data-href)[^>]*>'
            description = re.sub(link_img_pattern, '', description, flags=re.IGNORECASE)

            # 记录过滤后的图片数量
            filtered_img_count = len(re.findall(r'<img[^>]*>', description, re.IGNORECASE))

            if original_img_count != filtered_img_count:
                print(f"[微信小店上传] 图片过滤: 原始{original_img_count}张, 过滤后{filtered_img_count}张, 移除{original_img_count - filtered_img_count}张广告图片")

            return description

        except Exception as e:
            print(f"[微信小店上传] 过滤description图片时出错: {e}")
            return description


class WechatStoreUploadIntegration(AttributeQueryMixin):
    """微信小店上传功能集成类 - 用于与商品复制界面集成"""

    def __init__(self, product_table, shop_combo, copy_btn):
        """
        初始化集成类

        Args:
            product_table: 商品列表表格
            shop_combo: 店铺选择下拉框
            copy_btn: 复制按钮
        """
        self.product_table = product_table
        self.shop_combo = shop_combo
        self.copy_btn = copy_btn
        self.processor = KuaishouStoreUploadProcessor()

        # 连接复制按钮事件
        self.copy_btn.clicked.connect(self.on_copy_to_wechat_clicked)

    def _count_valid_products(self):
        """统计表格中有效的商品数量（检查序号列）"""
        try:
            total_rows = self.product_table.rowCount()
            valid_count = 0

            # 检查序号列（第0列），有序号就是有数据
            for row in range(total_rows):
                seq_item = self.product_table.item(row, 0)  # 序号列
                if seq_item and seq_item.text().strip():
                    valid_count += 1

            print(f"[微信小店上传] 表格总行数: {total_rows}, 有效商品数: {valid_count}")
            return valid_count

        except Exception as e:
            print(f"[微信小店上传] 统计有效商品数量异常: {e}")
            return 0

    def on_copy_to_wechat_clicked(self):
        """复制按钮点击事件 - 上传商品到快手小店"""
        try:
            from PyQt5.QtWidgets import QMessageBox

            print("[快手小店上传] 开始处理商品上传...")

            # 检查是否有选中的店铺
            current_shop = self._get_selected_shop()
            if not current_shop:
                QMessageBox.warning(None, "警告", "请先选择目标店铺")
                return

            print(f"[微信小店上传] 选中的店铺: {current_shop.get('店铺名称', 'Unknown')}")
            print(f"[微信小店上传] 店铺状态: {current_shop.get('状态', 'Unknown')}")
            print(f"[微信小店上传] ACCESS_TOKEN: {current_shop.get('accesstoken', 'None')[:20]}...")

            # 检查商品表格是否有有效数据
            valid_product_count = self._count_valid_products()
            if valid_product_count == 0:
                QMessageBox.warning(None, "警告", "商品列表为空，请先采集商品数据")
                return

            # 确认对话框
            reply = QMessageBox.question(
                None, "确认上传",
                f"确定要将商品列表中的商品上传到 {current_shop.get('店铺名称', 'Unknown')} 吗？\n\n"
                f"有效商品数量: {valid_product_count} 个",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            # 初始化快手API处理器
            try:
                # 从配置文件读取快手API配置
                config_file = "config/config.json"
                print(f"[快手小店上传] 🔍 检查配置文件: {config_file}")
                if os.path.exists(config_file):
                    print(f"[快手小店上传] ✅ 配置文件存在，开始读取...")
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    kuaishou_config = config_data.get('kuaishou_api_config', {})
                    self.processor.set_kuaishou_api(kuaishou_config)

                    # 🚀 读取图片过滤设置
                    print(f"[快手小店上传] 🔍 读取图片过滤设置...")
                    delete_image_config = config_data.get('delete_image', {})
                    print(f"[快手小店上传] 📋 原始配置: {delete_image_config}")

                    # 🔧 修复：无论配置是否为空都要应用设置
                    image_filter_settings = {
                        'main_image': {
                            'delete_before': delete_image_config.get('main_image', {}).get('delete_before', 0),
                            'delete_after': delete_image_config.get('main_image', {}).get('delete_after', 0)
                        },
                        'detail_image': {
                            'delete_before': delete_image_config.get('detail_image', {}).get('delete_before', 0),
                            'delete_after': delete_image_config.get('detail_image', {}).get('delete_after', 0)
                        }
                    }
                    print(f"[快手小店上传] 🔧 转换后设置: {image_filter_settings}")
                    self.processor.set_image_filter_settings(image_filter_settings)
                    print(f"[快手小店上传] ✅ 图片过滤设置已应用: 主图删除前{image_filter_settings['main_image']['delete_before']}张, 详情图删除前{image_filter_settings['detail_image']['delete_before']}张")
                else:
                    print(f"[快手小店上传] ❌ 配置文件不存在，使用默认初始化")
                    # 如果配置文件不存在，使用默认初始化
                    self.processor.set_kuaishou_api()
                print(f"[快手小店上传] 快手API初始化成功")
            except Exception as e:
                print(f"[快手小店上传] 快手API初始化失败: {e}")
                import traceback
                print(f"[快手小店上传] 异常详情: {traceback.format_exc()}")
                QMessageBox.critical(None, "错误", f"快手API初始化失败: {str(e)}")
                return

            # 禁用复制按钮，防止重复点击
            self.copy_btn.setEnabled(False)
            self.copy_btn.setText("上传中...")

            # 开始逐行处理商品
            self._process_products_to_wechat()

        except Exception as e:
            print(f"[微信小店上传] 处理异常: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "错误", f"上传处理异常: {str(e)}")
            # 恢复按钮状态
            self.copy_btn.setEnabled(True)
            self.copy_btn.setText("复制")

    def _process_products_to_wechat(self):
        """逐行处理商品并上传到快手小店"""
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox

            total_rows = self.product_table.rowCount()
            valid_product_count = self._count_valid_products()
            success_count = 0
            failed_count = 0
            processed_count = 0

            print(f"[微信小店上传] 开始处理 {valid_product_count} 个有效商品（总行数: {total_rows}）...")

            for row in range(total_rows):
                try:
                    # 检查序号列是否有内容，没有序号就跳过
                    seq_item = self.product_table.item(row, 0)  # 序号列
                    if not (seq_item and seq_item.text().strip()):
                        # 跳过空行
                        continue

                    processed_count += 1

                    # 更新状态显示
                    self.copy_btn.setText(f"上传中 {processed_count}/{valid_product_count}")
                    QApplication.processEvents()  # 更新界面

                    # 获取商品数据
                    product_data = self._get_product_data_from_row(row)
                    if not product_data:
                        print(f"[微信小店上传] 第 {row+1} 行商品数据无效，跳过")
                        failed_count += 1
                        continue

                    print(f"[微信小店上传] 处理第 {row+1} 行商品: {product_data.get('title', 'Unknown')}")

                    # 转换数据格式
                    try:
                        # 获取运费模板ID（从商品复制界面获取）
                        freight_template_id = 19219058228  # 从日志中看到的实际模板ID

                        # 重新从表格中解析类目ID，确保使用正确的值
                        category_item = self.product_table.item(row, 4)
                        actual_category_id = 10378  # 使用有效的叶子类目ID作为默认值
                        if category_item:
                            category_text = category_item.text().strip()
                            print(f"[快手小店上传] 第 {row+1} 行重新解析类目ID: '{category_text}'")
                            if category_text:
                                try:
                                    category_ids = [id.strip() for id in category_text.split(',') if id.strip()]
                                    if category_ids:
                                        actual_category_id = int(category_ids[-1])  # 使用最后一个类目ID
                                        print(f"[快手小店上传] 第 {row+1} 行解析到的最终类目ID: {actual_category_id}")
                                except Exception as e:
                                    print(f"[快手小店上传] 第 {row+1} 行解析类目ID失败: {e}")
                                    actual_category_id = 10378
                            else:
                                print(f"[快手小店上传] 第 {row+1} 行类目ID列为空，使用默认值: {actual_category_id}")
                        else:
                            print(f"[快手小店上传] 第 {row+1} 行类目ID列不存在，使用默认值: {actual_category_id}")

                        converted_data = self.processor.process_product_data(
                            cache_data=product_data,
                            category_id=actual_category_id,  # 使用表格中的实际类目ID
                            freight_template_id=freight_template_id,
                            row=row  # 🚀 传递row参数用于缓存主类目名称
                        )
                    except Exception as convert_error:
                        print(f"[快手小店上传] 第 {row+1} 行商品数据转换失败: {convert_error}")
                        raise convert_error

                    # 上传到快手小店
                    upload_result = self.processor.upload_product_to_kuaishou(converted_data)

                    if upload_result.get('success'):
                        print(f"[快手小店上传] 第 {row+1} 行商品上传成功")
                        success_count += 1

                        # 获取快手商品ID并显示在表格中
                        kuaishou_item_id = upload_result.get('product_id')

                        # 如果没有从product_id获取到，尝试从data.kwaiItemId获取
                        if not kuaishou_item_id:
                            data = upload_result.get('data', {})
                            kuaishou_item_id = data.get('kwaiItemId')

                        if kuaishou_item_id:
                            self._update_product_id_in_table(row, str(kuaishou_item_id))
                            print(f"[快手小店上传] 第 {row+1} 行商品ID: {kuaishou_item_id}")

                        self._update_row_upload_status(row, "上传成功", "#4CAF50")
                    else:
                        error_msg = upload_result.get('message', '未知错误')
                        print(f"[快手小店上传] 第 {row+1} 行商品上传失败: {error_msg}")
                        failed_count += 1
                        self._update_row_upload_status(row, f"上传失败: {error_msg}", "#F44336")

                except Exception as e:
                    print(f"[快手小店上传] 处理第 {row+1} 行商品异常: {e}")
                    failed_count += 1
                    self._update_row_upload_status(row, f"处理异常: {str(e)}", "#FF9800")

            # 显示完成结果
            QMessageBox.information(
                None, "上传完成",
                f"商品上传完成！\n\n"
                f"有效商品: {valid_product_count} 个\n"
                f"成功: {success_count}\n"
                f"失败: {failed_count}"
            )

            print(f"[微信小店上传] 上传完成 - 有效商品: {valid_product_count}, 成功: {success_count}, 失败: {failed_count}")

        except Exception as e:
            print(f"[微信小店上传] 批量处理异常: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "错误", f"批量处理异常: {str(e)}")

        finally:
            # 恢复按钮状态
            self.copy_btn.setEnabled(True)
            self.copy_btn.setText("复制")

    def _get_selected_shop(self):
        """获取当前选中的店铺信息"""
        try:
            # 获取当前选中的店铺名称
            current_text = self.shop_combo.currentText().strip()
            print(f"[微信小店上传] 当前选中的店铺文本: '{current_text}'")

            # 检查是否是有效的店铺选择
            if not current_text or current_text in ["全部店铺", "正在加载店铺列表...", "--无法获取店铺列表--"] or current_text.startswith("--"):
                print(f"[微信小店上传] 无效的店铺选择: '{current_text}'")
                return None

            # 从配置文件中查找匹配的店铺信息
            config_file = "config/账号列表.json"
            if not os.path.exists(config_file):
                print(f"[微信小店上传] 配置文件不存在: {config_file}")
                return None

            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    shops = json.load(f)

                print(f"[微信小店上传] 从配置文件加载了 {len(shops)} 个店铺")

                # 精确匹配店铺名称
                for shop in shops:
                    shop_name = shop.get('店铺名称', '').strip()
                    if shop_name == current_text:
                        print(f"[微信小店上传] 精确匹配到店铺: {shop_name}")
                        print(f"[微信小店上传] 店铺状态: {shop.get('状态', 'Unknown')}")
                        print(f"[微信小店上传] AppID: {shop.get('appid', 'Unknown')}")

                        # 验证必要字段
                        access_token = shop.get('accesstoken', '')
                        if access_token:
                            print(f"[微信小店上传] ACCESS_TOKEN: {access_token[:20]}...{access_token[-10:]}")
                        else:
                            print(f"[微信小店上传] ⚠️ ACCESS_TOKEN为空")

                        return shop

                # 如果精确匹配失败，尝试模糊匹配
                print(f"[微信小店上传] 精确匹配失败，尝试模糊匹配...")
                for shop in shops:
                    shop_name = shop.get('店铺名称', '').strip()
                    if shop_name and (shop_name in current_text or current_text in shop_name):
                        print(f"[微信小店上传] 模糊匹配到店铺: {shop_name}")
                        return shop

                # 如果都没有匹配到，提供可选的店铺列表
                print(f"[微信小店上传] 未找到匹配的店铺")
                print(f"[微信小店上传] 可用的店铺列表:")
                for i, shop in enumerate(shops[:5]):  # 只显示前5个
                    print(f"  {i+1}. {shop.get('店铺名称', 'Unknown')} ({shop.get('状态', 'Unknown')})")

                return None

            except Exception as e:
                print(f"[微信小店上传] 读取配置文件失败: {e}")
                return None

        except Exception as e:
            print(f"[微信小店上传] 获取选中店铺异常: {e}")
            return None

    def _get_product_data_from_row(self, row):
        """从表格行获取商品数据"""
        try:
            # 优先从原链接列（第7列）提取商品ID
            link_item = self.product_table.item(row, 7)  # 原链接在第7列
            product_id = None

            if link_item:
                link = link_item.text().strip()
                if link:
                    # 从1688链接中提取商品ID
                    import re
                    patterns = [
                        r'offer/(\d+)\.html',  # https://detail.1688.com/offer/785500552359.html
                        r'offerId=(\d+)',      # 其他格式的链接
                        r'/(\d+)\.html'        # 通用数字ID格式
                    ]

                    for pattern in patterns:
                        match = re.search(pattern, link)
                        if match:
                            product_id = match.group(1)
                            print(f"[微信小店上传] 第 {row+1} 行从原链接提取商品ID: {product_id}")
                            break

            # 如果从链接提取失败，尝试从商品ID列（第8列）获取
            if not product_id:
                product_id_item = self.product_table.item(row, 8)  # 商品ID在第8列
                if product_id_item:
                    product_id = product_id_item.text().strip()
                    if product_id:
                        print(f"[微信小店上传] 第 {row+1} 行从商品ID列获取: {product_id}")

            if not product_id:
                print(f"[微信小店上传] 第 {row+1} 行无法获取商品ID")
                return None

            # 从表格获取标题（第2列）
            title_item = self.product_table.item(row, 2)
            title = title_item.text() if title_item else "Unknown"
            print(f"[微信小店上传] 第 {row+1} 行标题: {title}")

            # 从表格获取类目ID（第4列）
            category_item = self.product_table.item(row, 4)
            category_id = None  # 不设置默认值，强制从表格解析
            if category_item:
                category_text = category_item.text().strip()
                print(f"[微信小店上传] 第 {row+1} 行类目ID原始文本: '{category_text}'")
                if category_text:
                    try:
                        # 解析类目ID，可能是逗号分隔的多个ID：1713,10377,10378
                        category_ids = [id.strip() for id in category_text.split(',') if id.strip()]
                        if category_ids:
                            category_id = int(category_ids[-1])  # 使用最后一个类目ID（子类目）
                            print(f"[微信小店上传] 第 {row+1} 行解析成功，类目ID: {category_id} (从 {category_text} 中获取最后一个)")
                        else:
                            print(f"[微信小店上传] 第 {row+1} 行类目ID解析为空列表")
                    except Exception as e:
                        print(f"[微信小店上传] 第 {row+1} 行解析类目ID失败: {e}")
                else:
                    print(f"[微信小店上传] 第 {row+1} 行类目ID文本为空")
            else:
                print(f"[微信小店上传] 第 {row+1} 行类目ID列不存在")

            # 如果没有解析到类目ID，使用默认值
            if category_id is None:
                category_id = 10378  # 使用一个有效的叶子类目ID作为默认值
                print(f"[微信小店上传] 第 {row+1} 行未能解析类目ID，使用默认值: {category_id}")

            # 尝试从缓存文件读取完整数据
            cache_file_path = f"config/pdcache/{product_id}.json"
            if os.path.exists(cache_file_path):
                try:
                    with open(cache_file_path, 'r', encoding='utf-8') as f:
                        cache_data = json.load(f)
                    print(f"[微信小店上传] 成功从缓存读取商品数据: {product_id}")

                    # 使用表格中的标题和类目ID覆盖缓存数据
                    cache_data['title'] = title
                    cache_data['category_id'] = category_id

                    return cache_data
                except Exception as e:
                    print(f"[微信小店上传] 读取缓存文件失败 {cache_file_path}: {e}")

            # 如果缓存文件不存在，从表格数据构建基础商品数据
            print(f"[微信小店上传] 缓存文件不存在，从表格构建数据: {product_id}")

            # 获取价格（第5列）
            price = 0
            market_price = 0
            price_item = self.product_table.item(row, 5)
            if price_item:
                price_text = price_item.text()
                try:
                    # 解析价格范围，如 "8.6~17.5"
                    if '~' in price_text:
                        prices = price_text.split('~')
                        if len(prices) >= 2:
                            price = float(prices[0])
                            market_price = float(prices[1])
                    else:
                        price = float(price_text)
                        market_price = price
                except:
                    price = 0
                    market_price = 0

            # 构建基础商品数据
            product_data = {
                'title': title,
                'category_id': category_id,
                'description': f'商品ID: {product_id}',
                'price': price,
                'market_price': market_price,
                'stock': 999,
                'product_image': [],  # 主图
                'product_sku_infos': [{
                    'sku_id': f'{product_id}_default',
                    'price': price,
                    'market_price': market_price,
                    'stock': 999,
                    'sku_code': '',
                    'barcode': '',
                    'attributes': []
                }],
                'product_attribute': [],  # 属性
                'original_link': link_item.text() if link_item else "",
                'product_id': product_id
            }

            return product_data

        except Exception as e:
            print(f"[微信小店上传] 获取第 {row+1} 行商品数据异常: {e}")
            return None

    def _update_product_id_in_table(self, row, product_id):
        """更新表格中的商品ID"""
        try:
            from PyQt5.QtWidgets import QTableWidgetItem

            # 商品ID在第8列
            product_id_item = QTableWidgetItem(str(product_id))
            self.product_table.setItem(row, 8, product_id_item)
            print(f"[快手小店上传] 第 {row+1} 行商品ID已更新: {product_id}")

        except Exception as e:
            print(f"[快手小店上传] 更新第 {row+1} 行商品ID异常: {e}")

    def _update_row_upload_status(self, row, status, color):
        """更新表格行的上传状态"""
        try:
            from PyQt5.QtWidgets import QTableWidgetItem
            from PyQt5.QtGui import QColor

            # 在最后一列显示上传状态
            col_count = self.product_table.columnCount()

            # 如果没有状态列，添加一列
            if col_count < 14:  # 假设状态列是第14列
                self.product_table.setColumnCount(col_count + 1)
                self.product_table.setHorizontalHeaderItem(col_count, QTableWidgetItem("上传状态"))

            # 设置状态文本和颜色
            status_item = QTableWidgetItem(status)
            status_item.setBackground(QColor(color))
            self.product_table.setItem(row, col_count, status_item)

        except Exception as e:
            print(f"[微信小店上传] 更新第 {row+1} 行状态异常: {e}")


def integrate_wechat_upload(product_table, shop_combo, copy_btn):
    """
    集成微信小店上传功能到现有界面

    Args:
        product_table: 商品列表表格
        shop_combo: 店铺选择下拉框
        copy_btn: 复制按钮

    Returns:
        WechatStoreUploadIntegration: 集成实例
    """
    return WechatStoreUploadIntegration(product_table, shop_combo, copy_btn)





